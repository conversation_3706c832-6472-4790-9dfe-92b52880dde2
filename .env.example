# ========================================================================== #
# ROLES & CHANNELS CONFIGURATION                                             #
# ========================================================================== #
# Role IDs
ADMIN_ROLE_ID=1367889949554376816
HR_ROLE_ID=1367900918183366656
VERIFIED_ROLE_ID=1337809000431091764
NEW_MEMBER_PINGS_ID=1364552856945954877

# Channel IDs
WELCOME_CHANNEL_ID=1309265274352042047
ADMIN_LOG_CHANNEL_ID=1367902119205802087
ERROR_LOG_CHANNEL_ID=1367902055145934959
GENERAL_CHANNEL_ID=1309285208964726795

# ========================================================================== #
# APPLICATION CONFIGURATION                                                  #
# ========================================================================== #
APP_NAME=dis-bot                    # Project Application name
APP_ENV=development                 # development, testing, staging, production
VERSION=1.0.0                       # Use package.json version

APP_TIMEZONE=UTC+01:00              # https://www.timeanddate.com/time/map/

# ========================================================================== #
# DISCORD CONFIGURATION                                                      #
# ========================================================================== #
TOKEN=your_discord_bot_token
APPLICATION_ID=your_discord_application_id
PUBLIC_KEY=your_discord_public_key
CLIENT_ID=your_discord_client_id
CLIENT_SECRET=your_discord_client_secret

# ========================================================================== #
# DATABASE CONFIGURATION                                                     #
# ========================================================================== #
DB_CONNECTION=mysql                 # mysql, postgresql, sqlite (usually mysql)
DB_USERNAME=your_mysql_username
DB_DATABASE=your_mysql_name
DB_PASSWORD=your_mysql_password
DB_HOST=your_mysql_hostname
DB_PORT=your_mysql_port
DB_CHARSET=character_set_name       # Preserved for future use
DB_COLLATION=collation_name         # Preserved for future use

# ========================================================================== #
# AUTHENTICATION & SECURITY                                                  #
# ========================================================================== #

# Encryption Keys
USER_DATA_ENCRYPTION_KEY=your_user_data_encryption_key

# ========================================================================== #
# EXTERNAL SERVICES & APIs                                                   #
# ========================================================================== #
CIRRUS_API_KEY=your_cirrus_api_key
CIRRUS_LOG_API_KEY=your_cirrus_log_api_key
ROBLOX_ASSETS_API_KEY=your_roblox_assets_api_key
ROBLOX_CLIENT_ID=your_roblox_client_id
ROBLOX_CLIENT_SECRET=your_roblox_client_secret

# ========================================================================== #
# MONITORING & LOGGING                                                       #
# ========================================================================== #

# Health Check System
HEALTH_CHECK_INTERVAL=300000
HEALTH_PORT=3000
ENABLE_HEALTH_ENDPOINT=true

# ========================================================================== #
# CI/CD & DEPLOYMENT                                                         #
# ========================================================================== #
DEPLOY_SERVER=<EMAIL>        # Preserved for future use
DEPLOY_PATH=/var/www/html                 # Preserved for future use
DEPLOY_SCRIPT=./scripts/deploy.sh         # Preserved for future use

# GitHub Actions
GITHUB_TOKEN=your_github_token            # For semantic-release

# Docker
DOCKER_REGISTRY=your-registry.com         # Preserved for future use
DOCKER_USERNAME=your_docker_username      # Preserved for future use
DOCKER_PASSWORD=your_docker_password      # Preserved for future use
