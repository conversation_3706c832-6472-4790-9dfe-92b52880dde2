/**
 * @file ONBOARDING SERVICE.JS
 *
 * @version 3.0.2
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Handles the user onboarding flow with proper data sequencing and validation.
 * Manages onboarding sessions, step prompts, validation, and user record updates.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

import { EmbedBuilder } from 'discord.js';

import { botConfig } from '../config/config.js';
import { messages } from '../config/messages.js';
import { User } from '../models/User.js';
import db from '../utils/Database.js';
import logger from '../utils/logger.js';
import {
  isValidEmail,
  isValidRobloxUsername,
  parseYesNo,
  sanitizeInput,
  sanitizeNickname,
  sanitizeTimeZone,
} from '../utils/validators.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').GuildMember} GuildMember
 * @typedef {import('discord.js').User} DiscordUser
 * @typedef {import('discord.js').Message} DiscordMessage
 * @typedef {Object} OnboardingSession
 * @property {string} userId - Discord user ID
 * @property {number} stepIndex - Current step index in onboarding flow
 * @property {Record<string, any>} data - Collected onboarding data
 * @property {number} startTime - Session start timestamp (ms)
 * @property {number} lastActivity - Last activity timestamp (ms)
 * @property {boolean} dmConfirmed - Whether DM was confirmed
 * @property {Array<Object>} flow - The onboarding flow steps
 */

// ------------ CONFIGURATION
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const ONBOARDING_FLOW_PATH = path.join(
  __dirname,
  '../models/onboardingFlow.json'
);

// ------------ CONSTANTS
const MAX_SESSION_AGE =
  typeof botConfig.onboarding.maxSessionAge === 'string'
    ? parseInt(botConfig.onboarding.maxSessionAge)
    : botConfig.onboarding.maxSessionAge;

// ------------ SESSION STORE
const sessions = new Map();
// ^ - userId -> session

// ------------ HELPERS

/**
 * Fetches the onboarding flow steps from a local JSON file.
 * @returns {Promise<Array<Object>>} Array of onboarding step objects
 */
async function fetchOnboardingFlow() {
  try {
    // Read from local file
    const data = await fs.readFile(ONBOARDING_FLOW_PATH, 'utf-8');
    return JSON.parse(data);
  } catch (err) {
    logger.error('Failed to fetch onboarding flow JSON:', err);
    throw err;
  }
}

/**
 * Builds a Discord embed for a given onboarding step and session data.
 * Ensures consistent title, color, footer, and timestamp for all onboarding embeds.
 * @param {Object} step - The onboarding step definition
 * @param {Record<string, any>} sessionData - Collected session data for placeholder replacement
 * @returns {import('discord.js').EmbedBuilder}
 */
function buildEmbedFromStep(step, sessionData) {
  if (!step || typeof step !== 'object') {
    // Defensive: fallback embed if step is missing or malformed
    return new EmbedBuilder()
      .setTitle('Dynamic Innovative Studio APP')
      .setColor('#ccf3ff')
      .setDescription(
        'An error occurred: onboarding step is missing or invalid.'
      )
      .setFooter({ text: 'Please contact support or try again.' })
      .setTimestamp();
  }
  const embed = new EmbedBuilder();

  // Consistent style for all onboarding embeds
  const baseTitle = step.embed?.title || 'Dynamic Innovative Studio APP';
  const baseColor = step.embed?.color || '#ccf3ff';
  const baseFooter =
    step.embed?.footer || 'Please respond to this message to continue';

  embed.setTitle(baseTitle);
  embed.setColor(baseColor);
  embed.setFooter({ text: baseFooter });
  embed.setTimestamp();

  let prompt = step.prompt || '';
  if (sessionData) {
    for (const [k, v] of Object.entries(sessionData)) {
      prompt = prompt.replace(new RegExp(`{${k}}`, 'g'), v ?? '');
    }
  }
  if (prompt) embed.setDescription(prompt);
  return embed;
}

/**
 * Sanitizes a field value based on its type.
 * @param {string} field - Field name
 * @param {any} value - Value to sanitize
 * @returns {string}
 */
function sanitizeField(field, value) {
  switch (field) {
    case 'email':
      return sanitizeInput(value).toLowerCase();
    case 'nickname':
      return sanitizeNickname(value);
    case 'robloxUsername':
      return isValidRobloxUsername(value) ? value : '';
    case 'timeZone':
      return sanitizeTimeZone(value);
    default:
      return sanitizeInput(value);
  }
}

/**
 * Validates a field value based on its validation type.
 * @param {string|null} validation - Validation type
 * @param {any} value - Value to validate
 * @returns {boolean}
 */
function validateField(validation, value) {
  switch (validation) {
    case 'email':
      return isValidEmail(value);
    case 'nickname':
      return !!sanitizeNickname(value);
    case 'robloxUsername':
      return value === 'skip' || isValidRobloxUsername(value);
    case 'timezone':
      return !!sanitizeTimeZone(value);
    case 'yesno':
      return typeof parseYesNo(value) === 'object' && parseYesNo(value).isValid;
    default:
      return true;
  }
}

/**
 * Gets the appropriate error message for a validation type.
 * @param {string|null} validation - Validation type
 * @returns {string}
 */
function getValidationErrorMessage(validation) {
  switch (validation) {
    case 'email':
      return messages.errors.invalidEmail;
    case 'nickname':
      return messages.errors.invalidNickname;
    case 'robloxUsername':
      return messages.errors.invalidRobloxUsername;
    case 'timezone':
      return messages.errors.invalidTimezone;
    case 'yesno':
      return messages.errors.invalidYesNo;
    default:
      return messages.errors.internalError;
  }
}

/**
 * Parses a field value based on its validation type.
 * @param {string|null} validation - Validation type
 * @param {any} value - Value to parse
 * @returns {any}
 */
function parseField(validation, value) {
  if (validation === 'yesno') return parseYesNo(value);
  return value;
}

/**
 * Gets the onboarding session for a user.
 * @param {string} userId
 * @returns {OnboardingSession|undefined}
 */
function getSession(userId) {
  return sessions.get(userId);
}

/**
 * Sets the onboarding session for a user.
 * @param {string} userId
 * @param {OnboardingSession} session
 */
function setSession(userId, session) {
  sessions.set(userId, session);
}

/**
 * Clears the onboarding session for a user.
 * @param {string} userId
 */
function clearSession(userId) {
  sessions.delete(userId);
}

// ------------ ONBOARDING SERVICE CLASS
/**
 * Service for managing the onboarding flow for Discord users.
 */
class OnboardingService {
  /**
   * Sends the current onboarding step as an embed to the user.
   * @param {DiscordUser} user
   * @param {OnboardingSession} session
   * @returns {Promise<void>}
   */
  async sendCurrentStep(user, session) {
    const step = session.flow[session.stepIndex];
    if (!step) {
      try {
        await user.send({
          embeds: [
            new EmbedBuilder()
              .setColor(parseInt(botConfig.colors.error.replace(/^#/, ''), 16))
              .setTitle('Onboarding Error')
              .setDescription(
                'An error occurred: onboarding step is missing. Please contact support or restart the onboarding process.'
              )
              .setTimestamp(),
          ],
        });
      } catch {
        // TODO: LOG DM ERRORS BUT KEEP GOING
      }
      return;
    }
    const embed = buildEmbedFromStep(step, session.data);
    try {
      await user.send({ embeds: [embed] });
    } catch {
      // TODO: LOG DM ERRORS BUT KEEP GOING
    }
  }

  /**
   * Starts the onboarding process for a member.
   * @param {GuildMember} member
   * @returns {Promise<void>}
   */
  async startOnboarding(member) {
    const userId = member.id;
    const userTag = member.user.tag;
    let dm;
    try {
      dm = await member.createDM();
    } catch (err) {
      // DM creation failed (likely DMs are closed)
      if (err.code === 50007) {
        logger.warn(
          {
            event: 'onboarding_dm_failed',
            user: userId,
            username: userTag,
            guild: member.guild?.id,
            stack: err.stack,
          },
          `Cannot send onboarding DM to user ${userTag} (${userId}): DMs are closed`
        );
        // Try to notify in server if possible
        try {
          await member.send(messages.errors.dmFailed);
        } catch {
          // If still fails, try to reply in a public channel if available
          const generalChannel = member.guild?.channels.cache.get(
            botConfig.channels.general
          );
          if (
            generalChannel &&
            'send' in generalChannel &&
            typeof generalChannel.send === 'function'
          ) {
            await generalChannel.send(
              `<@${userId}> ${messages.errors.dmFailed}`
            );
          }
        }
      } else {
        logger.error(
          {
            event: 'onboarding_dm_failed',
            user: userId,
            username: userTag,
            guild: member.guild?.id,
            stack: err.stack,
          },
          `Failed to create DM for onboarding: ${err.message}`
        );
      }
      return;
    }

    // Check if already in session
    if (getSession(userId)) {
      try {
        await dm.send(messages.onboarding.cancellation);
      } catch (err) {
        // Ignore DM send failures - user may have DMs disabled
        logger.debug('Failed to send cancellation message:', err);
      }
      return;
    }

    // Fetch onboarding flow
    let flow;
    try {
      flow = await fetchOnboardingFlow();
    } catch (err) {
      logger.error(
        `Failed to fetch onboarding flow for user ${userTag} (${userId}): ${err}`
      );
      try {
        await dm.send(messages.errors.internalError);
      } catch (err) {
        // Ignore DM send failures - user may have DMs disabled
        logger.debug('Failed to send error message:', err);
      }
      return;
    }

    const session = {
      userId,
      stepIndex: 0,
      data: {},
      startTime: Date.now(),
      lastActivity: Date.now(),
      dmConfirmed: true,
      flow,
    };
    setSession(userId, session);

    // Notify user (not embed) - clear onboarding start message
    try {
      await dm.send(
        '🚀 **Starting verification process for Dynamic Innovative Studio...**'
      );
    } catch (err) {
      // Ignore DM send failures - user may have DMs disabled
      logger.debug('Failed to send start message:', err);
    }

    // Save onboarding start time/status in DB
    await db.updateUserOnboardingStatus(userId, {
      onboardingStatus: 'in_progress',
      onboardingStartTime: new Date(),
      lastInteraction: new Date(),
    });

    // Send first step
    await this.sendCurrentStep(member.user, session);
  }

  /**
   * Processes a user's response during onboarding.
   * @param {DiscordMessage} message
   * @returns {Promise<void>}
   */
  async processResponse(message) {
    const userId = message.author.id;
    const userTag = message.author.tag;
    const session = getSession(userId);

    if (!session) return;

    const flow = session.flow;
    const step = flow[session.stepIndex];
    const content = message.content?.trim();

    // Debug log before setting field value
    logger.debug(
      {
        userId,
        stepIndex: session.stepIndex,
        stepType: step.confirmation ? 'confirmation' : 'collection',
        field: step.field,
        content: content,
        currentData: { ...session.data },
      },
      'Processing field input'
    );

    // Timeout check
    if (Date.now() - session.startTime > MAX_SESSION_AGE) {
      await message.author.send(messages.onboarding.timeout);
      clearSession(userId);
      await db.updateUserOnboardingStatus(userId, {
        onboardingStatus: 'failed',
        lastInteraction: new Date(),
      });
      return;
    }

    // Validation
    if (!validateField(step.validation, content)) {
      await message.author.send({
        embeds: [
          new EmbedBuilder()
            .setColor(parseInt(botConfig.colors.error.replace(/^#/, ''), 16))
            .setDescription(getValidationErrorMessage(step.validation)),
        ],
      });
      await this.sendCurrentStep(message.author, session);
      return;
    }

    // Parse/transform value (validation only)
    parseField(step.validation, content);

    // Store the value if this is a data collection step (not confirmation)
    if (step.field && !step.confirmation) {
      const sanitizedValue = sanitizeField(step.field, content);
      session.data[step.field] = sanitizedValue;

      // Debug log after storing value
      logger.debug(
        {
          userId,
          field: step.field,
          rawValue: content,
          sanitizedValue,
          sessionData: { ...session.data },
        },
        `Stored value for field: ${step.field}`
      );
    }

    // Confirmation logic
    if (step.confirmation && step.validation === 'yesno') {
      const confirmResult = parseYesNo(content);
      if (!confirmResult.isValid || confirmResult.value !== true) {
        // Go back to onRejectStep if defined
        if (typeof step.onRejectStep === 'number') {
          session.stepIndex = step.onRejectStep;
          await this.sendCurrentStep(message.author, session);
          return;
        }
      }
    }

    // Special skip logic for robloxUsername
    if (step.field === 'robloxUsername' && content.toLowerCase() === 'skip') {
      session.data[step.field] = '';
      session.stepIndex =
        typeof step.skipStep === 'number'
          ? step.skipStep
          : session.stepIndex + 1;
      // Check if next step exists
      if (session.stepIndex >= flow.length) {
        await this._completeOnboarding(message, session, userId, userTag);
        return;
      }
      await this.sendCurrentStep(message.author, session);
      return;
    }

    // Advance to next step
    session.stepIndex =
      typeof step.nextStep === 'number' ? step.nextStep : session.stepIndex + 1;
    session.lastActivity = Date.now();

    // Save progress in DB
    await db.updateUserOnboardingStatus(userId, {
      onboardingStatus: 'in_progress',
      lastInteraction: new Date(),
    });

    // Check if next step exists
    if (session.stepIndex >= flow.length) {
      await this._completeOnboarding(message, session, userId, userTag);
      return;
    }

    // Continue to next step
    await this.sendCurrentStep(message.author, session);
  }

  /**
   * Handles onboarding completion: saves user, sends messages, clears session.
   * @private
   */
  async _completeOnboarding(message, session, userId, userTag) {
    // Data integrity check for required fields
    if (
      !session.data.email ||
      !session.data.nickname ||
      !session.data.timeZone
    ) {
      logger.error('Onboarding failed: missing required fields', {
        userId,
        userTag,
        sessionData: { ...session.data },
      });
      await message.author.send({
        embeds: [
          new EmbedBuilder()
            .setColor(parseInt(botConfig.colors.error.replace(/^#/, ''), 16))
            .setDescription(
              'Some required information is missing. Please restart the onboarding process.'
            ),
        ],
      });
      clearSession(userId);
      return;
    }

    // Debug: Confirm email is present before creating User
    logger.debug('[onboardingService] session.data.email:', session.data.email);

    // Create a proper User instance to handle encryption
    const now = new Date();
    const user = new User({
      id: userId,
      tag: userTag,
      email: session.data.email,
      nickname: session.data.nickname,
      robloxUsername: session.data.robloxUsername || null,
      timeZone: session.data.timeZone,
      verified: true,
      verifiedAt: now,
      onboardingStartTime: session.startTime
        ? new Date(session.startTime)
        : now,
      metadata: {},
      onboardingStatus: 'completed',
      lastInteraction: now,
    });

    try {
      // User.toObject() will handle encryption
      await db.saveUser(user);
      logger.debug(
        {
          userId,
          email: session.data.email,
          success: true,
        },
        'User data saved successfully'
      );
    } catch (error) {
      logger.error(`Failed to save user data: ${error.message}`, {
        userId,
        error: error.stack,
      });
      await message.author.send({
        embeds: [
          new EmbedBuilder()
            .setColor(parseInt(botConfig.colors.error.replace(/^#/, ''), 16))
            .setDescription(
              'We encountered an issue saving your information. Please try again or contact support.'
            ),
        ],
      });
      return;
    }

    clearSession(userId);

    await message.author.send({
      embeds: [
        new EmbedBuilder()
          .setColor(parseInt(botConfig.colors.success.replace(/^#/, ''), 16))
          .setDescription(messages.onboarding.onboardingComplete),
      ],
    });

    const client = message.client;
    const welcomeChannelId = botConfig.channels.welcome;
    const welcomeChannel = client.channels.cache.get(welcomeChannelId);
    if (
      welcomeChannel &&
      'send' in welcomeChannel &&
      typeof welcomeChannel.send === 'function'
    ) {
      await welcomeChannel.send(
        messages.onboarding.welcome.replace('{user}', `<@${userId}>`)
      );
    }

    try {
      // Try to find the guild and member
      for (const guild of client.guilds.cache.values()) {
        const member = await guild.members.fetch(userId).catch(() => null);
        if (member) {
          const verifiedRoleId = botConfig.roles.verified;
          const role = guild.roles.cache.get(verifiedRoleId);
          if (role && !member.roles.cache.has(verifiedRoleId)) {
            await member.roles.add(role, 'Onboarding complete');
          }
        }
      }
    } catch (err) {
      logger.warn(
        {
          event: 'onboarding_role_add_failed',
          user: userId,
          username: userTag,
          stack: err?.stack,
        },
        'Failed to add verified role after onboarding'
      );
    }
  }

  /**
   * Graceful shutdown for onboarding service.
   * Clears all onboarding sessions and releases resources.
   */
  shutdown() {
    sessions.clear();
  }
}

// ------------ EXPORT
export default new OnboardingService();
