/**
 * @file MESSAGE SERVICE.JS
 *
 * @version 2.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Service for processing, sending, and retrieving messages.
 * Handles sending messages to Discord users/channels and fetching message history.
 * Not to be confused and add save message to database.
 * This service can be used for message.js & viewmessages.js commands.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import logger from '../utils/logger.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').User | import('discord.js').TextBasedChannel} DiscordTarget
 */

/**
 * Sends a message to a target channel or user.
 *
 * @param {DiscordTarget} target - Discord.js Channel or User object
 * @param {string|Object} content - Message content or Discord.js MessageOptions object
 * @returns {Promise<Object>} - The sent message object
 * @throws {Error} - If message sending fails
 */
export async function sendMessage(target, content = {}) {
  try {
    if (!target) {
      throw new Error('Invalid message target: Target is null or undefined');
    }

    // Check if the target has a 'send' method at runtime (type guard)
    if ('send' in target && typeof target.send === 'function') {
      return await target.send(content);
    }
    throw new Error('Target does not support sending messages.');
  } catch (error) {
    logger.error('Failed to send message:', error);
    throw error;
  }
}
