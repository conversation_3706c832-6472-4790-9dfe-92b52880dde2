/**
 * @file CONFIG.JS
 *
 * @version 3.0.1
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Core configuration hub for the Discord bot.
 * Centralizes all settings including Discord intents, partials,
 * cooldowns, roles, channels, encryption, onboarding settings,
 * logging preferences, environment variables, API connections,
 * and database configurations.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { GatewayIntentBits, Partials } from 'discord.js';
import semver from 'semver';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //

// ------------ ENVIRONMENT
/**
 * @typedef {Object} EnvironmentConfig
 * @property {string} TOKEN - Bot token for authentication
 * @property {string} CLIENT_ID - Discord application client ID
 * @property {string} CLIENT_SECRET - Discord application client secret
 * @property {string} APPLICATION_ID - Discord application ID
 * @property {string} APP_ENV - Node environment (development, production)
 */

// ------------ ROLE IDS
/**
 * @typedef {Object} RoleConfig
 * @property {string} admin - Admin role ID
 * @property {string} hr - HR role ID
 * @property {string} verified - Verified user role ID
 */

// ------------ CHANNEL IDS
/**
 * @typedef {Object} ChannelConfig
 * @property {string} welcome - Welcome channel ID
 * @property {string} adminLog - Admin log channel ID
 * @property {string} errorLog - Error log channel ID
 * @property {string} general - General channel ID
 */

// ------------ CACHE SETTINGS
/**
 * @typedef {Object} CacheConfig
 * @property {number} cacheTTL - Time-to-live for cached items (ms)
 * @property {number} cacheCheckInterval - Interval for checking expired items (ms)
 * @property {number} cacheMaxSize - Maximum number of items in cache
 */

// ------------ RATE LIMIT SETTINGS
/**
 * @typedef {Object} RateLimitConfig
 * @property {number} windowMS - Time window for rate limiting (ms)
 * @property {number} maxRequests - Maximum requests per window
 * @property {number} cleanupInterval - Interval for cleaning up expired entries (ms)
 * @property {string[]} bypassRoles - Role IDs that bypass rate limits
 */

// ------------ ENCRYPTION KEYS
/**
 * @typedef {Object} EncryptionConfig
 * @property {string} userData - Key for encrypting user data
 */

// ------------ ONBOARDING SETTINGS
/**
 * @typedef {Object} OnboardingConfig
 * @property {boolean} enabled - Enable/disable onboarding
 * @property {boolean} collectEmail - Enable/disable email verification
 * @property {boolean} collectRobloxUsername - Enable/disable Roblox Username verification
 * @property {number} verificationTimeout - Time limit for completing verification (ms)
 * @property {number} timeoutCheckInterval - Interval for checking timeouts (ms)
 * @property {number} maxSessionAge - Maximum onboarding session age (ms)
 * @property {number} dmTimeoutMs - Timeout for DM interactions (ms)
 */

// ------------ LOGGING SETTINGS
/**
 * @typedef {Object} FileLogOptions
 * @property {string} [filename] - Log filename pattern
 * @property {string} [maxSize] - Max file size before rotation
 * @property {string} [maxFiles] - Max number of files to keep
 * @property {string} [format] - Log format ('json', 'pretty')
 */

/**
 * @typedef {Object} ConsoleLogOptions
 * @property {boolean} [colorize] - Whether to colorize output
 * @property {string} [format] - Log format ('pretty', 'json')
 */

/**
 * @typedef {Object} DiscordLogOptions
 * @property {boolean} [includeStack] - Include stack traces
 * @property {number} [maxFieldLength] - Max field length
 * @property {boolean} [notifyOnError] - Whether to notify on errors
 */

/**
 * @typedef {Object} SentryLogOptions
 * @property {number} [tracesSampleRate] - Sample rate for traces
 * @property {boolean} [attachStacktrace] - Attach stack traces
 * @property {string} [environment] - Environment name
 * @property {boolean} [includeBreadcrumbs] - Include breadcrumbs
 */

/**
 * @typedef {Object} LogTargetConfig
 * @property {string[]} levels - Log levels to capture
 * @property {boolean} enabled - Whether this target is enabled
 * @property {FileLogOptions|ConsoleLogOptions|DiscordLogOptions|SentryLogOptions} options - Target-specific options
 */

/**
 * @typedef {Object} LoggingConfig
 * @property {LogTargetConfig & {options: FileLogOptions}} file - File logging settings
 * @property {LogTargetConfig & {options: ConsoleLogOptions}} console - Console logging settings
 * @property {LogTargetConfig & {options: DiscordLogOptions} & {errorChannel?: string, warningChannel?: string}} discord - Discord logging settings
 * @property {LogTargetConfig & {options: SentryLogOptions} & {dsn?: string}} sentry - Sentry logging settings
 * @property {string} defaultLevel - Default minimum log level
 * @property {boolean} maskSensitiveData - Whether to mask sensitive data
 * @property {string[]} sensitiveFields - Fields to mask
 * @property {string} logFileDir - Log directory
 * @property {number} maxLogSize - Max log size
 * @property {number} maxLogFiles - Max log files
 */

// ------------ API & ENDPOINTS SETTINGS
/**
 * @typedef {Object} ApiConfig
 * @property {Object} roblox - Roblox API configuration
 * @property {string} roblox.clientKey - Roblox Client Key
 * @property {string} roblox.clientSecretKey - Roblox Secret Key
 * @property {string} roblox.assetsApiKey - Roblox Assets API Key
 * @property {Object} cirrus - Cirrus API configuration
 * @property {string} cirrus.apiKey - Cirrus API key
 * @property {string} cirrus.logApiKey - Cirrus logging API key
 */

// ------------ DATABASE SETTINGS
/**
 * @typedef {Object} DatabaseTableConfig
 * @property {string} internal - Internal workers table name
 */

/**
 * @typedef {Object} DatabaseConfig
 * @property {string} dbUser - MySQL username
 * @property {string} dbName - MySQL database name
 * @property {string} dbPassword - MySQL password
 * @property {string} dbHost - MySQL hostname
 * @property {string} dbPort - MySQL port
 * @property {boolean} waitForConnections - Wait for connections flag
 * @property {number} connectionLimit - Connection limit
 * @property {number} queueLimit - Queue limit
 * @property {DatabaseTableConfig} tables - Database table configurations
 */

// ------------ COLOR SETTINGS
/**
 * @typedef {Object} ColorConfig
 * @property {string} primary - Main brand color hex code
 * @property {string} success - Success indicator color hex code
 * @property {string} error - Error indicator color hex code
 * @property {string} warning - Warning indicator color hex code
 */

// ------------ HEALTH CHECK SETTINGS
/**
 * @typedef {Object} HealthCheckConfig
 * @property {number} HEALTH_CHECK_INTERVAL - Interval for health checks (ms)
 * @property {number|string} HEALTH_PORT - Port for health check server
 * @property {boolean|string} ENABLE_HEALTH_ENDPOINT - Whether to enable the health endpoint
 */

// ------------ COMMAND COOLDOWNS
/**
 * @typedef {Object} CommandCooldownConfig
 * @property {number} broadcast - Broadcast command cooldown (ms)
 * @property {number} manageemployee - ManageEmployee command cooldown (ms)
 * @property {number} message - Message command cooldown (ms)
 * @property {number} onboarding - Onboarding command cooldown (ms)
 * @property {number} restart - Restart command cooldown (ms)
 * @property {number} shutdown - Shutdown command cooldown (ms)
 * @property {number} viewmessages - ViewMessages command cooldown (ms)
 * @property {number} help - Help command cooldown (ms)
 * @property {number} ping - Ping command cooldown (ms)
 * @property {number} register - Register command cooldown (ms)
 */

// ------------ GLOBAL COOLDOWN SETTINGS
/**
 * @typedef {Object} CooldownConfig
 * @property {number} commands - Global command cooldown (ms)
 * @property {number} onboarding - Onboarding attempt cooldown (ms)
 * @property {number} broadcast - Broadcast cooldown (ms)
 * @property {CommandCooldownConfig} commandCooldowns - Per-command cooldowns
 */

// ------------ BOT CONFIGURATION
/**
 * @typedef {Object} BotConfig
 * @property {CooldownConfig} cooldowns - Cooldown configurations
 * @property {HealthCheckConfig} healthCheck - Health check configurations
 * @property {ColorConfig} colors - Embedded message colors
 * @property {string} version - Bot version
 * @property {RoleConfig} roles - Role ID configurations
 * @property {ChannelConfig} channels - Channel ID configurations
 * @property {CacheConfig} cache - Cache configurations
 * @property {RateLimitConfig} rateLimits - Rate limit configurations
 * @property {EncryptionConfig} encryption - Encryption configurations
 * @property {OnboardingConfig} onboarding - Onboarding configurations
 * @property {LoggingConfig} logging - Logging configurations
 * @property {EnvironmentConfig} env - Environment variables
 * @property {ApiConfig} apis - API configurations
 * @property {DatabaseConfig} database - Database configurations
 */

// ------------ ENVIRONMENT VALIDATION
/**
 * Validates required environment variables and Node.js version.
 * Checks if all necessary environment variables are defined and
 * ensures the Node.js version meets the minimum requirements.
 *
 * @returns {boolean} Whether validation passed successfully
 * @throws {Error} If validation fails
 */
function validateEnvironment() {
  const requiredNode = '>=22.15.0';
  if (!semver.satisfies(process.version, requiredNode)) {
    console.error(
      `❌ Node.js version: ${requiredNode} required; you are using ${process.version}`
    );
    return false;
  }

  // Required Environment Variables
  const requiredEnvVars = [
    'ADMIN_ROLE_ID',
    'HR_ROLE_ID',
    'VERIFIED_ROLE_ID',
    'WELCOME_CHANNEL_ID',
    'ADMIN_LOG_CHANNEL_ID',
    'ERROR_LOG_CHANNEL_ID',
    'TOKEN',
    'APPLICATION_ID',
    'PUBLIC_KEY',
    'CLIENT_ID',
    'CLIENT_SECRET',
    'DB_USERNAME',
    'DB_DATABASE',
    'DB_PASSWORD',
    'DB_HOST',
    'DB_PORT',
    'USER_DATA_ENCRYPTION_KEY',
    'ROBLOX_CLIENT_ID',
    'ROBLOX_CLIENT_SECRET',
    'ROBLOX_ASSETS_API_KEY',
    'CIRRUS_API_KEY',
    'CIRRUS_LOG_API_KEY',
    'SENTRY_DSN',
    'VERSION',
    'HEALTH_CHECK_INTERVAL',
    'HEALTH_PORT',
    'ENABLE_HEALTH_ENDPOINT',
  ];

  // Check for missing environment variables
  const missingVars = requiredEnvVars.filter(varName => {
    // Ensure varName is a string (defense in depth)
    if (typeof varName !== 'string') {
      return false;
    }

    // Check if the environment variable is missing or empty
    // Use safer approach to avoid object injection
    const hasProperty = Object.prototype.hasOwnProperty.call(
      process.env,
      varName
    );
    const envValue = hasProperty
      ? Object.getOwnPropertyDescriptor(process.env, varName)?.value
      : undefined;

    return !envValue || envValue.trim() === '';
  });

  // Check if any required environment variables are missing
  if (missingVars.length > 0) {
    console.error(
      `❌ Missing required environment variables: ${missingVars.join(', ')}`
    );
    return false;
  }

  return true;
}

// ------------ DISCORD.JS INTENTS - CAPABILITIES THAT THE BOT NEEDS
/**
 * Discord.js intents configuration.
 * Defines which events the bot will receive from Discord.
 *
 * @type {GatewayIntentBits[]}
 */
const intents = [
  // For basic server interactions
  GatewayIntentBits.Guilds,

  // For member add events and user info
  GatewayIntentBits.GuildMembers,

  // For message commands
  GatewayIntentBits.GuildMessages,

  // For reading message content
  GatewayIntentBits.MessageContent,

  // For DM interactions
  GatewayIntentBits.DirectMessages,

  // For DM reactions
  GatewayIntentBits.DirectMessageReactions,
];

// ------------ DISCORD.JS PARTIALS - FOR HANDLING UNCACHED DATA
/**
 * Discord.js partials configuration for handling uncached data.
 * Allows the bot to receive events for entities that weren't
 * cached in the bot's memory.
 *
 * @type {Partials[]}
 */
const partials = [
  // For handling users that aren't cached
  Partials.User,

  // For handling DM channels that aren't cached
  Partials.Channel,

  // For handling messages that aren't cached
  Partials.Message,

  // For handling guild members that aren't cached
  Partials.GuildMember,
];

// ------------------------ BOT CONFIGURATION ------------------------ //
/**
 * Main bot configuration object.
 * Contains all settings for the bot's functionality.
 *
 * @type {BotConfig}
 */
const botConfig = {
  // ------------ COOLDOWNS FOR VARIOUS OPERATIONS (in milliseconds)
  cooldowns: {
    // 3 global seconds between commands
    commands: 3000,

    // 5 minutes between onboarding attempts
    onboarding: 300000,

    // 5 minutes between broadcasts
    broadcast: 300000,

    // Per-command cooldowns (override default if present)
    commandCooldowns: {
      // 10 minutes for /broadcast
      broadcast: 600000,

      // 7 seconds for /manage-employee
      manageemployee: 7000,

      // 5 seconds for /message
      message: 5000,

      // 5 seconds for /onboarding
      onboarding: 5000,

      // 30 seconds for /restart
      restart: 30000,

      // 1 minute for /shutdown
      shutdown: 60000,

      // 10 seconds for /viewmessages
      viewmessages: 10000,

      // 5 seconds for /help
      help: 5000,

      // 10 seconds for /ping
      ping: 10000,

      // 5 seconds for /register
      register: 5000,
    },
  },

  // ------------ HEALTH CHECK SYSTEM
  healthCheck: {
    // Health check interval in milliseconds
    HEALTH_CHECK_INTERVAL: Number(process.env.HEALTH_CHECK_INTERVAL),

    // Health check port
    HEALTH_PORT: process.env.HEALTH_PORT ?? '3000',

    // Whether to enable the health check endpoint
    ENABLE_HEALTH_ENDPOINT: process.env.ENABLE_HEALTH_ENDPOINT === 'true',
  },

  // ------------ EMBEDDED MESSAGES COLORS
  colors: {
    // D.I.S Main brand hex color
    primary: '#ccf3ff',

    // Discord green
    success: '#43B581',

    // Discord red
    error: '#F04747',

    // Discord yellow
    warning: '#FAA61A',
  },

  // ------------ BOT VERSION
  version: process.env.VERSION || '',

  // ------------ ROLE IDS
  roles: {
    admin: process.env.ADMIN_ROLE_ID || '1367890379600302172',
    hr: process.env.HR_ROLE_ID || '1367899953338388651',
    verified: process.env.VERIFIED_ROLE_ID || '1367890420146769961',
  },

  // ------------ CHANNEL IDS
  channels: {
    // For all channels, 1st one is default 2nd one is for the test server
    welcome: process.env.WELCOME_CHANNEL_ID || '1367901208802754732',
    adminLog: process.env.ADMIN_LOG_CHANNEL_ID || '1367901335344906300',
    errorLog: process.env.ERROR_LOG_CHANNEL_ID || '1367901252196892826',
    general: process.env.GENERAL_CHANNEL_ID || '',
  },

  // ------------ CENTRALIZED CACHE SETTINGS
  cache: {
    // Default time-to-live for cached items (5 minutes)
    cacheTTL: 300000,

    // Interval for checking expired items (1 minute)
    cacheCheckInterval: 60000,

    // Maximum number of items in cache
    cacheMaxSize: 1000,
  },

  // ------------ CENTRALIZED RATE LIMIT SETTINGS
  rateLimits: {
    // 1 minute window for rate limiting
    windowMS: 60000,

    // Maximum requests per window
    maxRequests: 100,

    // Interval for cleaning up expired entries
    cleanupInterval: 60000,

    // Role IDs that bypass rate limits
    bypassRoles: [process.env.ADMIN_ROLE_ID || ''],
  },

  // ------------ ENCRYPTION KEYS
  encryption: {
    // Key for encrypting user data
    userData: process.env.USER_DATA_ENCRYPTION_KEY || '',
  },

  // ------------ ONBOARDING SETTINGS
  onboarding: {
    // Enable/disable onboarding
    enabled: true,

    // Enable/disable email verification
    collectEmail: true,

    // Enable/disable Roblox Username verification
    collectRobloxUsername: true,

    // 24 hours to complete verification
    verificationTimeout: 86400000,

    // Timeout check interval (1 minute)
    timeoutCheckInterval: 60000,

    // Maximum session age for verification (24 hours)
    maxSessionAge: 86400000,

    // DM timeout in milliseconds (5 minutes)
    dmTimeoutMs: 300000,
  },

  // ------------ LOGGING SETTINGS
  logging: {
    // Global logging level ('trace', 'debug', 'info', 'warn', 'error')
    defaultLevel: 'info',

    // Whether to mask sensitive data in logs
    maskSensitiveData: true,

    // Fields considered sensitive
    sensitiveFields: ['email', 'token', 'secret', 'key'],

    // Directory to store log files
    logFileDir: 'logs',

    // Maximum size of log file before rotation (bytes)
    maxLogSize: 10 * 1024 * 1024,
    // 10 MB ^

    // Maximum number of rotated log files to keep
    maxLogFiles: 7,

    // File logging settings
    file: {
      enabled: true,
      levels: ['trace', 'debug', 'info', 'warn', 'error'],
      options: {
        maxSize: '10m',
        maxFiles: '7d',
        format: 'json',
      },
    },

    // Console logging settings
    console: {
      enabled: true,
      levels: ['debug', 'info', 'warn', 'error'],
      options: {
        colorize: true,
      },
    },
    // Discord channel logging settings
    discord: {
      enabled: true,
      levels: ['warn', 'error'],
      errorChannel: process.env.ERROR_LOG_CHANNEL_ID,
      warningChannel: process.env.ADMIN_LOG_CHANNEL_ID,
      options: {
        includeStack: true,
        maxFieldLength: 1024,
      },
    },

    // Sentry logging settings
    sentry: {
      enabled: process.env.SENTRY_DSN ? true : false,
      dsn: process.env.SENTRY_DSN,
      levels: ['warn', 'error'],
      options: {
        tracesSampleRate: 1.0,
        attachStacktrace: true,
        environment: process.env.APP_ENV || 'development',
        includeBreadcrumbs: true,
      },
    },
  },

  // ------------ CENTRALIZED ENVIRONMENT VARIABLES
  env: {
    // Bot token
    TOKEN: process.env.TOKEN || '',

    // Bot client ID
    CLIENT_ID: process.env.CLIENT_ID || '',

    // Bot client secret
    CLIENT_SECRET: process.env.CLIENT_SECRET || '',

    // Bot application ID
    APPLICATION_ID: process.env.APPLICATION_ID || '',

    // Node environment (development, production, etc.)
    APP_ENV: process.env.APP_ENV || '',
  },

  // ------------ CENTRALIZED API & ENDPOINTS SETTINGS
  apis: {
    // Roblox API & Endpoints
    roblox: {
      // Roblox Client Key
      clientKey: process.env.ROBLOX_CLIENT_ID || '',

      // Roblox Secret
      clientSecretKey: process.env.ROBLOX_CLIENT_SECRET || '',

      // Roblox Assets API Key
      assetsApiKey: process.env.ROBLOX_ASSETS_API_KEY || '',
    },

    // Cirrus API & Endpoints
    cirrus: {
      // Cirrus API Key
      apiKey: process.env.CIRRUS_API_KEY || '',

      // Cirrus Logging API Key
      logApiKey: process.env.CIRRUS_LOG_API_KEY || '',
    },
  },

  // ------------ CENTRALIZED DATABASE SETTINGS
  database: {
    // MySQL Username
    dbUser: process.env.DB_USERNAME || '',

    // MySQL NAME
    dbName: process.env.DB_DATABASE || '',

    // MySQL Password
    dbPassword: process.env.DB_PASSWORD || '',

    // MySQL Hostname
    dbHost: process.env.DB_HOST || '',

    // MySQL Port
    dbPort: process.env.DB_PORT || '',

    // Wait for connections
    waitForConnections: true,

    // Connection limit
    connectionLimit: 10,

    // Queue limit
    queueLimit: 0,

    // Table names
    tables: {
      // Internal workers collection
      internal: 'dis_internal_workers',
    },
  },
};

// ------------ EXPORTS
export { botConfig, intents, partials, validateEnvironment };
