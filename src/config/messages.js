/**
 * @file MESSAGES.JS
 *
 * @version 3.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Centralized storage for all bot message templates.
 * Used for consistent messaging across commands and events.
 * Organized by category for maintainability and reusability.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //

// ------------ ONBOARDING MESSAGES
/**
 * @typedef {Object} OnboardingMessages
 * @property {string} welcome - Onboarding completion welcome message
 * @property {string} cancellation - Onboarding cancellation message
 * @property {string} onboardingComplete - Onboarding completion message
 * @property {string} timeout - Onboarding session timeout message
 */

// ------------ ERROR MESSAGES
/**
 * @typedef {Object} ErrorMessages
 * @property {string} invalidEmail - Invalid email format error
 * @property {string} invalidRobloxUsername - Invalid Roblox username format error
 * @property {string} invalidNickname - Invalid nickname format error
 * @property {string} invalidTimezone - Invalid timezone format error
 * @property {string} invalidYesNo - Invalid yes/no response error
 * @property {string} permissionDenied - Permission denied error
 * @property {string} internalError - Internal system error
 * @property {string} cooldown - Command cooldown error
 * @property {string} dmFailed - DM delivery failure error
 */

// ------------ ADMIN MESSAGES
/**
 * @typedef {Object} AdminBroadcastErrors
 * @property {string} noRoleMembers - No members found for broadcast
 * @property {string} finalReport - Final broadcast report
 */

// ------------ ADMIN EMBED MESSAGES
/**
 * @typedef {Object} AdminViewMessagesEmbed
 * @property {string} title
 * @property {string} color
 * @property {string} description
 * @property {string} footer
 * @property {string} thumbnail
 * @property {string} content
 */

// ------------ ADMIN HELP EMBED
/**
 * @typedef {Object} AdminHelpEmbed
 * @property {string} title
 * @property {string} color
 * @property {string} description
 * @property {Array<{name: string, value: string, inline: boolean}>} fields
 * @property {Object} footer
 */

// ------------ ADMIN STARTUP EMBED
/**
 * @typedef {Object} AdminStartupEmbed
 * @property {string} title
 * @property {string} color
 * @property {string} description
 * @property {Array<{name: string, value: string, inline: boolean}>} fields
 * @property {string} footer
 */

// ------------ ADMIN ONBOARDING PROGRESS EMBED
/**
 * @typedef {Object} AdminOnboardingProgressEmbed
 * @property {string} title
 * @property {string} color
 * @property {string} description
 * @property {string} footer
 */

// ------------ ADMIN MESSAGES
/**
 * @typedef {Object} AdminMessages
 * @property {string} broadcastStart
 * @property {string} broadcastComplete
 * @property {string} messageSent
 * @property {string} noMessages
 * @property {AdminViewMessagesEmbed} viewMessagesEmbed
 * @property {AdminBroadcastErrors} broadcastErrors
 * @property {AdminHelpEmbed} helpEmbed
 * @property {AdminStartupEmbed} startupEmbed
 * @property {AdminOnboardingProgressEmbed} onboardingProgressEmbed
 */

// ------------ HR MESSAGES
/**
 * @typedef {Object} HRHelpEmbed
 * @property {string} title
 * @property {string} color
 * @property {string} description
 * @property {Array<{name: string, value: string, inline: boolean}>} fields
 * @property {Object} footer
 */

// ------------ HR EMBED MESSAGES
/**
 * @typedef {Object} HRMessages
 * @property {HRHelpEmbed} helpEmbed
 */

// ------------ MEMBER MESSAGES
/**
 * @typedef {Object} MemberHelpEmbed
 * @property {string} title
 * @property {string} color
 * @property {string} description
 * @property {Array<{name: string, value: string, inline: boolean}>} fields
 * @property {Object} footer
 */

// ------------ MEMBER EMBED MESSAGES
/**
 * @typedef {Object} MemberMessages
 * @property {MemberHelpEmbed} helpEmbed
 * @property {string} registerPrompt
 * @property {string} alreadyRegistered
 */

// ------------ MESSAGES TYPE
/**
 * @typedef {Object} Messages
 * @property {OnboardingMessages} onboarding
 * @property {ErrorMessages} errors
 * @property {AdminMessages} admin
 * @property {HRMessages} hr
 * @property {MemberMessages} member
 */

// ------------ MESSAGE TEMPLATES

/** @type {Messages} */
export const messages = {
  onboarding: {
    cancellation: '❌ **Verification Cancelled!** Use `/register` to restart.',

    welcome: `👋 **Welcome to Dynamic Innovative Studio, {user}!** We're excited to have you here. Introduce yourself to the team!`,
    onboardingComplete: `
  >>> ## 🎉 **ONBOARDING COMPLETE!** 🎊

  You now have full server access! Here's what to do next:

  **Getting Started:**
  • Use the \`/help\` command for a full list of available tools and commands.
  • Check announcements channels regularly for important updates and news.
  • If you need assistance, feel free to reach out to Human Resources.
  • To begin working on current projects, contact the Production Team, your Department Lead, or a Director.

  **Explore Our Work:**
  • Browse active repositories and contribute to ongoing development: [Dynamic Innovative Studio on GitHub](https://github.com/Dynamic-Innovative-Studio) to see what we are working on!
  • Check our portfolio and community website: [Dynamic Innovative Studio Official Website](https://dynamic-innovative-studio.firebaseapp.com) to see our latest news and updates!

  **Community & Platforms:**
  • Join our community on Community Discord: [Dynamic Innovative Community](https://discord.gg/nGEnj6abUs) to chat with the community!
  • Join our community on Roblox: [Dynamic Innovative Studio](https://www.roblox.com/communities/34320208/Dynamic-Innovative-Studio) if you are a Roblox Related Worker!

  **Support Our Mission:**
  • Help us grow and gain access to exclusive content: [Dynamic Innovative Studio Patreon](https://patreon.com/DynamicInnovativeStudio?utm_medium=unknown&utm_source=join_link&utm_campaign=creatorshare_creator&utm_content=copyLink)
  • Watch our latest videos: [Dynamic Innovative Studio YouTube](https://www.youtube.com/@DynamicInnovativeStudio) to see our latest videos!


  💡 Pro Tip: Custom Pings in <#1342392288722161716>!
		`,

    timeout:
      '⏳ **Session Expired!** Use `/register` in D.I.S server again to restart verification.',
  },

  errors: {
    invalidEmail: `
  ❌ **Invalid Email Format!**
  Please use: \`<EMAIL>\`
  *(Example: \`<EMAIL>\`)*
		`,

    invalidRobloxUsername: `
  ❌ **Invalid Roblox Username!**
  Please use a valid Roblox username (3-20 characters, letters/numbers/underscores only)
  *(Example: \`Player123\` or \`Cool_Gamer\`)*
  *Or type \`skip\` if you don't have one*
		`,

    invalidNickname: `
  ❌ **Invalid Nickname!**
  Please enter a valid name (letters, numbers, spaces, and common symbols only)
  *(Example: \`John\`, \`Alex_123\`, or \`Cool Player!\`)*
		`,

    invalidTimezone: `
  ❌ **Invalid Timezone!**
  Please use a valid timezone format
  *(Examples: \`UTC+01:00\`, \`GMT-5\`, \`EST\`, \`PST\`)*
		`,

    invalidYesNo: `
  ❌ **Invalid Response!**
  Please answer with \`yes\` or \`no\`
  *(You can also use: \`y\`, \`n\`, \`yeah\`, \`nope\`)*
		`,

    permissionDenied: '🚫 **Access Denied!**',

    internalError:
      '⚠️ **System Error!** Please contact Founder & CEO immediately.',

    cooldown: '⏱️ **Command Cooldown:** Try again in {time} seconds',

    dmFailed:
      '📭 **DM Failed!** Enable DMs in Privacy Settings and retry or use the same command but in the server.',
  },

  admin: {
    broadcastStart: '📢 Initializing broadcast to {count} users...',

    broadcastComplete: `
  ✅ **Broadcast Complete!**
  • Success: {success} users
  • Failed: {failed} users
		`,

    messageSent: '📨 Message delivered to `{user}`',

    noMessages: '📭 No message history found',

    viewMessagesEmbed: {
      title: '📨 DM History with {user}',
      color: '#ccf3ff',
      description: '{formatted_messages}',
      footer: 'Showing {message_count} messages',
      thumbnail: '{user_avatar}',
      content: '📜 **Message history for {user}**',
    },

    broadcastErrors: {
      noRoleMembers: '❌ No members found with the specified role',
      finalReport:
        '✅ **Broadcast Complete!**\nSuccess: {success}\nFailed: {failed}',
    },

    helpEmbed: {
      title: '🛠️ **ADMIN COMMAND LIST** 📋',
      color: '#ccf3ff',
      description: 'Administrative commands for bot and server management',
      fields: [
        { name: '/register', value: 'Starts verification', inline: false },
        { name: '/help', value: 'Show this dynamic help menu', inline: false },
        { name: '/ping', value: 'Check bot latency', inline: false },
        {
          name: '/broadcast [message] [role] [title] [author] [footer] [timestamp] [color]',
          value: "Mass DM verified employee's",
          inline: false,
        },
        {
          name: '/message [user] [message]',
          value: 'DM specific user',
          inline: false,
        },
        {
          name: '/viewmessages [user] [count]',
          value: 'Check user DM history',
          inline: false,
        },
        { name: '/restart [force]', value: 'Restart bot', inline: false },
        { name: '/shutdown', value: 'Terminate bot', inline: false },
        {
          name: '--WORK IN PROGRESS COMMANDS (below)--',
          value: 'W.I.P',
          inline: false,
        },
        {
          name: '/warn [user] [reason]',
          value: 'Issue formal warning',
          inline: false,
        },
        {
          name: '/roleupdate [user]',
          value: 'Update member roles via a "form"',
          inline: false,
        },
      ],
      footer: {
        text: 'Requires Admin Permissions Role (<@1367889949554376816>)',
      },
    },

    startupEmbed: {
      title: '🚀 Bot Startup Complete',
      color: '#ccf3ff',
      description: 'Dynamic Innovative Studio Discord Bot is now online!',
      fields: [
        { name: 'Version', value: '{version}', inline: true },
        { name: 'Environment', value: '{env}', inline: true },
        { name: 'Node Version', value: '{nodeVersion}', inline: true },
      ],
      footer: 'Bot maintained by Dynamic Innovative Studio',
    },

    onboardingProgressEmbed: {
      title: 'Onboarding Progress',
      color: '#ccf3ff',
      description: 'Current onboarding status for all users.',
      footer: 'Page {page} of {totalPages}',
    },
  },

  hr: {
    helpEmbed: {
      title: '👥 **HR COMMAND LIST** 📋',
      color: '#ccf3ff',
      description: "Human Resources commands for employee's management",
      fields: [
        { name: '/register', value: 'Starts verification', inline: false },
        { name: '/help', value: 'Show this dynamic help menu', inline: false },
        { name: '/ping', value: 'Check bot latency', inline: false },
        {
          name: '/viewmessages [user] [count]',
          value: 'Check user DM history',
          inline: false,
        },
        {
          name: '/message [user] [message]',
          value: 'DM specific user',
          inline: false,
        },
        {
          name: '/manageemployee [user]',
          value: 'View employee details',
          inline: false,
        },
        {
          name: '/onboarding',
          value: 'View onboarding progress',
          inline: false,
        },
        {
          name: '--WORK IN PROGRESS COMMANDS (below)--',
          value: 'W.I.P',
          inline: false,
        },
        {
          name: '/warn [user] [reason]',
          value: 'Issue formal warning',
          inline: false,
        },
        {
          name: '/roleupdate [user]',
          value: 'Update member roles via a "form"',
          inline: false,
        },
      ],
      footer: { text: 'Requires HR Permissions Role (<@1367899953338388651>)' },
    },
  },

  member: {
    helpEmbed: {
      title: "💡 **EMPLOYEE'S COMMANDS**",
      color: '#ccf3ff',
      description: "General employee's commands",
      fields: [
        { name: '/register', value: 'Start verification', inline: false },
        { name: '/help', value: 'Show this menu', inline: false },
        { name: '/ping', value: 'Check bot latency', inline: false },
      ],
      footer: { text: 'Contact HR for further assistance' },
    },

    registerPrompt: '📨 **Check DMs!** Sent registration instructions',

    alreadyRegistered: `
  ℹ️ **Account Verified!**
  Contact any of the Human Resources (HR) for access issues
		`,
  },
};

// ------------ EXPORT DEFAULT
export default messages;
