/**
 * @file PING.JS
 *
 * @version 2.0.1
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Slash command to check the bot's latency and responsiveness.
 * Responds with a "Pong!" embed showing bot and API latency.
 * Includes rate limiting and robust error handling.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { EmbedBuilder, MessageFlags, SlashCommandBuilder } from 'discord.js';

import { botConfig } from '../../config/config.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 */

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('ping')
  .setDescription("Check the bot's latency and responsiveness.");

// ------------ COMMAND EXECUTION
/**
 * Executes the ping command.
 * Shows bot and API latency in an embed.
 *
 * @param {ChatInputCommandInteraction} interaction - The command interaction.
 * @returns {Promise<void>}
 */
async function _execute(interaction) {
  try {
    // Rate limit check for ping command
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'ping',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `You're being rate limited. Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    const cooldownKey = `ping:${interaction.user.id}`;
    const pingCooldown = cooldown.getCooldownMs('ping');
    if (!cooldown.check(cooldownKey, pingCooldown)) {
      return await safeReply(interaction, {
        content: `⏳ Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, pingCooldown) / 1000)}s before using this command again.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    const latency = Date.now() - interaction.createdTimestamp;
    const apiLatency = Math.round(interaction.client.ws.ping);

    const embed = new EmbedBuilder()
      .setColor(parseInt(botConfig.colors.primary.replace(/^#/, ''), 16))
      .setTitle('🏓 Pong!')
      .addFields(
        { name: 'Bot Latency', value: `\`${latency}ms\``, inline: true },
        { name: 'API Latency', value: `\`${apiLatency}ms\``, inline: true }
      )
      .setFooter({ text: `Dynamic Innovative Studio • ${botConfig.version}` });

    await safeReply(interaction, { embeds: [embed] });
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    await handleError(err, { command: 'ping' }, undefined, {
      critical: false,
    });
    await safeReply(interaction, {
      content: '⚠️ **System Error!** Please contact Founder & CEO immediately.',
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard('register', _execute, {
    manualCooldown: true,
  }),
};
