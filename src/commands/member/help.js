/**
 * @file HELP.JS
 *
 * @version 3.0.1
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Slash command to display help information for D.I.S Bot commands.
 * Dynamically shows an embed message depending on the user's role.
 * Uses robust type safety and follows project code organization standards.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { MessageFlags, SlashCommandBuilder } from 'discord.js';

import { botConfig } from '../../config/config.js';
import { messages } from '../../config/messages.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import { getHelpEmbedForMember } from '../../utils/help.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 * @typedef {import('discord.js').GuildMember} GuildMember
 */

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('help')
  .setDescription('Show help information for D.I.S Bot commands.');

/**
 * Executes the help command.
 * Shows a dynamic help embed based on the user's role.
 *
 * @param {ChatInputCommandInteraction} interaction - The command interaction.
 * @returns {Promise<void>}
 */
async function _execute(interaction) {
  try {
    // Rate limit check
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'help',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `You're being rate limited. Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    const cooldownKey = `help:${interaction.user.id}`;
    const helpCooldown = cooldown.getCooldownMs('help');
    if (!cooldown.check(cooldownKey, helpCooldown)) {
      return await safeReply(interaction, {
        content: `⏳ Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, helpCooldown) / 1000)}s before using this command again.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    await interaction.deferReply({ flags: [MessageFlags.Ephemeral] });

    // Only proceed if interaction.member is a GuildMember (not APIInteractionGuildMember)
    /** @type {GuildMember|undefined} */
    const guildMember =
      interaction.member &&
      typeof interaction.member.permissions !== 'undefined' &&
      typeof interaction.member.roles !== 'undefined' &&
      typeof interaction.member.user !== 'undefined'
        ? /** @type {GuildMember} */ (interaction.member)
        : undefined;

    if (!guildMember) {
      return await safeReply(interaction, {
        content: messages.errors.internalError,
        flags: [MessageFlags.Ephemeral],
      });
    }

    const embed = getHelpEmbedForMember(guildMember, botConfig.roles, messages);

    embed.setThumbnail(interaction.client.user.displayAvatarURL());

    await safeReply(interaction, {
      embeds: [embed],
      flags: MessageFlags.Ephemeral,
    });
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    await handleError(err, { command: 'help' }, undefined, {
      critical: false,
    });
    await safeReply(interaction, {
      content: messages.errors.internalError,
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard('help', _execute),
};
