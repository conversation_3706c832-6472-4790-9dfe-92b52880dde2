/**
 * @file REGISTER.JS
 *
 * @version 2.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Slash command to start or restart the onboarding process for members.
 * Initiates a DM-based registration workflow handled by the OnboardingService.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { MessageFlags, SlashCommandBuilder } from 'discord.js';

import { messages } from '../../config/messages.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import OnboardingService from '../../services/onboardingService.js';
import * as cooldown from '../../utils/cooldown.js';
import db from '../../utils/Database.js';
import { handleError } from '../../utils/errorHandler.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 * @typedef {import('discord.js').GuildMember} GuildMember
 */

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('register')
  .setDescription('Start or restart the onboarding process.');

// ------------ MAIN EXECUTE FUNCTION
/**
 * @param {import("discord.js").Interaction<import("discord.js").CacheType>} interaction
 * @param {import("discord.js").Client<boolean> | undefined} client
 */
async function _execute(interaction, client) {
  try {
    // Rate limit check for register command
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId ?? undefined,
      guildId: interaction.guildId ?? undefined,
      commandName: 'register',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `You're being rate limited. Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    const cooldownKey = `register:${interaction.user.id}`;
    const registerCooldown = cooldown.getCooldownMs('register');
    if (!cooldown.check(cooldownKey, registerCooldown)) {
      return await safeReply(interaction, {
        content: `⏳ Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, registerCooldown) / 1000)}s before using this command again.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    if (interaction.isChatInputCommand && interaction.isChatInputCommand()) {
      await interaction.deferReply({ flags: [MessageFlags.Ephemeral] });
    }

    // Fetch user from DB
    const user = await db.getUser(interaction.user.id);

    if (user && user.verified) {
      return await safeReply(interaction, {
        content: messages.member.alreadyRegistered,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Try to get GuildMember (for DMs)
    let member = interaction.member;
    if (!member || typeof member.user === 'undefined') {
      // fallback: fetch from guild
      if (interaction.guild) {
        member = await interaction.guild.members.fetch(interaction.user.id);
      }
    }

    // Start onboarding
    if (
      !member ||
      typeof member.user !== 'object' ||
      !('tag' in member.user) ||
      !(
        member instanceof Object &&
        'createDM' in member &&
        typeof member.createDM === 'function'
      )
    ) {
      await safeReply(interaction, {
        content: messages.errors.internalError,
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }
    try {
      await OnboardingService.startOnboarding(member);
    } catch (err) {
      // If DM failed due to closed DMs, show a clear error
      if (err && err.code === 50007) {
        await safeReply(interaction, {
          content: messages.errors.dmFailed,
          flags: [MessageFlags.Ephemeral],
        });
        return;
      }
      throw err;
    }

    await safeReply(interaction, {
      content: messages.member.registerPrompt,
      flags: [MessageFlags.Ephemeral],
    });

    // Set cooldown after successful execution
    cooldown.set(cooldownKey);
  } catch (error) {
    await handleError(error, { command: 'register' }, client, {
      critical: true,
    });
    await safeReply(interaction, {
      content: messages.errors.internalError,
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard('register', _execute, {
    manualCooldown: true,
  }),
};
