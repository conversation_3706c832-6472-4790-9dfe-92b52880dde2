// @ts-check
/**
 * @file VIEWMESSAGES.JS
 *
 * @version 3.0.1
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Slash command for administrators to view recent DM history with a specific user.
 * Fetches messages from the user's DM channel and displays them in an embed.
 * Includes rate limiting, input validation, and robust error handling.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { EmbedBuilder, MessageFlags, SlashCommandBuilder } from 'discord.js';

import { botConfig } from '../../config/config.js';
import { messages } from '../../config/messages.js';
import { CommandMiddleware } from '../../middleware/commandAuthentication.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import logger from '../../utils/logger.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 * @typedef {import('discord.js').GuildMember} GuildMember
 * @typedef {import('discord.js').User} DiscordUser
 * @typedef {import('discord.js').Client} DiscordClient
 * @typedef {import('discord.js').Message} DiscordMessage
 */

/**
 * Maximum allowed count value
 * Limits how many messages can be retrieved at once
 */
const MAX_MESSAGE_COUNT = 50;

/**
 * Default number of messages to retrieve if not specified
 */
const DEFAULT_MESSAGE_COUNT = 10;

/**
 * Character limit for individual message content in the display
 * Prevents excessively long messages from breaking the display
 */
const MESSAGE_DISPLAY_CHAR_LIMIT = 1000;

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('viewmessages')
  .setDescription(
    'View recent messages for a specific user by username or mention.'
  )
  .addUserOption(option =>
    option
      .setName('co-worker')
      .setDescription(
        'The co-worker to view messages for (mention or username)'
      )
      .setRequired(true)
  )
  .addIntegerOption(option =>
    option
      .setName('count')
      .setDescription(
        `Number of recent messages to display (default: ${DEFAULT_MESSAGE_COUNT}, max: ${MAX_MESSAGE_COUNT})`
      )
      .setMinValue(1)
      .setMaxValue(MAX_MESSAGE_COUNT)
  );

// ------------ UTILITY FUNCTIONS

/**
 * Truncates a string if it exceeds the specified length
 * @param {string} str - String to truncate
 * @param {number} maxLength - Maximum allowed length
 * @returns {string} - Truncated string with ellipsis if needed
 */
function truncateString(str, maxLength) {
  if (str.length <= maxLength) return str;
  return str.substring(0, maxLength - 3) + '...';
}

/**
 * Formats an array of message objects for display.
 * @param {Array<{type: string, timestamp: Date, content: string}>} messageList
 * @returns {string}
 */
function formatMessagesDisplay(messageList) {
  return messageList
    .map(msg => {
      // Determine message source indicator
      const source = msg.type === 'incoming' ? 'User' : 'Bot';

      // Format timestamp
      const timestamp = msg.timestamp.toLocaleString();

      // Truncate content if too long
      const content = truncateString(msg.content, MESSAGE_DISPLAY_CHAR_LIMIT);

      // Build formatted message line
      return `[${timestamp}] **${source}:** ${content}`;
    })
    .join('\n');
}

// ------------ MAIN EXECUTE FUNCTION

/**
 * Executes the viewmessages command.
 * @param {ChatInputCommandInteraction} interaction
 * @param {DiscordClient} client
 * @returns {Promise<void>}
 */
async function _execute(interaction, client) {
  try {
    // Get user and count from command options
    const user = interaction.options.getUser('co-worker');
    const userId = user?.id;
    const count =
      interaction.options.getInteger('count') || DEFAULT_MESSAGE_COUNT;

    // Validate userId
    if (!userId || !/^\d{17,20}$/.test(userId)) {
      return await safeReply(interaction, {
        content: 'Invalid user ID provided.',
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Validate count
    if (typeof count !== 'number' || count < 1 || count > MAX_MESSAGE_COUNT) {
      logger.warn(`Invalid count provided to /viewmessages: ${count}`);
      return await safeReply(interaction, {
        content: `Count must be between 1 and ${MAX_MESSAGE_COUNT}.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Rate limit check
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'viewmessages',
    };
    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `You're being rate limited. Try again in ${Math.ceil(rate.resetTime / 1000)} seconds. You have ${status.remaining} requests left.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    const cooldownKey = `viewmessages:${interaction.user.id}`;
    const viewMessagesCooldown = cooldown.getCooldownMs('viewmessages');
    if (!cooldown.isExpired(cooldownKey, viewMessagesCooldown)) {
      return await safeReply(interaction, {
        content: `⏳ Please wait ${Math.ceil(cooldown.getRemaining(cooldownKey, viewMessagesCooldown) / 1000)}s before using this command again.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    await interaction.deferReply({ flags: [MessageFlags.Ephemeral] });

    // User details for display
    const userDisplay = user.tag;
    const userAvatar = user.displayAvatarURL();
    logger.debug(`Processing messages for user: ${userDisplay}`);

    // Fetch messages from DM channel
    try {
      const dmChannel = await user.createDM();
      const fetchedMessages = await dmChannel.messages.fetch({ limit: count });

      // Oldest first
      const messagesArray = Array.from(fetchedMessages.values()).reverse();

      // Check if messages exist
      if (messagesArray.length === 0) {
        return await safeReply(interaction, {
          content: messages.admin.noMessages,
        });
      }

      // Map messages to expected format
      const userMessages = messagesArray.map(msg => ({
        type: msg.author.id === userId ? 'incoming' : 'outgoing',
        timestamp: msg.createdAt,
        content: msg.content || '*Message has no text content*',
      }));

      logger.info(
        `Retrieved ${userMessages.length} messages from DMs with ${userId}`
      );

      // Format and send embed
      const formatted = formatMessagesDisplay(userMessages);
      const embedTemplate = messages.admin.viewMessagesEmbed;

      const embed = new EmbedBuilder()
        .setColor(
          embedTemplate.color
            ? parseInt(embedTemplate.color.replace(/^#/, ''), 16)
            : parseInt(botConfig.colors.primary.replace(/^#/, ''), 16)
        )
        .setTitle(embedTemplate.title.replace('{user}', userDisplay))
        .setDescription(
          embedTemplate.description.replace('{formatted_messages}', formatted)
        )
        .setFooter({
          text: embedTemplate.footer.replace(
            '{message_count}',
            String(userMessages.length)
          ),
        })
        .setTimestamp();

      if (userAvatar) {
        embed.setThumbnail(
          embedTemplate.thumbnail.replace('{user_avatar}', userAvatar)
        );
      }

      // Send reply
      await safeReply(interaction, {
        content: embedTemplate.content.replace('{user}', userDisplay),
        embeds: [embed],
      });

      // Set cooldown after successful execution
      cooldown.set(cooldownKey);
    } catch (dmError) {
      logger.error('Failed to fetch DMs:', dmError);

      // Pass options as an object, not a boolean
      await handleError(dmError, { command: 'viewmessages' }, client, {
        critical: true,
      });
      await safeReply(interaction, {
        content: messages.errors.internalError,
        flags: [MessageFlags.Ephemeral],
      });
    }
  } catch (error) {
    await handleError(error, { command: 'viewmessages' }, client, {
      critical: true,
    });
    await safeReply(interaction, {
      content: messages.errors.internalError,
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ COMMAND SECURITY
export const execute = CommandHandler.standard(
  'viewmessages',
  CommandMiddleware.hrAndAdmin(_execute)
);

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard(
    'viewmessages',
    CommandMiddleware.hrAndAdmin(_execute)
  ),
  manualCooldown: true,
};
