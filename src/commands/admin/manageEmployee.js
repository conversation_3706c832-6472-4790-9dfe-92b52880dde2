/**
 * @file MANAGE EMPLOYEE.JS
 *
 * @version 1.0.3
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Slash command to view detailed information about employees.
 * Allows HR and Admin users to view contact details, usernames, and system IDs.
 * Includes input validation, rate limiting, and robust error handling.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { EmbedBuilder, MessageFlags, SlashCommandBuilder } from 'discord.js';

import { botConfig } from '../../config/config.js';
import { CommandMiddleware } from '../../middleware/commandAuthentication.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import { decrypt } from '../../models/User.js';
import * as cooldown from '../../utils/cooldown.js';
import db from '../../utils/Database.js';
import { handleError } from '../../utils/errorHandler.js';
import logger from '../../utils/logger.js';
import { checkRateLimit, getRateLimitStatus } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 * @typedef {import('discord.js').Client} DiscordClient
 * @typedef {import('discord.js').User} DiscordUser
 * @typedef {Object} EmployeeData
 * @property {string} id_dis_internal_workers - Discord ID of the employee
 * @property {string} [email] - Email address of the employee
 * @property {string} [nickname] - Nickname of the employee
 * @property {string} [timezone] - Timezone of the employee
 * @property {string} [roblox_username] - Roblox username of the employee
 * @property {string} [discord_username] - Discord username of the employee
 * @property {string} [discord_user_id] - Discord user ID of the employee
 */

// ------------ SLASH COMMAND BUILDER
export const data = new SlashCommandBuilder()
  .setName('manageemployee')
  .setDescription('View detailed information about an employee')
  .addUserOption(option =>
    option
      .setName('co-worker')
      .setDescription('Select the Discord user to view information for')
      .setRequired(true)
  );

// ------------ UTILITY FUNCTIONS
/**
 * Formats database field names into human-readable labels
 *
 * @param {string} fieldName - The raw database field name
 * @returns {string} A formatted, human-readable field name
 */
function formatFieldName(fieldName) {
  // Map of common database field names to more readable versions
  const fieldNameMap = {
    id_dis_internal_workers: 'Discord ID',
    email: 'Email Address',
    nickname: 'Nickname',
    timezone: 'Time Zone',
    roblox_username: 'Roblox Username',
    discord_username: 'Discord Username',
    discord_user_id: 'Discord User ID',
  };

  // Use the mapping if available, otherwise format the field name
  const hasProperty = Object.prototype.hasOwnProperty.call(
    fieldNameMap,
    fieldName
  );
  if (hasProperty) {
    const value = Object.getOwnPropertyDescriptor(
      fieldNameMap,
      fieldName
    )?.value;
    return value;
  }

  // Convert snake_case to Title Case
  return fieldName
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Formats field values for display, handling null/empty values
 *
 * @param {*} value - The value to format
 * @returns {string} Formatted value string
 */
function formatFieldValue(value) {
  if (value === null || value === undefined || value === '') {
    return '*Not provided*';
  }

  return `\`${String(value)}\``;
}

/**
 * Determines if a field should be displayed inline in the embed
 *
 * @param {string} fieldName - The name of the field
 * @returns {boolean} Whether the field should be displayed inline
 */
function shouldBeInline(fieldName) {
  // Fields that look better when not inline (typically longer values)
  const nonInlineFields = [
    'id_dis_internal_workers',
    'discord_user_id',
    'email',
    'nickname',
    'roblox_username',
    'timezone',
  ];

  return !nonInlineFields.includes(fieldName);
}

// ------------ MAIN EXECUTE FUNCTION
/**
 * Core execution function for the manageEmployee command
 *
 * @async
 * @param {ChatInputCommandInteraction} interaction - The Discord interaction object
 * @param {DiscordClient} client - The Discord client instance
 * @returns {Promise<void>}
 * @private
 */
async function _execute(interaction, client) {
  try {
    // Get selected user from command options
    const user = interaction.options.getUser('co-worker');
    if (!user) {
      return await safeReply(interaction, {
        content: '⚠️ No user was selected. Please try again with a valid user.',
        flags: [MessageFlags.Ephemeral],
      });
    }
    const userId = user.id;

    // Security check: Ensure the user ID matches the expected Discord format
    if (!/^\d{17,20}$/.test(userId)) {
      return await safeReply(interaction, {
        content:
          '⚠️ Invalid user ID format. Please try again with a valid user.',
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Prevent excessive usage through rate limiting
    const rateLimitContext = {
      userId: interaction.user.id,
      channelId: interaction.channelId,
      guildId: interaction.guildId ?? undefined,
      commandName: 'manageemployee',
    };

    const rate = checkRateLimit(rateLimitContext);
    if (rate.limited) {
      const status = getRateLimitStatus(rateLimitContext);
      return await safeReply(interaction, {
        content: `⏱️ You're making requests too quickly. Please wait ${Math.ceil(rate.resetTime / 1000)} seconds before trying again. (${status.remaining} requests remaining)`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Personal cooldown check for this specific user
    const cooldownKey = `manageemployee:${interaction.user.id}`;
    const cooldownDuration = cooldown.getCooldownMs('manageemployee');

    if (!cooldown.isExpired(cooldownKey, cooldownDuration)) {
      const remainingTime = Math.ceil(
        cooldown.getRemaining(cooldownKey, cooldownDuration) / 1000
      );
      return await safeReply(interaction, {
        content: `⏳ Please wait ${remainingTime} seconds before using this command again.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Let user know we're working on their request
    await interaction.deferReply({ flags: [MessageFlags.Ephemeral] });

    // Query the database for employee information
    const rows = await db.query(
      'SELECT * FROM dis_internal_workers WHERE id_dis_internal_workers = ?',
      [userId]
    );

    /** @type {EmployeeData|undefined} */
    const employeeData = rows && rows[0];

    // Handle case where employee isn't found in database
    if (!employeeData) {
      return await safeReply(interaction, {
        content: `📝 This person is not registered in our employee database.`,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Decrypt the email field if present
    if (employeeData.email) {
      try {
        const decryptedEmail = decrypt(employeeData.email);
        employeeData.email =
          decryptedEmail === null ? undefined : decryptedEmail;
      } catch (e) {
        // Handle decryption error gracefully
        const err = e instanceof Error ? e : new Error(String(e));
        logger.error(
          {
            event: 'decryption_error',
            command: 'manageemployee',
            user: interaction.user.id,
            username: interaction.user.tag,
            target: userId,
            targetTag: user.tag,
            guild: interaction.guild?.id,
          },
          `Failed to decrypt email for user ${user.tag}: ${err.message}`
        );
        // Set email to a placeholder value
        employeeData.email = '[decryption error]';
      }
    }

    // Format employee data into readable fields for the embed
    const formattedFields = Object.entries(employeeData).map(
      ([fieldName, value]) => {
        // Format the field name to be more readable
        const formattedName = formatFieldName(fieldName);

        // Format the value, handling empty/null values
        const formattedValue = formatFieldValue(value);

        return {
          name: formattedName,
          value: formattedValue,
          inline: shouldBeInline(fieldName),
        };
      }
    );

    // Create a visually appealing embed with the employee information
    const embed = new EmbedBuilder()
      .setTitle(`👤 Employee Profile: ${user.tag}`)
      .setColor(parseInt(botConfig.colors.primary.replace(/^#/, ''), 16))
      .setThumbnail(user.displayAvatarURL({ forceStatic: true, size: 128 }))
      .addFields(formattedFields)
      .setFooter({ text: 'HR & Admin Access Only' })
      .setTimestamp();

    // Send the formatted information as an ephemeral message
    await safeReply(interaction, {
      embeds: [embed],
      flags: [MessageFlags.Ephemeral],
    });

    // Set cooldown after successful execution
    cooldown.set(cooldownKey);

    // Log successful command execution
    logger.info(
      {
        event: 'employee_info_viewed',
        command: 'manageemployee',
        user: interaction.user.id,
        username: interaction.user.tag,
        target: userId,
        targetTag: user.tag,
        guild: interaction.guild?.id,
      },
      `Admin ${interaction.user.tag} viewed employee info for ${user.tag}`
    );
  } catch (error) {
    // Handle errors gracefully
    const err = error instanceof Error ? error : new Error(String(error));
    await handleError(
      err,
      { command: 'manageemployee', user: interaction.user.id },
      client,
      { critical: true }
    );
    await safeReply(interaction, {
      content:
        '⚠️ Something went wrong while retrieving employee information. The error has been logged.',
      flags: [MessageFlags.Ephemeral],
    });
  }
}

// ------------ COMMAND SECURITY
export const execute = CommandHandler.standard(
  'manageemployee',
  CommandMiddleware.hrAndAdmin(_execute)
);

// ------------ EXPORT
export default {
  data,
  execute: CommandHandler.standard(
    'manageemployee',
    CommandMiddleware.hrAndAdmin(_execute)
  ),
  manualCooldown: true,
};
