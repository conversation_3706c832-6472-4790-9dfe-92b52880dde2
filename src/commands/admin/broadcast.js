/**
 * @file BROADCAST.JS
 *
 * @version 3.0.4
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Slash command to broadcast a direct message to all server co-workers.
 * Allows administrators to send a mass message to all users, with optional role filtering.
 * Supports embed customization, preview mode, rate limiting, and robust error handling.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import {
  EmbedBuilder,
  MessageFlags,
  PermissionFlagsBits,
  SlashCommandBuilder,
} from 'discord.js';

import { botConfig } from '../../config/config.js';
import { messages } from '../../config/messages.js';
import { CommandHandler } from '../../middleware/commandHandler.js';
import * as cooldown from '../../utils/cooldown.js';
import { handleError } from '../../utils/errorHandler.js';
import logger from '../../utils/logger.js';
import { getRateLimiter } from '../../utils/rateLimit.js';
import { safeReply } from '../../utils/replyHelpers.js';

// Create rate limiter for DM sending
const limiter = getRateLimiter('broadcast-dm', {
  maxConcurrent: 5,
  minTime: 100,
});

// ------------ COMMAND DATA
const data = new SlashCommandBuilder()
  .setName('broadcast')
  .setDescription('Send a direct message to all server members')
  .addStringOption(option =>
    option
      .setName('message')
      .setDescription('The message to broadcast')
      .setRequired(true)
      .setMaxLength(2000)
  )
  .addRoleOption(option =>
    option
      .setName('role')
      .setDescription('Only send to members with this role')
      .setRequired(false)
  )
  .addBooleanOption(option =>
    option
      .setName('preview')
      .setDescription('Preview the message without sending')
      .setRequired(false)
  )
  .addStringOption(option =>
    option
      .setName('title')
      .setDescription('Embed title (optional)')
      .setRequired(false)
      .setMaxLength(256)
  )
  .addStringOption(option =>
    option
      .setName('author')
      .setDescription('Embed author (optional)')
      .setRequired(false)
      .setMaxLength(256)
  )
  .addStringOption(option =>
    option
      .setName('footer')
      .setDescription('Embed footer (optional)')
      .setRequired(false)
      .setMaxLength(2048)
  )
  .addBooleanOption(option =>
    option
      .setName('timestamp')
      .setDescription('Add timestamp to embed')
      .setRequired(false)
  )
  .addStringOption(option =>
    option
      .setName('color')
      .setDescription('Embed color (hex code)')
      .setRequired(false)
      .setMaxLength(7)
  )
  .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
  .setDMPermission(false);

// ------------ HELPER FUNCTIONS

/**
 * Validates broadcast inputs.
 * @param {any} interaction - The command interaction
 * @param {string} content - Message content
 * @param {string|null} color - Embed color
 * @param {any} role - Role to filter members
 * @returns {Promise<{valid: boolean, error?: string}>}
 */
async function validateBroadcastInputs(interaction, content, color, role) {
  if (!content || content.trim().length === 0) {
    return { valid: false, error: 'Message content cannot be empty.' };
  }

  if (content.length > 2000) {
    return { valid: false, error: 'Message content exceeds 2000 characters.' };
  }

  if (color && !/^#?[0-9A-Fa-f]{6}$/.test(color)) {
    return {
      valid: false,
      error: 'Invalid color format. Use hex format (e.g., #FF0000).',
    };
  }

  if (role && !interaction.guild?.roles.cache.has(role.id)) {
    return { valid: false, error: 'Invalid role specified.' };
  }

  return { valid: true };
}

/**
 * Checks broadcast rate limit.
 * @param {any} interaction - The command interaction
 * @returns {Promise<{limited: boolean, error?: string}>}
 */
async function checkBroadcastRateLimit(interaction) {
  const rateLimitKey = `broadcast_rate:${interaction.user.id}`;
  const rateLimitWindow = 3600000; // 1 hour

  if (!cooldown.isExpired(rateLimitKey, rateLimitWindow)) {
    const remaining = cooldown.getRemaining(rateLimitKey, rateLimitWindow);
    return {
      limited: true,
      error: `Rate limit active. Try again in ${Math.ceil(remaining / 60000)} minutes.`,
    };
  }
  return { limited: false };
}

/**
 * Sends a broadcast preview.
 * @param {any} interaction - The command interaction
 * @param {any} previewOptions - Preview options object
 * @returns {Promise<void>}
 */
async function sendBroadcastPreview(interaction, previewOptions) {
  const { content, title, author, footer, timestamp, color } = previewOptions;
  const isEmbed = Boolean(title || author || footer || color || timestamp);

  if (isEmbed) {
    const embed = new EmbedBuilder().setDescription(content);
    if (title) embed.setTitle(title);
    if (author) embed.setAuthor({ name: author });
    if (footer) embed.setFooter({ text: footer });
    if (timestamp) embed.setTimestamp();
    if (color) {
      try {
        embed.setColor(
          parseInt(color.startsWith('#') ? color.slice(1) : color, 16)
        );
      } catch {
        logger.warn(`Invalid color '${color}' provided for preview`);
      }
    }

    await safeReply(interaction, {
      content: '📋 **PREVIEW MODE** - This is how your broadcast will look:',
      embeds: [embed],
      flags: [MessageFlags.Ephemeral],
    });
  } else {
    await safeReply(interaction, {
      content: `📋 **PREVIEW MODE** - This is how your broadcast will look:\n\n${content}`,
      flags: [MessageFlags.Ephemeral],
    });
  }
}

/**
 * Executes the actual broadcast to guild members.
 * @param {any} interaction - The command interaction
 * @param {any} options - Broadcast options
 * @returns {Promise<void>}
 */
async function executeBroadcast(interaction, options) {
  const { content, title, author, footer, timestamp, color, role } = options;
  const cooldownKey = `broadcast:${interaction.user.id}`;
  const broadcastCooldown = Number(botConfig.cooldowns.broadcast);

  // Check cooldown
  if (!cooldown.isExpired(cooldownKey, broadcastCooldown)) {
    return await safeReply(interaction, {
      content: messages.errors.cooldown.replace(
        '{time}',
        String(
          Math.ceil(
            cooldown.getRemaining(cooldownKey, broadcastCooldown) / 1000
          )
        )
      ),
      flags: [MessageFlags.Ephemeral],
    });
  }

  // Get guild and members
  const guild = interaction.guild;
  if (!guild) {
    return await safeReply(interaction, {
      content: 'Guild not found for this command.',
      flags: [MessageFlags.Ephemeral],
    });
  }

  await guild.members.fetch();
  let members = guild.members.cache.filter(
    /** @param {any} m */ m => !m.user.bot
  );

  if (role) {
    members = members.filter(
      /** @param {any} m */ m => m.roles.cache.has(role.id)
    );
    if (members.size === 0) {
      return await safeReply(interaction, {
        content: messages.admin.broadcastErrors.noRoleMembers,
        flags: [MessageFlags.Ephemeral],
      });
    }
  }

  const memberArray = members
    .map(/** @param {any} m */ m => m.user)
    .sort(() => Math.random() - 0.5);
  const total = memberArray.length;

  await safeReply(interaction, {
    content: `📤 Starting broadcast to ${total} members...`,
    flags: [MessageFlags.Ephemeral],
  });

  let success = 0;
  let failed = 0;
  /** @type {Array<{id: string, tag: string, error: string}>} */
  const failedUsers = [];

  const isEmbed = Boolean(title || author || footer || color || timestamp);

  const promises = memberArray.map(
    /** @param {any} user */ user =>
      limiter.schedule(async () => {
        try {
          const dmChannel = await user.createDM();
          if (isEmbed) {
            const embed = new EmbedBuilder().setDescription(content);
            if (title) embed.setTitle(title);
            if (author) embed.setAuthor({ name: author });
            if (footer) embed.setFooter({ text: footer });
            if (timestamp) embed.setTimestamp();
            if (color) {
              try {
                embed.setColor(
                  parseInt(color.startsWith('#') ? color.slice(1) : color, 16)
                );
              } catch (e) {
                logger.warn(`Invalid color '${color}' provided`, String(e));
              }
            }
            await dmChannel.send({ embeds: [embed] });
          } else {
            await dmChannel.send(content);
          }
          success++;
        } catch (e) {
          const err = e instanceof Error ? e : new Error(String(e));
          failed++;
          failedUsers.push({ id: user.id, tag: user.tag, error: err.message });
          logger.warn(`Failed to DM ${user.tag}: ${err.message}`);
        }
      })
  );

  await Promise.allSettled(promises);

  let result = messages.admin.broadcastErrors.finalReport
    .replace('{success}', String(success))
    .replace('{failed}', String(failed));

  if (failed > 0) {
    result += `\nFailed IDs: ${failedUsers
      .slice(0, 10)
      .map(u => u.id)
      .join(', ')}${failedUsers.length > 10 ? '...' : ''}`;
  }

  cooldown.set(cooldownKey);

  await safeReply(interaction, {
    content: result,
    flags: [MessageFlags.Ephemeral],
  });
}

// ------------ MAIN EXECUTE FUNCTION
/**
 * Executes the broadcast command.
 * @param {any} interaction - The command interaction object
 * @param {any} client - The Discord client instance
 * @returns {Promise<void>}
 */
async function _execute(interaction, client) {
  try {
    const content = interaction.options.getString('message');
    const role = interaction.options.getRole('role');
    const preview = interaction.options.getBoolean('preview') ?? false;

    // Ensure content is not null
    if (!content) {
      return await safeReply(interaction, {
        content: 'Message content is required.',
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Rate limit check for broadcast command
    const rateCheck = await checkBroadcastRateLimit(interaction);
    if (rateCheck.limited) {
      return await safeReply(interaction, {
        content: rateCheck.error,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // Retrieve embed options
    const title = interaction.options.getString('title')?.trim();
    const author = interaction.options.getString('author')?.trim();
    const footer = interaction.options.getString('footer')?.trim();
    const timestamp = interaction.options.getBoolean('timestamp');
    const color = interaction.options.getString('color')?.trim();

    // Validate inputs
    const validation = await validateBroadcastInputs(
      interaction,
      content,
      color,
      role
    );
    if (!validation.valid) {
      return await safeReply(interaction, {
        content: validation.error,
        flags: [MessageFlags.Ephemeral],
      });
    }

    // PREVIEW MODE
    if (preview) {
      return await sendBroadcastPreview(interaction, {
        content,
        title,
        author,
        footer,
        timestamp,
        color,
      });
    }

    // BROADCAST EXECUTION
    return await executeBroadcast(interaction, {
      content,
      title,
      author,
      footer,
      timestamp,
      color,
      role,
    });
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    await handleError(err, { command: 'broadcast' }, client, {
      critical: true,
      notifyAdmins: true,
    });
    await safeReply(interaction, {
      content: messages.errors.internalError,
      flags: [MessageFlags.Ephemeral],
    });
  }
}

export default {
  data,
  execute: CommandHandler.admin('broadcast', _execute),
  manualCooldown: true,
};
