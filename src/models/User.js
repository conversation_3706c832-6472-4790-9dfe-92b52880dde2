/**
 * @file USER.JS
 *
 * @version 3.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * User model with encryption for data security, providing
 * methods for creating, updating, and managing user information.
 * Encrypts sensitive data like email addresses using AES-256-GCM.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ I<PERSON><PERSON><PERSON>
import crypto from 'crypto';

import { logger } from '@sentry/node';

import { botConfig } from '../config/config.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {Object} UserData
 * @property {string} id - Discord user ID
 * @property {string} tag - Discord user tag (username#discriminator)
 * @property {string} [email] - User's email address
 * @property {string} [nickname] - User's nickname
 * @property {string} [robloxUsername] - User's <PERSON>lo<PERSON> username
 * @property {boolean} [verified] - Whether the user is verified
 * @property {Date} [joinedAt] - When the user joined
 * @property {Date|null} [verifiedAt] - When the user was verified
 * @property {Date|null} [onboardingStartTime] - When the onboarding started
 * @property {Object} [metadata] - Additional metadata
 * @property {string} [onboardingStatus] - User's onboarding status
 * @property {Date} [lastInteraction] - User's last interaction time
 * @property {string} [username] - Discord username
 * @property {string} [displayName] - Discord display name
 * @property {string|null} [timeZone] - User's timezone
 */

// ------------ ENCRYPTION CONFIGURATION
/**
 * Accept both base64 and hex keys for encryption.
 * @param {string} key
 * @returns {Buffer}
 */
function decodeKey(key) {
  if (!key) throw new Error('Encryption key is missing');
  if (/^[A-Fa-f0-9]{64}$/.test(key)) {
    return Buffer.from(key, 'hex');
  }
  return Buffer.from(key, 'base64');
}

const ENCRYPTION_KEY = decodeKey(
  process.env.USER_DATA_ENCRYPTION_KEY || botConfig.encryption.userData
);

// Confirm the encryption key is loaded
console.log(
  '[User.js] ENCRYPTION_KEY loaded:',
  ENCRYPTION_KEY && ENCRYPTION_KEY.length,
  'bytes'
);

if (ENCRYPTION_KEY.length !== 32) {
  throw new Error(
    'Invalid encryption key length; must be 32 bytes for AES-256-GCM'
  );
}

/**
 * Encrypts text using AES-256-GCM algorithm.
 * @param {string} text - Plain text to encrypt
 * @returns {string|null} - Encrypted text in format 'iv:authTag:ciphertext' or null if input is empty
 */
function encrypt(text) {
  if (!text) return null;
  try {
    const iv = crypto.randomBytes(12);
    const cipher = crypto.createCipheriv('aes-256-gcm', ENCRYPTION_KEY, iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    const authTag = cipher.getAuthTag().toString('hex');
    return `${iv.toString('hex')}:${authTag}:${encrypted}`;
  } catch (err) {
    console.error('[User.js] Encryption failed:', err);
    return null;
  }
}

/**
 * Decrypts text encrypted with AES-256-GCM algorithm.
 * @param {string} text - Encrypted text in format 'iv:authTag:ciphertext'
 * @returns {string|null} - Decrypted text or null if input is empty, authentication fails, or decryption error occurs
 */
export function decrypt(text) {
  if (!text) return null;
  try {
    const [ivHex, authTagHex, encrypted] = text.split(':');
    const iv = Buffer.from(ivHex, 'hex');
    const authTag = Buffer.from(authTagHex, 'hex');
    const decipher = crypto.createDecipheriv('aes-256-gcm', ENCRYPTION_KEY, iv);
    decipher.setAuthTag(authTag);
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  } catch (error) {
    logger.error('Decryption failed:', error);
    return null;
  }
}

/**
 * Parse JSON or return original value if parsing fails.
 * @param {string|Object} value - JSON string to parse
 * @returns {any}
 */
function parseJSON(value) {
  if (typeof value !== 'string') return value;
  try {
    return JSON.parse(value);
  } catch (error) {
    logger.error('Failed to parse JSON:', error);
    return value;
  }
}

// ------------ USER CLASS

/**
 * User class representing a Discord user in the system.
 * Handles data validation, encryption, and formatting.
 */
export class User {
  /**
   * Create a new User instance.
   * @param {UserData} userData - User data object
   */
  constructor(userData) {
    if (!userData.id) throw new Error('User id is required for User model');

    /** @type {string} */
    this.id = userData.id;

    /** @type {string} */
    this.tag = userData.tag;

    /** @type {string|null} */
    this.email = userData.email || null;

    /** @type {string|null} */
    this.nickname = userData.nickname || null;

    /** @type {string|null} */
    this.robloxUsername = userData.robloxUsername || null;

    /** @type {boolean} */
    this.verified = Boolean(userData.verified);

    /** @type {Date} */
    this.joinedAt = userData.joinedAt || new Date();

    /** @type {Date|null} */
    this.verifiedAt = userData.verifiedAt || null;

    /** @type {Date|null} */
    this.onboardingStartTime = userData.onboardingStartTime || null;

    /** @type {Object} */
    this.metadata = userData.metadata || {};

    /** @type {string} */
    this.onboardingStatus = userData.onboardingStatus || 'pending';

    /** @type {Date} */
    this.lastInteraction = userData.lastInteraction || new Date();

    /** @type {string|null} */
    this.username = userData.username || null;

    /** @type {string|null} */
    this.displayName = userData.displayName || null;

    /** @type {string|null} */
    this.timeZone = userData.timeZone || null;
  }

  /**
   * Convert User instance to a plain object for storage.
   * Encrypts sensitive data before storage.
   * @returns {Object} Plain object representation of the user with encrypted fields
   */
  toObject() {
    return {
      id_dis_internal_workers: this.id,
      tag: this.tag,
      email: this.email ? encrypt(this.email) : null,
      nickname: this.nickname || null,
      robloxUsername: this.robloxUsername || null,
      timeZone: this.timeZone || null,
      verified: this.verified ? 1 : 0,
      joinedAt: this.joinedAt || new Date(),
      verifiedAt: this.verifiedAt || null,
      onboardingStartTime: this.onboardingStartTime || null,
      metadata: this.metadata || {},
      onboardingStatus: this.onboardingStatus || 'pending',
      lastInteraction: this.lastInteraction || new Date(),
    };
  }

  /**
   * Update user data with new values.
   * Always updates last interaction time.
   * @param {Object} updates - Object containing fields to update
   * @returns {User} Updated User instance
   */
  update(updates) {
    for (const [key, value] of Object.entries(updates)) {
      switch (key) {
        case 'tag':
          this.tag = value;
          break;
        case 'email':
          this.email = value;
          break;
        case 'nickname':
          this.nickname = value;
          break;
        case 'robloxUsername':
          this.robloxUsername = value;
          break;
        case 'verified':
          this.verified = value;
          break;
        case 'joinedAt':
          this.joinedAt = value;
          break;
        case 'verifiedAt':
          this.verifiedAt = value;
          break;
        case 'onboardingStartTime':
          this.onboardingStartTime = value;
          break;
        case 'metadata':
          this.metadata = value;
          break;
        case 'onboardingStatus':
          this.onboardingStatus = value;
          break;
        case 'username':
          this.username = value;
          break;
        case 'displayName':
          this.displayName = value;
          break;
        case 'timeZone':
          this.timeZone = value;
          break;
        default:
          // Ignore unknown fields
          break;
      }
    }
    this.lastInteraction = new Date();
    return this;
  }

  /**
   * Mark user as verified and update verification metadata.
   * @returns {User} Updated User instance
   */
  verify() {
    this.verified = true;
    this.verifiedAt = new Date();
    this.onboardingStatus = 'completed';
    return this;
  }

  /**
   * Create a User instance from MySQL data.
   * Handles MySQL date formats and JSON parsing.
   * @param {Object} data - Data from MySQL
   * @returns {User}
   */
  static fromMySQL(data) {
    const userData = {
      ...data,
      id: data.id_dis_internal_workers,
      email: data.email ? decrypt(data.email) : null,
      nickname: data.nickname || null,
      robloxUsername: data.robloxUsername || null,
      timeZone: data.timeZone || null,
      verified: Boolean(data.verified),
      joinedAt:
        data.joinedAt instanceof Date ? data.joinedAt : new Date(data.joinedAt),
      verifiedAt: data.verifiedAt
        ? data.verifiedAt instanceof Date
          ? data.verifiedAt
          : new Date(data.verifiedAt)
        : null,
      onboardingStartTime: data.onboardingStartTime
        ? data.onboardingStartTime instanceof Date
          ? data.onboardingStartTime
          : new Date(data.onboardingStartTime)
        : null,
      metadata: parseJSON(data.metadata) || {},
      onboardingStatus: data.onboardingStatus || 'pending',
      lastInteraction: data.lastInteraction
        ? data.lastInteraction instanceof Date
          ? data.lastInteraction
          : new Date(data.lastInteraction)
        : new Date(),
    };
    return new User(userData);
  }

  /**
   * Create a User instance from a Discord.js GuildMember.
   * @param {import('discord.js').GuildMember} member - Discord.js GuildMember instance
   * @returns {User}
   */
  static fromDiscordMember(member) {
    return new User({
      id: member.id,
      tag: member.user.tag,
      username: member.user.username,
      displayName: member.displayName,
      joinedAt: member.joinedAt || new Date(),
      email: undefined,
      nickname: undefined,
      robloxUsername: undefined,
      verified: false,
      verifiedAt: null,
      onboardingStartTime: null,
      metadata: {},
      onboardingStatus: 'pending',
      lastInteraction: new Date(),
      timeZone: null,
    });
  }
}

// ------------ EXPORT
export default User;
