[{"prompt": "👋 Welcome to Dynamic Innovative Studio!\nTo get started, please reply with your **email address**.", "embed": {"title": "Welcome to Dynamic Innovative Studio", "color": "#ccf3ff", "footer": "Please respond to this message to continue"}, "validation": "email", "field": "email", "saveToUser": true, "nextStep": 1}, {"prompt": "You entered **{email}**. Is this correct? (yes/no)", "validation": "yesno", "confirmation": true, "onRejectStep": 0, "nextStep": 2}, {"prompt": "What would you like us to **call you**? (e.g. nickname, first name, alias)", "validation": "nickname", "field": "nickname", "saveToUser": true, "nextStep": 3}, {"prompt": "Which **time zone** are you in? (e.g. UTC+01:00) — this helps us coordinate better.", "validation": "timezone", "field": "timeZone", "saveToUser": true, "nextStep": 4}, {"prompt": "Please enter your **Roblox username**, or type 'skip' if you don't have one.", "validation": "robloxUsername", "field": "robloxUsername", "saveToUser": true, "nextStep": 5}, {"prompt": "You entered **{robloxUsername}**. Is this correct? (yes/no)", "validation": "yesno", "saveToUser": false, "confirmation": true, "onRejectStep": 4, "nextStep": 6}, {"prompt": "Thank you for providing your information! We will use it to give you access to our resources and support! **Reply with a thanks** to finish.", "validation": null, "complete": true}]