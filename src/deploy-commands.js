/**
 * @file DEPLOY COMMANDS.JS
 *
 * @version 2.0.3
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Registers all slash commands with the Discord API.
 * Scans the commands directory and deploys all valid command definitions.
 * Should be run manually when commands are added/changed.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').RESTPostAPIApplicationCommandsJSONBody} Command
 */
/**
 * @typedef {import('discord.js').REST} RESTClient
 * @typedef {import('discord.js').RESTPostAPIApplicationCommandsResult} RESTResponse
 */
// ------------ IMPORTS
import 'dotenv/config';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

import { REST, Routes } from 'discord.js';

import { botConfig } from './config/config.js';
import logger from './utils/logger.js';

// ------------ ENVIRONMENT & VALIDATION
const { APPLICATION_ID, TOKEN } = botConfig.env;

// Validate required environment variables
if (!APPLICATION_ID || !TOKEN) {
  logger.error(
    '❌ Missing required environment variables (APPLICATION_ID or TOKEN)'
  );
  process.exit(1);
}

// ------------ PATH RESOLUTION
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const commandsPath = path.resolve(__dirname, 'commands');

// ------------ COMMANDS REGISTRATION
/** @type {import('discord.js').RESTPostAPIApplicationCommandsJSONBody[]} */
const commands = [];

/**
 * Validates and processes a single command file.
 * @param {string} filePath - Path to the command file
 * @param {string} fileName - Name of the command file
 * @returns {Promise<void>}
 */
async function processCommandFile(filePath, fileName) {
  try {
    // Dynamic import for ESM compatibility
    const commandModule = await import(`file://${filePath}`);
    const command = commandModule.default;

    // Validate the command has required properties
    if (
      !command ||
      !command.data ||
      typeof command.data.toJSON !== 'function'
    ) {
      logger.warn(`⚠️ Command at ${fileName} is missing required properties`);
      return;
    }

    commands.push(command.data.toJSON());
    logger.info(`✅ Loaded command: ${command.data.name}`);
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error(`❌ Failed to load command ${fileName}:`, err);
  }
}

/**
 * Loads all command files from the commands directory structure.
 * Commands are organized in category folders.
 * @returns {Promise<void>}
 */
async function loadCommands() {
  try {
    // Ensure commands directory exists
    if (!fs.existsSync(commandsPath)) {
      logger.error(`Commands directory not found at: ${commandsPath}`);
      return;
    }

    // Get all category directories (only allow safe folder names)
    const allEntries = fs.readdirSync(commandsPath, { withFileTypes: true });
    const categories = allEntries
      .filter(
        // allow only safe folder names
        dirent => dirent.isDirectory() && /^[\w-]+$/.test(dirent.name)
      )
      .map(dirent => dirent.name);

    for (const category of categories) {
      // Defensive: ensure category is a safe string
      if (!/^[\w-]+$/.test(category)) continue;
      const categoryPath = path.join(commandsPath, category);

      // Defensive: check directory again before statSync
      // Additional safety: ensure path is within expected bounds
      if (!categoryPath.startsWith(commandsPath)) continue;
      // eslint-disable-next-line security/detect-non-literal-fs-filename
      if (!fs.existsSync(categoryPath)) continue;
      // eslint-disable-next-line security/detect-non-literal-fs-filename
      const stat = fs.statSync(categoryPath);
      if (!stat.isDirectory()) continue;

      // Only allow .js files with safe names
      // Additional safety: ensure path is still within bounds
      if (!categoryPath.startsWith(commandsPath)) continue;
      // eslint-disable-next-line security/detect-non-literal-fs-filename
      const fileDirents = fs.readdirSync(categoryPath, { withFileTypes: true });
      const files = fileDirents
        .filter(dirent => dirent.isFile() && /^[\w.-]+\.js$/.test(dirent.name))
        .map(dirent => dirent.name);

      logger.info(
        `Loading ${files.length} commands from category: ${category}`
      );

      // Process each command file
      for (const file of files) {
        const filePath = path.join(categoryPath, file);
        await processCommandFile(filePath, file);
      }
    }
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Failed to load commands:', err);
  }
}

// ------------ COMMAND REGISTRATION

/**
 * Registers all loaded commands with the Discord API.
 * @returns {Promise<void>}
 */
async function registerCommands() {
  // Initialize REST API client with version and token
  const rest = new REST({ version: '10' }).setToken(TOKEN);

  try {
    logger.info(
      `Started refreshing ${commands.length} application (/) commands.`
    );

    // API request to update the commands
    const response = await rest.put(
      Routes.applicationCommands(APPLICATION_ID),
      { body: commands }
    );

    logger.info(
      `Successfully reloaded ${Array.isArray(response) ? response.length : 0} application (/) commands.`
    );
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error registering commands with Discord API:', err);
  }
}

// ------------ MAIN EXECUTION FLOW
(async () => {
  try {
    await loadCommands();
    await registerCommands();
  } catch (error) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Fatal error during command deployment:', err);
    process.exit(1);
  }
})();
