/**
 * @file HELP.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Utility for generating the correct help embed for a Discord member
 * based on their roles (admin, HR, or member).
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { EmbedBuilder } from 'discord.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').GuildMember} GuildMember
 * @typedef {import('../config/messages.js').Messages} Messages
 * @typedef {import('../config/config.js').RoleConfig} RoleConfig
 */

/**
 * Returns the appropriate help embed for a member based on their roles.
 * @param {GuildMember} member - The Discord GuildMember object.
 * @param {RoleConfig} roles - The botConfig.roles object.
 * @param {Messages} messages - The messages object.
 * @returns {EmbedBuilder}
 */
export function getHelpEmbedForMember(member, roles, messages) {
  /**
   * Normalizes the embed object to ensure the footer has a text property.
   * @param {Record<string, any>} embed
   * @returns {Record<string, any>}
   */
  function normalizeEmbed(embed) {
    return {
      ...embed,
      color: Number(`0x${embed.color.replace('#', '')}`),
      footer:
        embed.footer &&
        typeof embed.footer === 'object' &&
        'text' in embed.footer
          ? embed.footer
          : { text: embed.footer?.text || String(embed.footer) || '' },
    };
  }
  if (member?.roles?.cache?.has?.(roles?.admin)) {
    const helpEmbed = messages.admin.helpEmbed;
    return new EmbedBuilder(normalizeEmbed(helpEmbed));
  }
  if (member?.roles?.cache?.has?.(roles?.hr)) {
    const helpEmbed = messages.hr.helpEmbed;
    return new EmbedBuilder(normalizeEmbed(helpEmbed));
  }
  const helpEmbed = messages.member.helpEmbed;
  return new EmbedBuilder(normalizeEmbed(helpEmbed));
}
