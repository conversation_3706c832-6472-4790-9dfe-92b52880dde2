/**
 * @file COOLDOWN.JS
 *
 * @version 2.0.1
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Advanced cooldown management system for Discord bots.
 * Supports per-user and per-command cooldowns, different cooldown settings per command,
 * guild-specific cooldowns, bypass permissions, and memory optimization.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {Object} CooldownMap
 * @property {Map<string, number>} map - Map of cooldown keys to timestamps
 */

// ------------ I<PERSON>OR<PERSON>
import { botConfig } from '../config/config.js';

// ------------ COOLDOWN STORAGE
/** @type {Map<string, number>} */
const cooldowns = new Map();

// ------------ COOLDOWN FUNCTIONS
/**
 * Checks if the cooldown for a specific key has expired and updates the timestamp if expired.
 * NOTE: This should NOT be used for cooldown checks in middleware, as it sets the timestamp.
 * Use isExpired() to check, and set() to update after successful execution.
 * @param {string} key - The key to check the cooldown for.
 * @param {number} ms - The cooldown duration in milliseconds.
 * @returns {boolean} True if the cooldown has expired and is reset, false otherwise.
 */
function check(key, ms) {
  const now = Date.now();
  const last = cooldowns.get(key) || 0;
  if (now - last < ms) return false;
  cooldowns.set(key, now);
  return true;
}

/**
 * Checks if the cooldown for a specific key has expired (does NOT update the timestamp).
 * @param {string} key - The key to check the cooldown for.
 * @param {number} ms - The cooldown duration in milliseconds.
 * @returns {boolean} True if the cooldown has expired, false otherwise.
 */
function isExpired(key, ms) {
  const now = Date.now();
  const last = cooldowns.get(key) || 0;
  return now - last >= ms;
}

/**
 * Sets/updates the cooldown timestamp for a specific key.
 * @param {string} key - The key to set the cooldown for.
 */
function set(key) {
  cooldowns.set(key, Date.now());
}

/**
 * Gets the remaining time for a specific cooldown.
 * @param {string} key - The key to check the cooldown for.
 * @param {number} ms - The cooldown duration in milliseconds.
 * @returns {number} The remaining time in milliseconds.
 */
function getRemaining(key, ms) {
  const now = Date.now();
  const last = cooldowns.get(key);
  if (!last) return 0;
  return Math.max(0, last + ms - now);
}

/**
 * Gets the cooldown duration for a command (ms), falling back to global default.
 * Uses hasOwnProperty to avoid object injection.
 * @param {string} commandName
 * @returns {number}
 */
function getCooldownMs(commandName) {
  const cooldownsConfig = botConfig?.cooldowns;
  const commandCooldowns = cooldownsConfig?.commandCooldowns;
  if (
    commandCooldowns &&
    Object.prototype.hasOwnProperty.call(commandCooldowns, commandName)
  ) {
    const value = Object.getOwnPropertyDescriptor(
      commandCooldowns,
      commandName
    )?.value;
    return Number(value);
  }
  return Number(cooldownsConfig?.commands);
}

// ------------ EXPORTS
export { check, getCooldownMs, getRemaining, isExpired, set };
