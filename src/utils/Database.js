/**
 * @file DATABASE.JS
 *
 * @version 3.0.1
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Handles database interactions for the bot, including connecting to the database,
 * executing queries, and managing user/message data. Provides retry logic, error
 * reporting, and utility methods for user and message persistence.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import * as Sentry from '@sentry/node';
import mysql from 'mysql2/promise';

import { botConfig } from '../config/config.js';
import { User } from '../models/User.js';

import logger, { markShuttingDown } from './logger.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('../models/Message.js').Message} Message
 */

// ------------ <PERSON><PERSON><PERSON>ER FUNCTIONS
/**
 * Retry wrapper for async DB operations.
 * @param {Function} fn - Function to retry
 * @param {number} [retries=3] - Number of retries
 * @param {number} [delayMs=500] - Delay between retries in ms
 * @returns {Promise<any>}
 * @throws {Error}
 */
async function retryAsync(fn, retries = 3, delayMs = 500) {
  let lastErr;
  for (let i = 0; i < retries; i++) {
    try {
      return await fn();
    } catch (err) {
      lastErr = err;
      if (i < retries - 1) await new Promise(res => setTimeout(res, delayMs));
    }
  }
  throw lastErr;
}

/**
 * Alert admins on persistent DB errors.
 * @param {string} message
 * @param {Error|unknown} error
 * @returns {Promise<void>}
 */
async function alertAdmins(message, error) {
  const errorMessage =
    error instanceof Error
      ? error.message
      : typeof error === 'string'
        ? error
        : 'Unknown error';

  logger.error(
    `❗ **Persistent DB Error:** ${message}\n\`\`\`${errorMessage}\`\`\``
  );
}

// ------------ SINGLETON CLASS FOR DATABASE OPERATIONS
class Database {
  constructor() {
    if (Database.instance) {
      return Database.instance;
    }
    this.pool = null;
    this.initializePool();
    Database.instance = this;
  }

  /**
   * Initialize MySQL connection pool.
   * @returns {Promise<void>}
   */
  async initializePool() {
    try {
      this.pool = mysql.createPool({
        user: botConfig.database.dbUser,
        database: botConfig.database.dbName,
        password: botConfig.database.dbPassword,
        host: botConfig.database.dbHost,
        port: parseInt(botConfig.database.dbPort),
        waitForConnections: botConfig.database.waitForConnections,
        connectionLimit: botConfig.database.connectionLimit,
        queueLimit: botConfig.database.queueLimit,
      });
      await this.pool.execute('SELECT 1');
      logger.info('MySQL connection established successfully');
    } catch (error) {
      const errorToLog =
        error instanceof Error ? error : new Error(String(error));
      logger.error('Failed to initialize MySQL connection:', errorToLog);
      Sentry.captureException(errorToLog);
      throw errorToLog;
    }
  }

  /**
   * Execute a SQL query with retry logic.
   * @param {string} sql
   * @param {Array<any>} [params=[]]
   * @returns {Promise<Array<any>>}
   */
  async query(sql, params = []) {
    try {
      return await retryAsync(async () => {
        if (!this.pool) await this.initializePool();
        if (!this.pool) {
          throw new Error('Database connection pool is not initialized.');
        }
        const sanitizedParams = params.map(param =>
          param === undefined ? null : param
        );
        const [rows] = await this.pool.execute(sql, sanitizedParams);
        return rows;
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error : new Error(String(error));
      logger.error(`Error executing query: ${sql}`, { err: errorMessage });
      Sentry.captureException(errorMessage);
      throw errorMessage;
    }
  }

  /**
   * Prepare date for MySQL.
   * @param {any} value
   * @returns {any}
   */
  _prepareDateForMySQL(value) {
    if (value instanceof Date) {
      // Check if date is valid before calling toISOString()
      if (!isNaN(value.getTime())) {
        return value.toISOString().slice(0, 19).replace('T', ' ');
      }
      // Return current time if date is invalid
      return new Date().toISOString().slice(0, 19).replace('T', ' ');
    }
    return value;
  }

  /**
   * Prepare data for MySQL insertion/update.
   * Uses Object.prototype.hasOwnProperty to avoid object injection.
   * @param {Record<string, any>} data
   * @returns {Record<string, any>}
   */
  _prepareForMySQL(data) {
    const result = {};
    // Use for...in loop to avoid array access security issues
    for (const key in data) {
      if (!Object.prototype.hasOwnProperty.call(data, key)) continue;

      // Use safe property access to avoid object injection
      const hasProperty = Object.prototype.hasOwnProperty.call(data, key);
      const value = hasProperty
        ? Object.getOwnPropertyDescriptor(data, key)?.value
        : undefined;

      if (value === undefined) {
        Object.defineProperty(result, key, {
          value: null,
          writable: true,
          enumerable: true,
          configurable: true,
        });
        continue;
      }

      // Prepare date for MySQL
      if (value instanceof Date) {
        Object.defineProperty(result, key, {
          value: this._prepareDateForMySQL(value),
          writable: true,
          enumerable: true,
          configurable: true,
        });
      } else if (
        typeof value === 'object' &&
        value !== null &&
        !(value instanceof Date)
      ) {
        Object.defineProperty(result, key, {
          value: JSON.stringify(value),
          writable: true,
          enumerable: true,
          configurable: true,
        });
      } else {
        Object.defineProperty(result, key, {
          value: value,
          writable: true,
          enumerable: true,
          configurable: true,
        });
      }
    }
    return result;
  }

  // ------------ USER OPERATIONS
  /**
   * Get a user by Discord ID.
   * @param {string} userId
   * @returns {Promise<User|null>}
   */
  async getUser(userId) {
    try {
      return await retryAsync(async () => {
        const rows = await this.query(
          'SELECT * FROM dis_internal_workers WHERE id_dis_internal_workers = ?',
          [userId]
        );
        if (rows.length > 0) {
          return User.fromMySQL(rows[0]);
        }
        return null;
      });
    } catch (error) {
      const errorToLog =
        error instanceof Error ? error : new Error(String(error));
      logger.error(`Error getting user ${userId}:`, errorToLog);
      await alertAdmins(`getUser failed for ${userId}`, errorToLog);
      Sentry.captureException(errorToLog);
      throw errorToLog;
    }
  }

  /**
   * Save a user to the database.
   * @param {User} user
   * @returns {Promise<void>}
   */
  async saveUser(user) {
    try {
      const userData = this._prepareForMySQL(user.toObject());
      if (!userData.id_dis_internal_workers) {
        logger.error(
          'Attempting to save user with null id_dis_internal_workers',
          { userData }
        );
        throw new Error('id_dis_internal_workers cannot be null');
      }

      // Validation for critical date fields
      ['verifiedAt', 'lastInteraction', 'onboardingStartTime'].forEach(
        field => {
          // Use safe property access to avoid object injection
          const hasProperty = Object.prototype.hasOwnProperty.call(
            userData,
            field
          );
          const val = hasProperty
            ? Object.getOwnPropertyDescriptor(userData, field)?.value
            : undefined;

          if (
            (val instanceof Date && isNaN(val.getTime())) ||
            (typeof val === 'string' && isNaN(new Date(val).getTime()))
          ) {
            Object.defineProperty(userData, field, {
              value: new Date(),
              writable: true,
              enumerable: true,
              configurable: true,
            });
          }
        }
      );
      await retryAsync(async () => {
        const columns = Object.keys(userData);
        const values = Object.values(userData);
        const placeholders = columns.map(() => '?').join(', ');
        const updatePairs = columns.map(col => `${col} = ?`).join(', ');
        await this.query(
          `INSERT INTO dis_internal_workers (${columns.join(', ')}) VALUES (${placeholders})
                     ON DUPLICATE KEY UPDATE ${updatePairs}`,
          [...values, ...values]
        );
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error : new Error(String(error));
      Sentry.captureException(errorMessage);
      logger.error(`Error saving user ${user}:`, errorMessage);
      throw errorMessage;
    }
  }

  /**
   * Update a user in the database.
   * @param {string} userId
   * @param {Record<string, any>} updates
   * @returns {Promise<void>}
   */
  async updateUser(userId, updates) {
    try {
      await retryAsync(async () => {
        const preparedUpdates = this._prepareForMySQL(updates);
        const columns = Object.keys(preparedUpdates);
        const values = Object.values(preparedUpdates);
        const updatePairs = columns.map(col => `${col} = ?`).join(', ');
        await this.query(
          `UPDATE dis_internal_workers SET ${updatePairs} WHERE id_dis_internal_workers = ?`,
          [...values, userId]
        );
        logger.debug(
          `Successfully updated user ${userId} with fields: ${columns.join(', ')}`
        );
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error : new Error(String(error));
      logger.error(`Error updating user ${userId}:`, errorMessage);
      await alertAdmins(`updateUser failed for ${userId}`, error);
      Sentry.captureException(errorMessage);
      throw errorMessage;
    }
  }

  /**
   * Get workers by verification status.
   * @param {boolean} verified
   * @returns {Promise<Array<User>>}
   */
  async getUsersByVerificationStatus(verified) {
    try {
      return await retryAsync(async () => {
        const rows = await this.query(
          'SELECT * FROM dis_internal_workers WHERE verified = ?',
          [verified]
        );
        return rows.map(row => User.fromMySQL(row));
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error : new Error(String(error));
      logger.error(
        'Error getting workers by verification status:',
        errorMessage
      );
      await alertAdmins('getUsersByVerificationStatus failed', error);
      Sentry.captureException(errorMessage);
      throw errorMessage;
    }
  }

  /**
   * Log user activity using Pino logger instead of database.
   * @param {Record<string, any>} activity
   * @returns {void}
   */
  logUserActivity(activity) {
    try {
      const { userId, action, timestamp = new Date(), details = {} } = activity;
      logger.info(
        {
          component: 'user_activity',
          userId,
          action,
          timestamp: timestamp.toISOString(),
          ...details,
        },
        `User ${userId} performed ${action}`
      );
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error : new Error(String(error));
      logger.error('Error logging user activity:', errorMessage);
    }
  }

  /**
   * Update a user's onboarding status.
   * @param {string} userId
   * @param {Record<string, any>} statusData
   * @returns {Promise<void>}
   */
  async updateUserOnboardingStatus(userId, statusData) {
    try {
      await retryAsync(async () => {
        const preparedData = this._prepareForMySQL(statusData);
        if (
          'onboardingStatus' in preparedData &&
          (preparedData.onboardingStatus === 'pending' ||
            preparedData.onboardingStatus === true)
        ) {
          preparedData.onboardingStartTime = new Date();
        }
        await this.updateUser(userId, preparedData);
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error : new Error(String(error));
      logger.error(
        `Error updating onboarding status for user ${userId}:`,
        errorMessage
      );
      await alertAdmins(
        `updateUserOnboardingStatus failed for ${userId}`,
        error
      );
      Sentry.captureException(errorMessage);
      throw errorMessage;
    }
  }

  /**
   * Get onboarding progress for all users (for admin dashboard/command).
   * @returns {Promise<Array<any>>} Array of user onboarding progress objects
   */
  async getAllOnboardingProgress() {
    try {
      return await this.query(
        'SELECT id_dis_internal_workers, tag, onboardingStatus, verified, onboardingStartTime, verifiedAt FROM dis_internal_workers ORDER BY onboardingStatus DESC, verified DESC, onboardingStartTime DESC'
      );
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error : new Error(String(error));
      logger.error(
        'Error fetching onboarding progress for all users:',
        errorMessage
      );
      throw errorMessage;
    }
  }

  /**
   * Close database connections - important for clean shutdown.
   * @returns {Promise<void>}
   */
  async close() {
    if (this.pool) {
      await this.pool.end();
      logger.info('Database connections closed');
    }
  }
}

/**
 * Graceful shutdown function.
 * @param {string} signal
 * @returns {Promise<void>}
 */
export async function gracefulShutdown(signal) {
  logger.info(`💤 Shutting down (${signal})...`);
  markShuttingDown();

  try {
    // Type assertion for global client
    /** @type {{ client?: { destroy?: () => void } }} */
    const globalClient = /** @type {any} */ (global);
    if (
      globalClient.client &&
      typeof globalClient.client.destroy === 'function'
    ) {
      globalClient.client.destroy();
    }
    if (typeof logger.flush === 'function') logger.flush();

    try {
      const onboardingService = (
        await import('../services/onboardingService.js')
      ).default;
      if (typeof onboardingService.shutdown === 'function')
        onboardingService.shutdown();
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : String(e);
      logger.warn('OnboardingService graceful shutdown failed:', errorMessage);
    }

    try {
      const db = (await import('./Database.js')).default;
      if (typeof db.close === 'function') await db.close();
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : String(e);
      logger.warn('Database close failed:', errorMessage);
    }

    await new Promise(res => setTimeout(res, 500));
    logger.info('Shutdown complete. Exiting.');
    process.exit(0);
  } catch (err) {
    const errorMessage = err instanceof Error ? err : new Error(String(err));
    logger.error('Error during graceful shutdown:', errorMessage);
    process.exit(1);
  }
}

// ------------ INSTANCE CREATION
/** @type {Database|undefined} */
Database.instance = undefined;

// ------------ EXPORT
export default new Database();
