/**
 * @file ERROR HANDLER.JS
 *
 * @version 3.0.1
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Centralized error handler for the bot.
 * Logs errors, notifies error log channel, and optionally notifies admins.
 * Provides a unified way to log errors, send notifications to a specific channel,
 * and optionally notify admins about critical errors.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import * as Sentry from '@sentry/node';
import { EmbedBuilder } from 'discord.js';

import { botConfig } from '../config/config.js';

import logger from './logger.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').Client} DiscordClient
 * @typedef {import('discord.js').TextChannel} TextChannel
 * @typedef {Object} ErrorHandlerContext
 * @property {string} [event] - Event name (e.g., 'messageCreate')
 * @property {string} [command] - Command name (e.g., 'help')
 * @property {string|number} [user] - User ID
 * @property {string|number} [guild] - Guild ID
 * @property {string} [stack] - Error stack trace
 * @property {string} [name] - Error name
 */

/**
 * @typedef {Object} ErrorHandlerOptions
 * @property {boolean} [critical=false] - Whether the error is critical
 * @property {boolean} [notifyAdmins=false] - Whether to notify admins about the error
 */

// ------------ MAIN ERROR HANDLER FUNCTION

/**
 * Formats an error message for Discord display.
 * @param {Error|string} error - The error to format
 * @returns {string} Formatted error message
 */
function formatErrorMessage(error) {
  if (typeof error === 'string') {
    return error;
  }
  if (error instanceof Error) {
    return error.stack || error.message || String(error);
  }
  return String(error);
}

/**
 * Creates embed fields from error context.
 * @param {string} errorMsg - Formatted error message
 * @param {ErrorHandlerContext} context - Error context
 * @returns {Array<{name: string, value: string, inline?: boolean}>} Embed fields
 */
function createErrorFields(errorMsg, context) {
  return [
    { name: 'Error', value: `\`\`\`\n${errorMsg.slice(0, 1000)}\n\`\`\`` },
    context.event
      ? { name: 'Event', value: String(context.event), inline: true }
      : null,
    context.command
      ? { name: 'Command', value: String(context.command), inline: true }
      : null,
    context.user
      ? { name: 'User', value: `<@${context.user}>`, inline: true }
      : null,
  ].filter(f => f !== null);
}

/**
 * Checks if a channel can receive messages.
 * @param {any} channel - Discord channel to check
 * @returns {boolean} True if channel can receive messages
 */
function canSendToChannel(channel) {
  return (
    channel &&
    typeof channel.send === 'function' &&
    !('children' in channel) &&
    !('recipients' in channel)
  );
}

/**
 * Sends admin notification for errors.
 * @param {DiscordClient} client - Discord client
 * @returns {Promise<void>}
 */
async function notifyAdmins(client) {
  const guild = client.guilds?.cache?.first();
  const adminRoleId = botConfig.roles.admin;

  if (!guild || !adminRoleId) return;

  const adminRole = guild.roles.cache.get(adminRoleId);
  const adminChannel = client.channels.cache.get(botConfig.channels.adminLog);

  if (adminChannel && adminRole && canSendToChannel(adminChannel)) {
    // Type assertion: canSendToChannel ensures channel has send method
    await /** @type {any} */ (adminChannel).send(
      `${adminRole} An error occurred. Please check the error log channel for details.`
    );
  }
}

/**
 * Centralized error handler for the bot.
 * Logs error to console and Sentry, sends to Discord error channel, and optionally notifies admins.
 *
 * @param {Error|string} error - The error object or message
 * @param {ErrorHandlerContext} [context={}] - Additional context (e.g., event, command, user)
 * @param {DiscordClient} [client=global.client] - Discord client instance
 * @param {ErrorHandlerOptions} [options={}] - Additional options for error handling
 * @returns {Promise<void>}
 */
export async function handleError(
  error,
  context = {},
  client = /** @type {any} */ (global).client,
  options = {}
) {
  // Standardize options with defaults
  const { critical = false, notifyAdmins: shouldNotifyAdmins = false } =
    options;

  // Log error to console and Sentry
  if (error instanceof Error) {
    logger.error(error.message, {
      ...context,
      stack: error.stack,
      name: error.name,
    });
  } else {
    logger.error(String(error), context);
  }

  // Report to Sentry
  if (Sentry.captureException) {
    Sentry.captureException(error);
  }

  // Skip Discord notifications if no client is provided
  if (!client) return;

  try {
    const errorMsg = formatErrorMessage(error);
    const fields = createErrorFields(errorMsg, context);

    // Create embed for error notification
    const errorEmbed = new EmbedBuilder()
      .setColor(parseInt(botConfig.colors.error.replace(/^#/, ''), 16))
      .setTitle(critical ? '⚠️ Critical Error' : '❌ Bot Error')
      .setDescription(
        critical
          ? 'A critical error occurred in the bot.'
          : 'An unexpected error occurred.'
      )
      .addFields(...fields)
      .setTimestamp();

    // Send to error log channel
    const channelId = botConfig.channels.errorLog;
    const channel = client.channels?.cache?.get(channelId);

    if (canSendToChannel(channel)) {
      // Type assertion: canSendToChannel ensures channel has send method
      await /** @type {any} */ (channel).send({ embeds: [errorEmbed] });
    }

    // Notify admins if requested
    if (shouldNotifyAdmins) {
      await notifyAdmins(client);
    }
  } catch (e) {
    // Handle error in error handler
    const err = e instanceof Error ? e : new Error(String(e));
    logger.error('Failed to process error notification:', err);
  }
}
