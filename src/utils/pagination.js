/**
 * @file PAGINATION.JS
 *
 * @version 1.0.1
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Pagination utility for Discord embeds with button navigation.
 * Handles page navigation, button interactions, and automatic cleanup.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import {
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  ComponentType,
  MessageFlags,
} from 'discord.js';

import logger from './logger.js';
import { safeReply } from './replyHelpers.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 * @typedef {import('discord.js').ButtonInteraction} ButtonInteraction
 * @typedef {import('discord.js').EmbedBuilder} EmbedBuilder
 * @typedef {import('discord.js').Message} Message
 * @typedef {import('discord.js').ActionRowBuilder<import('discord.js').ButtonBuilder>} ButtonActionRowBuilder
 */

// ------------ CONSTANTS
const PAGINATION_TIMEOUT = 300000; // 5 minutes

// ------------ PAGINATION CLASS
/**
 * Handles paginated embeds with button navigation.
 */
export class PaginationHandler {
  /**
   * @param {EmbedBuilder[]} pages - Array of embed pages
   * @param {ChatInputCommandInteraction} interaction - Discord interaction
   * @param {object} options - Pagination options
   * @param {boolean} [options.ephemeral=true] - Whether the message should be ephemeral
   * @param {number} [options.timeout=300000] - Timeout in milliseconds
   */
  constructor(pages, interaction, options = {}) {
    this.pages = pages;
    this.interaction = interaction;
    this.currentPage = 0;
    this.ephemeral = options.ephemeral ?? true;
    this.timeout = options.timeout ?? PAGINATION_TIMEOUT;
    this.collector = null;
    this.message = null;
  }

  /**
   * Creates navigation buttons for pagination.
   * @returns {ButtonActionRowBuilder}
   */
  createButtons() {
    /** @type {ButtonActionRowBuilder} */
    const row = new ActionRowBuilder();

    const previousButton = new ButtonBuilder()
      .setCustomId('pagination_previous')
      .setLabel('◀ Previous')
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(this.currentPage === 0);

    const pageButton = new ButtonBuilder()
      .setCustomId('pagination_page')
      .setLabel(`${this.currentPage + 1} / ${this.pages.length}`)
      .setStyle(ButtonStyle.Primary)
      .setDisabled(true);

    const nextButton = new ButtonBuilder()
      .setCustomId('pagination_next')
      .setLabel('Next ▶')
      .setStyle(ButtonStyle.Secondary)
      .setDisabled(this.currentPage === this.pages.length - 1);

    row.addComponents(previousButton, pageButton, nextButton);
    return row;
  }

  /**
   * Sends the paginated message.
   * @returns {Promise<void>}
   */
  async send() {
    try {
      // If only one page, send without buttons
      if (this.pages.length <= 1) {
        await safeReply(this.interaction, {
          embeds: [this.pages[0]],
          flags: this.ephemeral ? [MessageFlags.Ephemeral] : [],
        });
        return;
      }

      // Send initial message with buttons
      const response = await safeReply(this.interaction, {
        embeds: [this.pages[this.currentPage]],
        components: [this.createButtons()],
        flags: this.ephemeral ? [MessageFlags.Ephemeral] : [],
      });

      // Get the message object for interaction collection
      this.message =
        this.interaction.deferred || this.interaction.replied
          ? await this.interaction.fetchReply()
          : response;

      // Set up button interaction collector
      this.setupCollector();
    } catch (error) {
      logger.error(
        'Failed to send paginated message:',
        error instanceof Error ? error : new Error(String(error))
      );
      throw error;
    }
  }

  /**
   * Sets up the button interaction collector.
   */
  setupCollector() {
    if (!this.message) return;

    this.collector = this.message.createMessageComponentCollector({
      componentType: ComponentType.Button,
      time: this.timeout,
    });

    this.collector.on('collect', async buttonInteraction => {
      try {
        await this.handleButtonInteraction(buttonInteraction);
      } catch (error) {
        logger.error(
          'Error handling pagination button:',
          error instanceof Error ? error : new Error(String(error))
        );
        await buttonInteraction
          .reply({
            content: 'An error occurred while navigating pages.',
            ephemeral: true,
          })
          .catch(() => {});
      }
    });

    this.collector.on('end', async () => {
      try {
        await this.cleanup();
      } catch (error) {
        logger.error(
          'Error cleaning up pagination:',
          error instanceof Error ? error : new Error(String(error))
        );
      }
    });
  }

  /**
   * Handles button interactions for pagination.
   * @param {ButtonInteraction} buttonInteraction
   */
  async handleButtonInteraction(buttonInteraction) {
    // Check if the user who clicked is the same as the command user
    if (buttonInteraction.user.id !== this.interaction.user.id) {
      await buttonInteraction.reply({
        content: "You cannot navigate someone else's pagination.",
        ephemeral: true,
      });
      return;
    }

    // Handle navigation
    switch (buttonInteraction.customId) {
      case 'pagination_previous':
        if (this.currentPage > 0) {
          this.currentPage--;
        }
        break;
      case 'pagination_next':
        if (this.currentPage < this.pages.length - 1) {
          this.currentPage++;
        }
        break;
      default:
        return;
    }

    // Update the message
    await buttonInteraction.update({
      embeds: [this.pages[this.currentPage]],
      components: [this.createButtons()],
    });
  }

  /**
   * Cleans up the pagination by disabling buttons.
   */
  async cleanup() {
    if (!this.message) return;

    try {
      // Create disabled buttons
      /** @type {ButtonActionRowBuilder} */
      const disabledRow = new ActionRowBuilder();
      const buttons = this.createButtons().components.map(button =>
        ButtonBuilder.from(button).setDisabled(true)
      );
      disabledRow.addComponents(buttons);

      // Update message with disabled buttons
      await this.message.edit({
        components: [disabledRow],
      });
    } catch (error) {
      // Message might be deleted or inaccessible, ignore errors
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.debug('Could not disable pagination buttons:', errorMessage);
    }
  }
}

// ------------ HELPER FUNCTIONS

/**
 * Creates and sends a paginated message.
 * @param {EmbedBuilder[]} pages - Array of embed pages
 * @param {ChatInputCommandInteraction} interaction - Discord interaction
 * @param {object} [options] - Pagination options
 * @returns {Promise<PaginationHandler>}
 */
export async function createPaginatedMessage(pages, interaction, options = {}) {
  const pagination = new PaginationHandler(pages, interaction, options);
  await pagination.send();
  return pagination;
}

/**
 * Checks if an interaction is a pagination button.
 * @param {ButtonInteraction} interaction
 * @returns {boolean}
 */
export function isPaginationButton(interaction) {
  return interaction.customId?.startsWith('pagination_');
}

// ------------ EXPORTS
export default {
  PaginationHandler,
  createPaginatedMessage,
  isPaginationButton,
};
