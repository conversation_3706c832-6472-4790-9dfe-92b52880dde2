/**
 * @file CACHE MANAGEMENT.JS
 *
 * @version 2.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Robust, type-safe caching utility for DIS APP.
 * Supports automatic expiration, custom TTL, memory optimization, statistics, and full TypeScript support.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {Object} CacheOptions
 * @property {number} [defaultTTL=300000] - Default time-to-live in milliseconds (5 minutes)
 * @property {number} [checkInterval=60000] - Interval to check for expired items (1 minute)
 * @property {number} [maxSize=1000] - Maximum number of items in cache
 */

/**
 * @typedef {Object} CacheItem
 * @property {*} data - The cached data
 * @property {number} timestamp - When the item was added
 * @property {number} ttl - Time-to-live in milliseconds
 */

/**
 * @typedef {Object} CacheStats
 * @property {number} hits - Number of cache hits
 * @property {number} misses - Number of cache misses
 * @property {number} size - Current cache size
 * @property {number} expired - Number of expired items removed
 * @property {number} entries - Number of entries in the cache
 */

// ------------ CACHE MANAGER CLASS
class CacheManager {
  /**
   * Creates a new cache manager instance.
   * @param {CacheOptions} [options={}]
   */
  constructor(options = {}) {
    /** @type {Map<string, CacheItem>} */
    this.cache = new Map();

    this.options = {
      defaultTTL:
        typeof options.defaultTTL === 'number' ? options.defaultTTL : 300000,
      checkInterval:
        typeof options.checkInterval === 'number'
          ? options.checkInterval
          : 60000,
      maxSize: typeof options.maxSize === 'number' ? options.maxSize : 1000,
    };

    this.stats = {
      hits: 0,
      misses: 0,
      size: 0,
      expired: 0,
    };

    /** @type {NodeJS.Timeout | null} */
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, this.options.checkInterval);
  }

  /**
   * Get data from cache.
   * @param {string} key
   * @returns {*} Cached data or null if not found/expired
   */
  get(key) {
    const item = this.cache.get(key);

    if (!item) {
      this.stats.misses++;
      return null;
    }

    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      this.stats.expired++;
      this.stats.misses++;
      this.stats.size = this.cache.size;
      return null;
    }

    this.stats.hits++;
    return item.data;
  }

  /**
   * Store data in cache.
   * @param {string} key
   * @param {*} data
   * @param {number} [ttl]
   * @returns {boolean}
   */
  set(key, data, ttl = this.options.defaultTTL) {
    if (this.cache.size >= this.options.maxSize && !this.cache.has(key)) {
      const oldestKey = this.cache.keys().next().value;
      if (oldestKey !== undefined) {
        this.cache.delete(oldestKey);
      }
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });

    this.stats.size = this.cache.size;
    return true;
  }

  /**
   * Remove item from cache.
   * @param {string} key
   * @returns {boolean}
   */
  delete(key) {
    const result = this.cache.delete(key);
    this.stats.size = this.cache.size;
    return result;
  }

  /**
   * Check if key exists and is not expired.
   * @param {string} key
   * @returns {boolean}
   */
  has(key) {
    const item = this.cache.get(key);

    if (!item) return false;

    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      this.stats.expired++;
      this.stats.size = this.cache.size;
      return false;
    }

    return true;
  }

  /**
   * Remove all expired items from cache.
   * @returns {number} Number of items removed
   */
  cleanup() {
    const now = Date.now();
    let removed = 0;

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
        removed++;
      }
    }

    this.stats.expired += removed;
    this.stats.size = this.cache.size;
    return removed;
  }

  /**
   * Clear the entire cache.
   */
  clear() {
    this.cache.clear();
    this.stats.size = 0;
  }

  /**
   * Get cache statistics.
   * @returns {CacheStats}
   */
  getStats() {
    return {
      ...this.stats,
      entries: this.cache.size,
    };
  }

  /**
   * Stop the automatic cleanup process.
   */
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }
}

// ------------ DEFAULT CACHE INSTANCE AND HELPERS

/** @type {CacheManager} */
export const defaultCache = new CacheManager();

/**
 * Export the CacheManager class and default instance.
 */
export { CacheManager };

/**
 * Get cached data from the default cache.
 * @param {string} key
 * @returns {*} Cached data or null
 */
export function getCachedData(key) {
  return defaultCache.get(key);
}

/**
 * Set cached data in the default cache.
 * @param {string} key
 * @param {*} data
 * @param {number} [ttl]
 * @returns {boolean}
 */
export function setCachedData(key, data, ttl) {
  return defaultCache.set(key, data, ttl);
}
