/**
 * @file RATE LIMIT.JS
 *
 * @version 2.0.1
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Advanced rate limiting system for Discord bots.
 * Prevents spam and abuse, protects from API rate limits,
 * supports per-command and global limits, and tracks stats.
 * Fully type-annotated for editor support and maintainability.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {Object} RateLimitOptions
 * @property {number} [windowMs=60000] - Time window in milliseconds
 * @property {number} [maxRequests=5] - Maximum requests per window
 * @property {number} [cleanupInterval=60000] - Cleanup interval for expired entries
 * @property {string[]} [bypassRoles=[]] - Role IDs that bypass rate limits
 */

/**
 * @typedef {Object} RateLimitConfig
 * @property {number} windowMs - Time window in milliseconds
 * @property {number} maxRequests - Maximum requests per window
 * @property {boolean} global - Whether limit applies globally or per channel
 */

/**
 * @typedef {Object} RateLimitContext
 * @property {string} userId - Discord user ID
 * @property {string} [channelId] - Discord channel ID (optional)
 * @property {string} [guildId] - Discord guild ID (optional)
 * @property {string} [commandName] - Command name (optional)
 */

// ------------ IMPORTS
import Bottleneck from 'bottleneck';

import { botConfig } from '../config/config.js';

// ------------ BOTTLENECK RATE LIMITER MAP
/** @type {Map<string, Bottleneck>} */
const limiters = new Map();

/**
 * Get or create a Bottleneck rate limiter by key.
 * @param {string} key
 * @param {object} opts
 * @returns {Bottleneck}
 */
export function getRateLimiter(key, opts = {}) {
  if (!limiters.has(key)) {
    limiters.set(key, new Bottleneck(opts));
  }
  // Always return a Bottleneck instance, never undefined

  return /** @type {Bottleneck} */ (limiters.get(key));
}

// ------------ RATE LIMIT MANAGER CLASS
/**
 * Manages rate limits for users, channels, and commands.
 * Supports per-command and global limits, bypass roles, and stats.
 */
class RateLimitManager {
  /**
   * Create a new rate limit manager.
   */
  constructor() {
    /** @type {Map<string, {count: number, resetAt: number, history: number[]}>} */
    this.requests = new Map();

    /** @type {Map<string, RateLimitConfig>} */
    this.commandConfigs = new Map();

    this.options = {
      windowMs: Number(botConfig.rateLimits.windowMS),
      maxRequests: Number(botConfig.rateLimits.maxRequests),
      cleanupInterval: Number(botConfig.rateLimits.cleanupInterval),
      bypassRoles: Array.isArray(botConfig.rateLimits.bypassRoles)
        ? botConfig.rateLimits.bypassRoles
        : [],
    };

    this.stats = {
      limited: 0,
      total: 0,
    };

    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, this.options.cleanupInterval);
  }

  /**
   * Configure rate limit for a specific command.
   * @param {string} commandName
   * @param {number} maxRequests
   * @param {number} [windowMs]
   * @param {boolean} [global=false]
   */
  configureCommand(
    commandName,
    maxRequests,
    windowMs = this.options.windowMs,
    global = false
  ) {
    this.commandConfigs.set(commandName, {
      windowMs,
      maxRequests,
      global,
    });
  }

  /**
   * Get a unique key for the rate limit.
   * @param {RateLimitContext} context
   * @returns {string}
   * @private
   */
  _getKey(context) {
    const { userId, channelId, guildId, commandName } = context;
    const commandConfig = commandName
      ? this.commandConfigs.get(commandName)
      : null;
    if (commandConfig && commandConfig.global) {
      return commandName
        ? `${userId}-${commandName}-${guildId || 'global'}`
        : `${userId}-${guildId || 'global'}`;
    }
    return commandName
      ? `${userId}-${commandName}-${channelId || 'dm'}-${guildId || 'global'}`
      : `${userId}-${channelId || 'dm'}-${guildId || 'global'}`;
  }

  /**
   * Check if a request should be rate limited.
   * @param {RateLimitContext} context
   * @returns {{ limited: boolean, remaining: number, resetTime: number }}
   */
  check(context) {
    const key = this._getKey(context);
    const now = Date.now();
    const commandConfig = context.commandName
      ? this.commandConfigs.get(context.commandName)
      : null;
    const config = commandConfig || {
      windowMs: this.options.windowMs,
      maxRequests: this.options.maxRequests,
    };
    if (!this.requests.has(key)) {
      this.requests.set(key, {
        count: 0,
        resetAt: now + config.windowMs,
        history: [],
      });
    }
    const request = this.requests.get(key);
    if (!request) {
      return {
        limited: false,
        remaining: config.maxRequests,
        resetTime: config.windowMs,
      };
    }
    if (now >= request.resetAt) {
      request.count = 0;
      request.resetAt = now + config.windowMs;
      request.history = [];
    }
    request.count++;
    request.history.push(now);
    this.stats.total++;
    const limited = request.count > config.maxRequests;
    if (limited) this.stats.limited++;
    return {
      limited,
      remaining: Math.max(0, config.maxRequests - request.count),
      resetTime: request.resetAt - now,
    };
  }

  /**
   * Check if user can bypass rate limits.
   * @param {Object} member - Discord.js GuildMember object
   * @returns {boolean}
   */
  canBypass(member) {
    if (!member || !this.options.bypassRoles.length) return false;

    return member.roles.cache.some((/** @type {{ id: string; }} */ role) =>
      this.options.bypassRoles.includes(role.id)
    );
  }

  /**
   * Get current rate limit status for a context.
   * @param {RateLimitContext} context
   * @returns {{ count: number, remaining: number, resetTime: number, limited: boolean }}
   */
  getStatus(context) {
    const key = this._getKey(context);
    const now = Date.now();
    if (!this.requests.has(key)) {
      const commandConfig = context.commandName
        ? this.commandConfigs.get(context.commandName)
        : null;
      const maxRequests = commandConfig
        ? commandConfig.maxRequests
        : this.options.maxRequests;
      return {
        count: 0,
        remaining: maxRequests,
        resetTime: 0,
        limited: false,
      };
    }
    const request = this.requests.get(key);
    if (!request) {
      return {
        count: 0,
        remaining: this.options.maxRequests,
        resetTime: 0,
        limited: false,
      };
    }
    if (now >= request.resetAt) {
      return {
        count: 0,
        remaining:
          context.commandName && this.commandConfigs.has(context.commandName)
            ? (this.commandConfigs.get(context.commandName)?.maxRequests ??
              this.options.maxRequests)
            : this.options.maxRequests,
        resetTime: 0,
        limited: false,
      };
    }
    const config =
      context.commandName && this.commandConfigs.has(context.commandName)
        ? this.commandConfigs.get(context.commandName)
        : { maxRequests: this.options.maxRequests };
    return {
      count: request.count,
      remaining: Math.max(
        0,
        (config?.maxRequests ?? this.options.maxRequests) - request.count
      ),
      resetTime: request.resetAt - now,
      limited:
        request.count >= (config?.maxRequests ?? this.options.maxRequests),
    };
  }

  /**
   * Remove expired rate limit entries to free up memory.
   * @returns {number} Number of entries removed
   */
  cleanup() {
    const now = Date.now();
    let removed = 0;
    for (const [key, request] of this.requests.entries()) {
      if (request && now >= request.resetAt) {
        this.requests.delete(key);
        removed++;
      }
    }
    this.stats.expired = (this.stats.expired || 0) + removed;
    return removed;
  }

  /**
   * Get current rate limit statistics.
   * @returns {{ limited: number, total: number, expired?: number }}
   */
  getStats() {
    return { ...this.stats };
  }

  /**
   * Reset rate limits for a specific context.
   * @param {RateLimitContext} context
   * @returns {boolean}
   */
  reset(context) {
    const key = this._getKey(context);
    return this.requests.delete(key);
  }

  /**
   * Disable the cleanup interval when no longer needed.
   */
  destroy() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = undefined;
    }
  }
}

// ------------ DEFAULT INSTANCE AND EXPORTS

/** @type {RateLimitManager} */
export const rateLimit = new RateLimitManager();

export { RateLimitManager };

/**
 * Helper function using the default rate limit manager.
 * @param {RateLimitContext} context
 * @returns {{ limited: boolean, remaining: number, resetTime: number }}
 */
export function checkRateLimit(context) {
  return rateLimit.check(context);
}

/**
 * Helper function to get current rate limit status for a context.
 * @param {RateLimitContext} context
 * @returns {{ count: number, remaining: number, resetTime: number, limited: boolean }}
 */
export function getRateLimitStatus(context) {
  return rateLimit.getStatus(context);
}
