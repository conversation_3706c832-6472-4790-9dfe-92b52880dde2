/**
 * @file LOGGER.JS
 *
 * @version 3.0.1
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Centralized logging system for the Discord bot.
 * Logs to file, Discord (as embed), and Sentry.
 * Provides masking for sensitive data, structured logging, and log file management.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import fs from 'fs';
import path from 'path';

import * as Sentry from '@sentry/node';
import { EmbedBuilder } from 'discord.js';
import { pino } from 'pino';

import { botConfig } from '../config/config.js';

import { handleError } from './errorHandler.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {Object} LogExtra
 * @property {string} [component]
 * @property {string} [email]
 * @property {string} [event]
 * @property {string} [user]
 * @property {string} [guild]
 * @property {string} [status]
 * @property {string} [error]
 * @property {string} [stack]
 * @property {string} [username]
 * @property {string} [target]
 * @property {string} [targetTag]
 * @property {string} [code]
 * @property {string} [command]
 * @property {string} [options]
 * @property {Object} [err]
 */

/**
 * @typedef {Object} DiscordLogOptions
 * @property {string} level
 * @property {string} msg
 * @property {string} [component]
 * @property {LogExtra} [extra]
 */

// ------------ LOG DIRECTORY SETUP
const logDir = path.resolve(process.cwd(), 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// ------------ PINO LOGGER CONFIGURATION
// Load log levels from config.js if available, else use default array
const logLevels = Array.isArray(botConfig.logging?.levels)
  ? botConfig.logging.levels
  : ['info', 'warn', 'error', 'debug'];

const loggerOptions = {
  level: logLevels[0] || 'info',
  formatters: {
    level(label) {
      return { level: label.toUpperCase() };
    },
    bindings(bindings) {
      return {
        pid: bindings.pid,
        hostname: bindings.hostname,
        app: 'discord-bot',
      };
    },
  },
  timestamp: pino.stdTimeFunctions.isoTime,
  base: { requestId: undefined },
  transport: undefined,
};
const fileStream = pino.destination(path.join(logDir, 'bot-log.json'));
const pinoLogger = pino(loggerOptions, fileStream);

// ------------ EMAIL MASKING
/**
 * Masks an email address for privacy in logs.
 * @param {string} email
 * @returns {string}
 */
function maskEmail(email) {
  if (!email || typeof email !== 'string') return '';
  const [user, domain] = email.split('@');
  if (!user || !domain) return email;
  return `${user[0]}***@${domain[0]}***.${domain.split('.').pop()}`;
}

// ------------ DISCORD EMBED LOGGING
let isShuttingDown = false;

/**
 * Mark the logger as shutting down to prevent further Discord logs.
 */
export function markShuttingDown() {
  isShuttingDown = true;
}

const MAX_EMBED_FIELD_LENGTH = 1024;

/**
 * Truncates a string to fit Discord embed field limits.
 * @param {string} str
 * @returns {string}
 */
function safeFieldValue(str) {
  if (!str) return '';
  if (str.length > MAX_EMBED_FIELD_LENGTH) {
    return str.slice(0, MAX_EMBED_FIELD_LENGTH - 20) + '\n...[truncated]';
  }
  return str;
}

/**
 * Sends a log message as a Discord embed to the admin log channel.
 * Only sends info/debug logs if enabled in config.
 * @param {DiscordLogOptions} param0
 * @returns {Promise<void>}
 */
async function sendDiscordLog({ level, msg, component, extra }) {
  try {
    if (isShuttingDown) return;

    const client = global.client;

    if (
      !client ||
      typeof client.isReady !== 'function' ||
      !client.isReady() ||
      !client.token ||
      client.token === '' ||
      client.destroyed ||
      !client.rest ||
      typeof client.rest.setToken !== 'function' ||
      !client.rest.token
    ) {
      return;
    }

    if (!botConfig.logging.logToChannels) return;
    if (level !== 'INFO' && level !== 'DEBUG') return;
    const channelId = botConfig.channels.adminLog;
    const channel = client?.channels?.cache?.get(channelId);

    if (!channel || typeof channel.send !== 'function' || !client.application) {
      return;
    }

    const embed = new EmbedBuilder()
      .setColor(parseInt(botConfig.colors.primary.replace(/^#/, ''), 16))
      .setTitle(`${level} Log`)
      .setDescription(safeFieldValue(`\`\`\`${msg}\`\`\``))
      .addFields(
        {
          name: 'Module',
          value: safeFieldValue(component || 'unknown'),
          inline: true,
        },
        { name: 'Timestamp', value: new Date().toISOString(), inline: true }
      )
      .addFields([
        {
          name: 'Details',
          value: safeFieldValue(extra ? JSON.stringify(extra, null, 2) : msg),
          inline: false,
        },
      ])
      .setTimestamp();

    if (extra) {
      embed.addFields({
        name: 'Extra',
        value: safeFieldValue(`\`\`\`${JSON.stringify(extra, null, 2)}\`\`\``),
      });
    }
    await channel.send({ embeds: [embed] });
  } catch (e) {
    handleError('Discord embed log error:', e);
  }
}

// ------------ SENTRY LOGGING
/**
 * Adds a Sentry breadcrumb for context.
 * @param {string} level
 * @param {string|Object} msg
 * @param {Object} data
 */
function sentryBreadcrumb(level, msg, data) {
  try {
    Sentry.addBreadcrumb({
      level: level.toLowerCase(),
      message: typeof msg === 'string' ? msg : JSON.stringify(msg),
      data,
    });
  } catch (e) {
    // Sentry errors should not crash logging
    handleError('Sentry breadcrumb error:', e);
  }
}

/**
 * Captures an exception in Sentry with extra context.
 * @param {Error} error
 * @param {Object} context
 */
function sentryException(error, context) {
  Sentry.captureException(error, { extra: context });
}

// ------------ LOGGER INTERFACE
/**
 * Centralized logger object for the bot.
 * Provides info, warn, error, debug, and child loggers.
 */
const logger = {
  /**
   * Info log.
   * @param {string|Object} msgOrObj
   * @param {string} [msg]
   */
  info: (msgOrObj, msg) => {
    const safeObj = typeof msgOrObj === 'object' ? { ...msgOrObj } : {};
    if (safeObj.email) safeObj.email = maskEmail(safeObj.email);
    const message =
      msg || (typeof msgOrObj === 'string' ? msgOrObj : undefined);
    pinoLogger.info(safeObj, message);
    sentryBreadcrumb('info', message || safeObj, safeObj);
    sendDiscordLog({
      level: 'INFO',
      msg: message || JSON.stringify(safeObj),
      component: safeObj.component,
      extra: safeObj,
    });
  },
  /**
   * Warn log.
   * @param {string|Object} msgOrObj
   * @param {string} [msg]
   */
  warn: (msgOrObj, msg) => {
    const safeObj = typeof msgOrObj === 'object' ? { ...msgOrObj } : {};
    if (safeObj.email) safeObj.email = maskEmail(safeObj.email);
    const message =
      msg || (typeof msgOrObj === 'string' ? msgOrObj : undefined);
    pinoLogger.warn(safeObj, message);
    sentryBreadcrumb('warning', message || safeObj, safeObj);
    // Do NOT send warn to Discord (handled by errorHandler.js)
  },
  /**
   * Error log.
   * @param {string|Object} msg
   * @param {Error|Object} [err]
   */
  error: (msg, err) => {
    const safeMsg =
      typeof msg === 'object' && msg.email
        ? { ...msg, email: maskEmail(msg.email) }
        : msg;
    if (err instanceof Error) {
      const errObj = {
        message: err.message,
        stack: err.stack,
        name: err.name,
      };
      // Only spread additional properties if they don't overwrite the above
      for (const key of Object.keys(err)) {
        if (!(key in errObj)) {
          const hasProperty = Object.prototype.hasOwnProperty.call(err, key);
          const value = hasProperty
            ? Object.getOwnPropertyDescriptor(err, key)?.value
            : undefined;
          Object.defineProperty(errObj, key, {
            value: value,
            writable: true,
            enumerable: true,
            configurable: true,
          });
        }
      }
      pinoLogger.error(
        {
          err: errObj,
        },
        safeMsg
      );
      sentryException(err, safeMsg);
    } else if (err) {
      pinoLogger.error({ err }, safeMsg);
      sentryBreadcrumb('error', safeMsg, err);
    } else {
      pinoLogger.error(safeMsg);
      sentryBreadcrumb('error', safeMsg, {});
    }
    // Do NOT send error to Discord (handled by errorHandler.js)
  },
  /**
   * Debug log.
   * @param {string|Object} msgOrObj
   * @param {string} [msg]
   */
  debug: (msgOrObj, msg) => {
    const safeObj = typeof msgOrObj === 'object' ? { ...msgOrObj } : {};
    if (safeObj.email) safeObj.email = maskEmail(safeObj.email);
    const message =
      msg || (typeof msgOrObj === 'string' ? msgOrObj : undefined);
    pinoLogger.debug(safeObj, message);
    sentryBreadcrumb('debug', message || safeObj, safeObj);
    sendDiscordLog({
      level: 'DEBUG',
      msg: message || JSON.stringify(safeObj),
      component: safeObj.component,
      extra: safeObj,
    });
  },
  /**
   * Create a child logger with additional bindings.
   * @param {Object} bindings
   * @returns {Object}
   */
  child: bindings => {
    return {
      info: (msgOrObj, msg) => logger.info({ ...bindings, ...msgOrObj }, msg),
      warn: (msgOrObj, msg) => logger.warn({ ...bindings, ...msgOrObj }, msg),
      error: (msg, err) => {
        if (typeof msg === 'object' && msg !== null) {
          logger.error({ ...bindings, ...msg }, err);
        } else {
          logger.error(msg, err);
        }
      },
      debug: (msgOrObj, msg) => logger.debug({ ...bindings, ...msgOrObj }, msg),
    };
  },
  /**
   * Clear the log file (for use by a command).
   */
  clearLogFile: () => {
    try {
      fileStream.flushSync();
      fs.writeFileSync(path.join(logDir, 'bot-log.json'), '');
      pinoLogger.info('Log file cleared by command');
    } catch (e) {
      pinoLogger.error('Failed to clear log file:', e);
    }
  },
  /**
   * Flush the log file stream (for graceful shutdown).
   */
  flush: () => {
    try {
      fileStream.flushSync();
    } catch (e) {
      pinoLogger.error('File stream flush error:', e);
    }
  },
};

// ------------ FLUSH FILE STREAM ON EXIT
process.on('exit', () => {
  try {
    fileStream.flushSync();
  } catch (e) {
    pinoLogger.error('File stream flush error:', e);
  }
});

// ------------ EXPORT LOGGER
export default logger;
