/**
 * @file REPLY HELPERS.JS
 *
 * @version 2.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Helper for safe replies to Discord interactions.
 * Ensures correct reply method is used based on interaction state.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import logger from './logger.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').Interaction} Interaction
 * @typedef {object} ReplyOptions
 */

// ------------ SAFE REPLY FUNCTION
/**
 * Safely reply to an interaction, using followUp if already replied,
 * editReply if deferred, or reply otherwise.
 * Uses runtime checks to avoid TypeScript property errors for non-applicable interaction types.
 * @param {Interaction} interaction - Discord.js interaction object
 * @param {ReplyOptions} options - Options for the reply (content, embeds, flags, etc.)
 * @returns {Promise<void>}
 */
export async function safeReply(interaction, options) {
  if (!interaction) return Promise.reject(new Error('No interaction provided'));

  try {
    // Only handle interactions that support reply/followUp/editReply
    // (e.g., not AutocompleteInteraction)
    if (
      'replied' in interaction &&
      typeof interaction.replied === 'boolean' &&
      'followUp' in interaction &&
      typeof interaction.followUp === 'function'
    ) {
      if (interaction.replied) {
        await interaction.followUp(options);
        return;
      }
    }
    if (
      'deferred' in interaction &&
      typeof interaction.deferred === 'boolean' &&
      'editReply' in interaction &&
      typeof interaction.editReply === 'function'
    ) {
      if (interaction.deferred) {
        await interaction.editReply(options);
        return;
      }
    }
    if ('reply' in interaction && typeof interaction.reply === 'function') {
      await interaction.reply(options);
      return;
    }
    throw new Error('Interaction does not support replying.');
  } catch (error) {
    if (error instanceof Error) {
      logger?.error?.(`Failed to reply to interaction: ${error.message}`);
    } else {
      logger?.error?.(
        'Failed to reply to interaction: An unknown error occurred'
      );
    }
    return Promise.reject(error);
  }
}
