/**
 * @file MESSAGE MANAGER.JS
 *
 * @version 2.0.1
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Message handling utilities for Discord bot communications.
 * Provides functions for formatting, sanitizing, and splitting message content.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {Object.<string, any>} MessageData
 */

// ------------ FORMAT MESSAGE
/**
 * Formats a message by replacing placeholders with corresponding data values.
 *
 * @param {string} template - The message template containing placeholders in format {key}
 * @param {MessageData} [data={}] - An object with keys that match the placeholders
 * @returns {string} - The formatted message with all placeholders replaced
 *
 * @example
 * // Returns "Hello <PERSON>, welcome to Discord!"
 * formatMessage("Hello {name}, welcome to {platform}!", { name: "<PERSON>", platform: "Discord" })
 */
export function formatMessage(template, data = {}) {
  return Object.keys(data).reduce((msg, key) => {
    const hasProperty = Object.prototype.hasOwnProperty.call(data, key);
    const value = hasProperty
      ? Object.getOwnPropertyDescriptor(data, key)?.value
      : undefined;
    return msg.replace(
      new RegExp(`{${key.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}}`, 'g'),
      String(value ?? '')
    );
  }, template);
}

// ------------ SANITIZE MESSAGE
/**
 * Sanitizes message content to prevent mentions and potential exploits.
 *
 * @param {string} content - The raw message content
 * @returns {string} - Sanitized message content
 *
 * @example
 * // Returns "Hello [mention]!"
 * sanitizeMessage("Hello @everyone!")
 */
export function sanitizeMessage(content) {
  if (typeof content !== 'string') return '';
  // Replace potentially disruptive mentions
  return content.replace(/@everyone|@here/g, '[mention]');
}

// ------------ SPLIT MESSAGE
/**
 * Splits a long message into chunks that respect Discord's character limit.
 *
 * @param {string} content - The message content to split
 * @param {number} [maxLength=2000] - Maximum length of each chunk (defaults to Discord's 2000 char limit)
 * @returns {string[]} - Array of message chunks
 *
 * @example
 * // Returns ["This is a ", "long message"]
 * splitMessage("This is a long message", 10)
 */
export function splitMessage(content, maxLength = 2000) {
  if (!content || content.length <= maxLength) {
    return content.length ? [content] : [];
  }

  const result = [];
  let current = content;

  while (current.length > maxLength) {
    result.push(current.slice(0, maxLength));
    current = current.slice(maxLength);
  }

  if (current.length > 0) result.push(current);

  return result;
}
