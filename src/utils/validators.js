/**
 * @file VALIDATORS.JS
 *
 * @version 2.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Validation utilities for various user inputs and Discord permissions.
 * Includes Discord ID, email, Roblox username, permissions, and yes/no parsing.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { PermissionFlagsBits } from 'discord.js';

import { botConfig } from '../config/config.js';

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').GuildMember} GuildMember
 */

// ------------ ADMIN CHECK
/**
 * Checks if a member is an admin (has Administrator permission or admin role).
 * @param {GuildMember} member
 * @param {string} [adminRoleId=botConfig.roles.admin]
 * @returns {boolean}
 */
export function isAdmin(member, adminRoleId = botConfig.roles.admin) {
  return hasAdminPermission(member, adminRoleId);
}

// ------------ DISCORD ID VALIDATION
/**
 * Validates a Discord user ID.
 * @param {string} id
 * @returns {boolean}
 */
export function isValidDiscordId(id) {
  return /^\d{17,20}$/.test(id);
}

// ------------ PERMISSION CHECKS
/**
 * Checks if a member has admin permissions (Administrator or admin role).
 * @param {GuildMember} member
 * @param {string} [adminRoleId=botConfig.roles.admin]
 * @returns {boolean}
 */
export function hasAdminPermission(
  member,
  adminRoleId = botConfig.roles.admin
) {
  if (!member) return false;
  return (
    member.permissions.has(PermissionFlagsBits.Administrator) ||
    (member.roles && member.roles.cache && member.roles.cache.has(adminRoleId))
  );
}

/**
 * Checks if a member has HR permissions (ManageRoles or HR role).
 * @param {GuildMember} member
 * @param {string} [hrRoleId=botConfig.roles.hr]
 * @returns {boolean}
 */
export function hasHumanResourcesPermission(
  member,
  hrRoleId = botConfig.roles.hr
) {
  if (!member) return false;
  return (
    member.permissions.has(PermissionFlagsBits.ManageRoles) ||
    (member.roles && member.roles.cache && member.roles.cache.has(hrRoleId))
  );
}

// ------------ EMAIL VALIDATION
/**
 * Validate email format using RFC 5322 standard.
 * @param {string} email
 * @returns {boolean}
 */
export function isValidEmail(email) {
  if (!email || typeof email !== 'string') return false;
  // RFC 5322 compliant regex for email validation
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailRegex.test(email);
}

// ------------ ROBLOX VALIDATION
/**
 * Validate Roblox username format.
 * @param {string} username
 * @returns {boolean}
 */
export function isValidRobloxUsername(username) {
  if (!username || typeof username !== 'string') return false;
  if (username.toLowerCase() === 'skip') return true;
  // Roblox username requirements:
  // - 3-20 characters, alphanumeric or underscores, no spaces, cannot start/end with underscore, no consecutive underscores
  const robloxUsernameRegex = /^[a-zA-Z0-9][a-zA-Z0-9_]{1,18}[a-zA-Z0-9]$/;
  const hasConsecutiveUnderscores = /__/.test(username);
  return (
    username.length >= 3 &&
    username.length <= 20 &&
    robloxUsernameRegex.test(username) &&
    !hasConsecutiveUnderscores
  );
}

// ------------ SANITIZATION
/**
 * Sanitize input string to prevent injection attacks.
 * @param {string} input
 * @returns {string}
 */
export function sanitizeInput(input) {
  if (!input || typeof input !== 'string') return '';
  return (
    input
      .trim()
      // Remove HTML tags
      .replace(/<[^>]*>/g, '')
      // Only allow alphanumeric, whitespace, @, ., -
      .replace(/[^\w\s@.-]/g, '')
      // Limit to 1000 characters
      .slice(0, 1000)
  );
}

/**
 * Sanitize nickname input, allowing common name characters.
 * @param {string} input
 * @returns {string}
 */
export function sanitizeNickname(input) {
  if (!input || typeof input !== 'string') return '';
  return (
    input
      .trim()
      // Remove HTML tags
      .replace(/<[^>]*>/g, '')
      // Allow Unicode letters, numbers, spaces, and these: - _ . ' ! # @
      .replace(/[^\p{L}\p{N}\s\-_.!'#@]/gu, '')
      // Discord nickname limit
      .slice(0, 32)
  );
}

/**
 * Sanitize and validate timezone input.
 * Accepts formats like "UTC+01:00", "UTC-8", "GMT+2", "PST", "EST", etc.
 * @param {string} input
 * @returns {string}
 */
export function sanitizeTimeZone(input) {
  if (!input || typeof input !== 'string') return '';
  const sanitized = input
    .trim()
    // Remove HTML tags
    .replace(/<[^>]*>/g, '')
    // Allow alphanum, space, +, -, :, ., (, ), /
    .replace(/[^\w\s:+\-()./]/g, '')
    // Limit to 16 characters
    .slice(0, 16);

  // Accept common timezone formats
  const tzRegex =
    /^(UTC|GMT)?\s*([+-]?\d{1,2}(:\d{2})?)?$|^[A-Za-z]{2,5}(\/[A-Za-z_]+)?$/;
  if (tzRegex.test(sanitized)) {
    return sanitized;
  }
  return '';
}

// ------------ PERMISSION CHECKING
/**
 * Check if user has required permission (admin or human resources).
 * @param {GuildMember} member
 * @param {string[]} [requiredRoles=['admin']] - Array of required role types
 * @returns {boolean}
 */
export function hasPermission(member, requiredRoles = ['admin']) {
  if (!member || !member.guild) return false;

  const adminRoleId = botConfig.roles.admin;
  const hrRoleId = botConfig.roles.hr;

  /** @type {string[]} */
  const rolesToCheck = [];

  if (requiredRoles.includes('admin') && adminRoleId) {
    rolesToCheck.push(adminRoleId);
  }
  if (requiredRoles.includes('hr') && hrRoleId) {
    rolesToCheck.push(hrRoleId);
  }

  if (rolesToCheck.length === 0) return false;

  return member.roles.cache.some(role => rolesToCheck.includes(role.id));
}

// ------------ YES/NO VALIDATION
/**
 * Check if a string is a valid yes/no response.
 * @param {string} response
 * @returns {{ isValid: boolean, value: boolean|null }}
 */
export function parseYesNo(response) {
  if (!response || typeof response !== 'string') {
    return { isValid: false, value: null };
  }
  const normalized = response.trim().toLowerCase();
  if (
    ['yes', 'y', 'yeah', 'yep', 'sure', 'ok', 'okay', 'true', '1'].includes(
      normalized
    )
  ) {
    return { isValid: true, value: true };
  }
  if (['no', 'n', 'nope', 'nah', 'false', '0'].includes(normalized)) {
    return { isValid: true, value: false };
  }
  return { isValid: false, value: null };
}

// ------------ EXPORT
export default {
  isValidEmail,
  isValidRobloxUsername,
  sanitizeInput,
  sanitizeNickname,
  sanitizeTimeZone,
  hasPermission,
  parseYesNo,
  isValidDiscordId,
  hasAdminPermission,
  isAdmin,
  hasHumanResourcesPermission,
};
