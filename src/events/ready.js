/**
 * @file READY.JS
 *
 * @version 2.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Handler for Discord.js 'ready' event that executes once when the bot starts.
 * Sends a notification embed to the configured admin log channel with bot startup information.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { EmbedBuilder } from 'discord.js';

import { botConfig } from '../config/config.js';
import { messages } from '../config/messages.js';
import { handleError } from '../utils/errorHandler.js';
import logger from '../utils/logger.js';

// ------------ TYPE SAFETY ANNOTATIONS
/**
 * @typedef {import('discord.js').Client} DiscordClient
 */

// ------------ EVENT HANDLER
/**
 * Discord.js event: ready
 * Executes once when the bot is ready.
 *
 * @type {{name: string, once: boolean, execute: function(DiscordClient): Promise<void>}}
 */
export default {
  name: 'ready',
  once: true,
  /**
   * Execute function that runs when the bot is ready.
   * - Sends a startup embed to the admin log channel.
   * - Logs startup status.
   *
   * @param {DiscordClient} client - Discord.js client instance
   * @returns {Promise<void>}
   */
  async execute(client) {
    try {
      // Get log channel from config
      const logChannel = client.channels.cache.get(botConfig.channels.adminLog);

      if (!logChannel) {
        logger.warn(
          {
            event: 'ready',
            component: 'startup',
            guild: client.guilds.cache.first()?.id,
          },
          'Admin log channel not found!'
        );
        return;
      }

      // Create embed from message template
      let colorValue = botConfig.colors.primary;
      if (
        typeof colorValue !== 'string' ||
        !/^#?[0-9a-fA-F]{6}$/.test(colorValue)
      ) {
        // fallback to default hex string
        colorValue = '#ccf3ff';
      }
      if (!colorValue.startsWith('#')) {
        colorValue = `#${colorValue}`;
      }
      // Convert hex string to number for Discord.js ColorResolvable
      const colorNumber = parseInt(colorValue.replace('#', ''), 16);

      const startupEmbed = new EmbedBuilder()
        .setTitle(messages.admin.startupEmbed.title)
        .setColor(colorNumber)
        .setDescription(messages.admin.startupEmbed.description)
        .addFields(
          ...messages.admin.startupEmbed.fields.map(field => ({
            name: field.name,
            value: field.value
              .replace('{env}', botConfig.env.APP_ENV)
              .replace('{nodeVersion}', process.version)
              .replace('{version}', botConfig.version),
            inline: field.inline,
          }))
        )
        .setFooter({ text: messages.admin.startupEmbed.footer })
        .setTimestamp();

      // Send embed to admin log channel (only if text-based)
      if (
        typeof logChannel.isTextBased === 'function'
          ? logChannel.isTextBased()
          : logChannel.type ===
            (await import('discord.js')).ChannelType.GuildText
      ) {
        // Ensure logChannel is a TextChannel before sending
        if ('send' in logChannel && typeof logChannel.send === 'function') {
          await logChannel.send({ embeds: [startupEmbed] });
          logger.info(
            {
              event: 'ready',
              component: 'startup',
              guild: client.guilds.cache.first()?.id,
            },
            'Startup notification sent to admin log channel'
          );
        } else {
          logger.warn(
            {
              event: 'ready',
              component: 'startup',
              guild: client.guilds.cache.first()?.id,
            },
            'Admin log channel does not support sending messages.'
          );
        }
      } else {
        logger.warn(
          {
            event: 'ready',
            component: 'startup',
            guild: client.guilds.cache.first()?.id,
          },
          'Admin log channel is not text-based, cannot send embed.'
        );
      }
    } catch (error) {
      await handleError(error, { event: 'ready' }, client);
    }
  },
};
