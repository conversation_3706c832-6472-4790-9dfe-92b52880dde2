/**
 * @file MESSAGE CREATE.JS
 *
 * @version 2.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Handles the Discord.js 'messageCreate' event.
 * Processes direct messages (DMs) for onboarding and ignores
 * messages from bots, system users, and guild channels.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import OnboardingService from '../services/onboardingService.js';
import { handleError } from '../utils/errorHandler.js';

// ------------ TYPE SAFETY ANNOTATIONS
/**
 * @typedef {import('discord.js').Client} DiscordClient
 * @typedef {import('discord.js').Message} Message
 */

// ------------ EVENT HANDLERS
/**
 * Discord.js event: messageCreate
 * Handles incoming messages and routes DMs to onboarding.
 *
 * @type {{name: string, once: boolean, execute: function(DiscordClient, Message): Promise<void>}}
 */
export default {
  name: 'messageCreate',
  once: false,
  /**
   * Executes when a message is created.
   * - Ignores bot/system messages and guild messages.
   * - Routes DMs to onboarding service.
   *
   * @param {DiscordClient} client - The Discord.js client instance
   * @param {Message} message - The Discord.js message object
   * @returns {Promise<void>}
   */
  async execute(client, message) {
    try {
      // Skip processing for bot or system messages
      if (message.author.bot || message.author.system) return;

      // Only handle direct messages (DMs), ignore guild messages
      if (message.guild) return;

      // Route the message to onboarding service if user is in onboarding flow
      await OnboardingService.processResponse(message);
    } catch (error) {
      await handleError(
        error,
        { event: 'messageCreate', user: message?.author?.id },
        client
      );
    }
  },
};
