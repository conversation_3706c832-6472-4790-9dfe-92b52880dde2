/**
 * @file GUILD MEMBER ADD.JS
 *
 * @version 2.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Handles Discord guild member events: join, leave, and update.
 * Responsible for onboarding, role management, and logging.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { botConfig } from '../config/config.js';
import { User } from '../models/User.js';
import OnboardingService from '../services/onboardingService.js';
import db from '../utils/Database.js';
import { handleError } from '../utils/errorHandler.js';
import logger from '../utils/logger.js';

// ------------ TYPE SAFETY ANNOTATIONS

/**
 * @typedef {import('discord.js').GuildMember} GuildMember
 */

// ------------ EVENT HANDLERS

/**
 * <PERSON>les member join events.
 * - Logs the event.
 * - Creates a user record if not present.
 * - Initiates onboarding or sends a welcome message.
 * - Restores verified role for returning members.
 * - Logs to admin channel.
 *
 * @param {GuildMember} member - Discord.js GuildMember object
 * @returns {Promise<void>}
 */
export async function handleMemberJoin(member) {
  try {
    logger.info(
      {
        event: 'guildMemberAdd',
        user: member.id,
        username: member.user.tag,
        guild: member.guild?.id,
      },
      `New member joined: ${member.user.tag} (${member.id})`
    );

    // Skip bots
    if (member.user.bot) return;

    // Create user record if doesn't exist
    const user = await db.getUser(member.id);

    if (!user) {
      // Convert discord member to our User model
      const newUser = User.fromDiscordMember(member);
      await db.saveUser(newUser);

      logger.info(
        {
          event: 'guildMemberAdd',
          user: member.id,
          username: member.user.tag,
          guild: member.guild?.id,
        },
        `Created new user record for ${member.user.tag}`
      );

      // Check if onboarding is enabled
      if (botConfig.onboarding.enabled) {
        // Start DM onboarding process
        await OnboardingService.startOnboarding(member);
      } else {
        // If onboarding disabled, just send welcome message in welcome channel
        const welcomeChannel = member.guild.channels.cache.get(
          botConfig.channels.welcome
        );
        // Only send if channel is text-based
        if (
          welcomeChannel &&
          'send' in welcomeChannel &&
          typeof welcomeChannel.send === 'function'
        ) {
          await welcomeChannel.send(
            `Welcome to the server, ${member}! We're glad to have you here.`
          );
        }
      }
    } else {
      // User exists in our database
      logger.info(
        {
          event: 'guildMemberAdd',
          user: member.id,
          username: member.user.tag,
          guild: member.guild?.id,
          status: 'returning',
        },
        `Returning member joined: ${member.user.tag}`
      );

      // Check if they're verified
      if (user.verified) {
        // Add verified role if they don't have it
        const verifiedRole = member.guild.roles.cache.get(
          botConfig.roles.verified
        );
        if (verifiedRole && !member.roles.cache.has(verifiedRole.id)) {
          await member.roles.add(verifiedRole);
          logger.info(
            {
              event: 'guildMemberAdd',
              user: member.id,
              username: member.user.tag,
              guild: member.guild?.id,
              status: 'restored_verified_role',
            },
            `Restored verified role for returning member: ${member.user.tag}`
          );
        }
      } else if (botConfig.onboarding.enabled) {
        // Not verified, restart onboarding
        await OnboardingService.startOnboarding(member);
      }
    }

    const adminLogChannel = member.guild.channels.cache.get(
      botConfig.channels.adminLog
    );
    if (
      adminLogChannel &&
      'send' in adminLogChannel &&
      typeof adminLogChannel.send === 'function'
    ) {
      await adminLogChannel.send(
        `📥 **Member Joined**: ${member.user.tag} (${member.id})`
      );
    }
  } catch (error) {
    await handleError(
      error,
      {
        event: 'guildMemberAdd',
        user: member?.user?.id,
        guild: member.guild?.id,
      },

      // Client is not available here, pass undefined
      undefined
    );
  }
}

/**
 * Handles member leave events.
 * - Logs the event.
 * - Updates user metadata to reflect departure.
 * - Logs to admin channel.
 *
 * @param {GuildMember} member - Discord.js GuildMember object
 * @returns {Promise<void>}
 */
export async function handleMemberLeave(member) {
  try {
    logger.info(
      {
        event: 'guildMemberRemove',
        user: member.id,
        username: member.user.tag,
        guild: member.guild?.id,
      },
      `Member left: ${member.user.tag} (${member.id})`
    );

    // Skip bots
    if (member.user.bot) return;

    // Update user metadata
    const user = await db.getUser(member.id);
    if (user) {
      // Update metadata to track when they left
      user.update({
        metadata: {
          ...user.metadata,
          leftAt: new Date(),
          leftServer: true,
        },
      });

      await db.saveUser(user);
      logger.info(
        {
          event: 'guildMemberRemove',
          user: member.id,
          username: member.user.tag,
          guild: member.guild?.id,
          status: 'updated_user_left',
        },
        `Updated user record for leaving member: ${member.user.tag}`
      );
    }

    const adminLogChannel = member.guild.channels.cache.get(
      botConfig.channels.adminLog
    );
    if (
      adminLogChannel &&
      'send' in adminLogChannel &&
      typeof adminLogChannel.send === 'function'
    ) {
      await adminLogChannel.send(
        `📤 **Member Left**: ${member.user.tag} (${member.id})`
      );
    }
  } catch (error) {
    await handleError(
      error,
      {
        event: 'guildMemberRemove',
        user: member?.user?.id,
        guild: member.guild?.id,
      },
      undefined
    );
  }
}

/**
 * Handles member update events.
 * - Tracks username/tag changes.
 * - Updates user records for tag changes.
 * - Tracks verified role changes and updates user verification status.
 * - Logs changes to admin channel.
 *
 * @param {GuildMember} oldMember - Discord.js GuildMember before update
 * @param {GuildMember} newMember - Discord.js GuildMember after update
 * @returns {Promise<void>}
 */
export async function handleMemberUpdate(oldMember, newMember) {
  try {
    // Skip bots
    if (newMember.user.bot) return;

    // Check for username/tag changes
    if (oldMember.user.tag !== newMember.user.tag) {
      logger.info(
        {
          event: 'guildMemberUpdate',
          user: newMember.id,
          oldTag: oldMember.user.tag,
          newTag: newMember.user.tag,
          guild: newMember.guild?.id,
          status: 'tag_change',
        },
        `Member changed tag: ${oldMember.user.tag} -> ${newMember.user.tag}`
      );

      // Update user record
      const user = await db.getUser(newMember.id);
      if (user) {
        user.update({
          tag: newMember.user.tag,
          metadata: {
            ...user.metadata,
            previousTags: [
              ...(user.metadata.previousTags || []),
              oldMember.user.tag,
            ],
          },
        });

        await db.saveUser(user);
        logger.info(
          {
            event: 'guildMemberUpdate',
            user: newMember.id,
            username: newMember.user.tag,
            guild: newMember.guild?.id,
            status: 'updated_tag',
          },
          `Updated user record for tag change: ${newMember.user.tag}`
        );
      }
      const adminLogChannel = newMember.guild.channels.cache.get(
        botConfig.channels.adminLog
      );
      if (
        adminLogChannel &&
        'send' in adminLogChannel &&
        typeof adminLogChannel.send === 'function'
      ) {
        await adminLogChannel.send(
          `📝 **Tag Change**: ${oldMember.user.tag} -> ${newMember.user.tag} (${newMember.id})`
        );
      }
    }

    // Check for verified role changes
    const verifiedRoleId = botConfig.roles.verified;
    const hadVerifiedRole = oldMember.roles.cache.has(verifiedRoleId);
    const hasVerifiedRole = newMember.roles.cache.has(verifiedRoleId);

    // Role was added
    if (!hadVerifiedRole && hasVerifiedRole) {
      logger.info(
        {
          event: 'guildMemberUpdate',
          user: newMember.id,
          username: newMember.user.tag,
          guild: newMember.guild?.id,
          status: 'verified_role_added',
        },
        `Member received verified role: ${newMember.user.tag}`
      );

      const user = await db.getUser(newMember.id);
      if (user && !user.verified) {
        user.verify();
        await db.saveUser(user);
        logger.info(
          {
            event: 'guildMemberUpdate',
            user: newMember.id,
            username: newMember.user.tag,
            guild: newMember.guild?.id,
            status: 'user_verified',
          },
          `Updated user record to verified: ${newMember.user.tag}`
        );
      }
    }

    // Role was removed
    if (hadVerifiedRole && !hasVerifiedRole) {
      logger.info(
        {
          event: 'guildMemberUpdate',
          user: newMember.id,
          username: newMember.user.tag,
          guild: newMember.guild?.id,
          status: 'verified_role_removed',
        },
        `Member lost verified role: ${newMember.user.tag}`
      );

      const user = await db.getUser(newMember.id);
      if (user && user.verified) {
        user.update({
          verified: false,
          metadata: {
            ...user.metadata,
            verificationRevoked: new Date(),
          },
        });

        await db.saveUser(user);
        logger.info(
          {
            event: 'guildMemberUpdate',
            user: newMember.id,
            username: newMember.user.tag,
            guild: newMember.guild?.id,
            status: 'user_unverified',
          },
          `Updated user record to unverified: ${newMember.user.tag}`
        );
      }
    }
  } catch (error) {
    await handleError(
      error,
      {
        event: 'guildMemberUpdate',
        user: newMember?.user?.id,
        guild: newMember.guild?.id,
      },
      undefined
    );
  }
}

// ------------ DISCORD EVENT EXPORTS

/**
 * Discord.js event: guildMemberAdd
 * @type {{name: string, once: boolean, execute: function(Object, GuildMember): Promise<void>}}
 */
export default {
  name: 'guildMemberAdd',
  once: false,
  async execute(_client, member) {
    await handleMemberJoin(member);
  },
};

/**
 * Discord.js event: guildMemberRemove
 * @type {{name: string, once: boolean, execute: function(Object, GuildMember): Promise<void>}}
 */
export const guildMemberRemove = {
  name: 'guildMemberRemove',
  once: false,
  async execute(_client, member) {
    await handleMemberLeave(member);
  },
};

/**
 * Discord.js event: guildMemberUpdate
 * @type {{name: string, once: boolean, execute: function(Object, GuildMember, GuildMember): Promise<void>}}
 */
export const guildMemberUpdate = {
  name: 'guildMemberUpdate',
  once: false,
  async execute(_client, oldMember, newMember) {
    await handleMemberUpdate(oldMember, newMember);
  },
};
