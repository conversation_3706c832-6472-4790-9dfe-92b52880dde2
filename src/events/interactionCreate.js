/**
 * @file INTERACTION CREATE.JS
 *
 * @version 2.0.3
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Handles Discord slash commands and interactions.
 * Loads commands dynamically and routes Discord interactions to the appropriate command handler.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

import { Collection, MessageFlags } from 'discord.js';

import { botConfig } from '../config/config.js';
import { handleError } from '../utils/errorHandler.js';
import logger from '../utils/logger.js';
import { isPaginationButton } from '../utils/pagination.js';
import { safeReply } from '../utils/replyHelpers.js';

// ------------ TYPE SAFETY ANNOTATIONS
/**
 * @typedef {import('discord.js').Client} DiscordClient
 * @typedef {import('discord.js').Interaction} Interaction
 * @typedef {import('discord.js').Collection<string, object>} CommandCollection
 */

/**
 * @typedef {DiscordClient & { commands: CommandCollection }} Client
 */

// ------------ ESM COMPATIBILITY
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ------------ LOAD COMMANDS
/**
 * Processes a single command file and adds it to the commands collection.
 * @param {string} filePath - Path to the command file
 * @param {string} fileName - Name of the command file
 * @param {string} category - Category name for logging
 * @param {CommandCollection} commands - Collection to add the command to
 * @returns {Promise<void>}
 */
async function processCommandFile(filePath, fileName, category, commands) {
  try {
    // Dynamic import for ESM compatibility
    const commandModule = await import(`file://${filePath}`);
    const command = commandModule.default;

    // Validate command has required properties
    if (!command || !command.data || !command.execute) {
      logger.warn(`Command ${fileName} missing required properties`);
      return;
    }

    commands.set(command.data.name, command);
    logger.info(
      `Loaded command: ${command.data.name} from ${category}/${fileName}`
    );
  } catch (err) {
    logger.error(`Failed to load command ${fileName}:`, err);
  }
}

/**
 * Loads all command modules from the commands directory structure.
 * Commands are organized in category folders.
 *
 * @returns {Promise<CommandCollection>} Collection of command objects keyed by command name
 */
async function loadCommands() {
  // Use Collection for better performance with commands map
  const commands = new Collection();

  try {
    // Resolve commands directory path
    const commandsPath = path.resolve(__dirname, '..', 'commands');

    // Ensure the directory exists
    if (!fs.existsSync(commandsPath)) {
      logger.error(`Commands directory not found at: ${commandsPath}`);
      return commands;
    }

    // Read all category directories using withFileTypes for safety
    const categoryDirents = fs.readdirSync(commandsPath, {
      withFileTypes: true,
    });
    const categories = categoryDirents
      .filter(dirent => dirent.isDirectory() && /^[\w-]+$/.test(dirent.name))
      .map(dirent => dirent.name);

    // Process each category folder
    for (const category of categories) {
      const categoryPath = path.join(commandsPath, category);

      // Additional safety: ensure path is within expected bounds
      if (!categoryPath.startsWith(commandsPath)) continue;

      // Get all JS files in the category, filter for safe filenames
      // eslint-disable-next-line security/detect-non-literal-fs-filename
      const fileDirents = fs.readdirSync(categoryPath, {
        withFileTypes: true,
      });

      const files = fileDirents
        .filter(dirent => dirent.isFile() && /^[\w.-]+\.js$/.test(dirent.name))
        .map(dirent => dirent.name);

      // Load each command file
      for (const file of files) {
        const filePath = path.join(categoryPath, file);
        await processCommandFile(filePath, file, category, commands);
      }
    }

    logger.info(`Successfully loaded ${commands.size} commands`);
    return commands;
  } catch (error) {
    logger.error('Error loading commands:', error);
    return commands;
  }
}

// ------------ INTERACTION HANDLER

/**
 * Discord.js event: interactionCreate
 * Handles Discord interaction events and routes to the correct command.
 *
 * @type {{name: string, once: boolean, execute: function(Client, Interaction): Promise<void>}}
 */
export default {
  name: 'interactionCreate',
  once: false,
  /**
   * Handles Discord interaction events.
   * - Checks if commands are loaded.
   * - Validates the interaction is a command.
   * - Finds and executes the appropriate command handler.
   * - Handles errors and replies safely.
   *
   * @param {Client} client - Discord.js client instance
   * @param {Interaction} interaction - Discord interaction object
   * @returns {Promise<void>}
   */
  async execute(client, interaction) {
    // Use commands loaded at startup
    const commands = client.commands;
    if (!commands) {
      logger.error('Client commands collection is undefined.');
      await safeReply(interaction, {
        content: 'Internal error: commands not loaded.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    // Handle button interactions (pagination)
    if (interaction.isButton() && isPaginationButton(interaction)) {
      // Pagination buttons are handled by the PaginationHandler collector
      // No further action needed here
      return;
    }

    // Early return for non-command interactions
    if (!interaction.isCommand()) return;

    const commandName = interaction.commandName;
    const command = commands.get(commandName);

    // Check if command exists
    logger.debug(`Interaction received: ${commandName}, found: ${!!command}`);

    if (!command) {
      logger.warn(`Unknown command requested: ${commandName}`);
      await safeReply(interaction, {
        content: 'Unknown command. Type `/` to see available commands.',
        flags: [MessageFlags.Ephemeral],
      });
      return;
    }

    try {
      const startTime =
        botConfig.env.APP_ENV === 'development' ? Date.now() : null;
      await command.execute(interaction, client);
      if (startTime) {
        const executionTime = Date.now() - startTime;
        logger.debug(`Command ${commandName} executed in ${executionTime}ms`);
      }
    } catch (error) {
      await handleError(
        error,
        { event: 'interactionCreate', command: commandName },
        client
      );
      const errorMessage = 'There was an error executing this command.';
      await safeReply(interaction, {
        content: errorMessage,
        flags: [MessageFlags.Ephemeral],
      });
    }
  },
};

// ------------ EXPORTS
export { loadCommands };
