/**
 * @file COMMAND AUTHENTICATION.JS
 *
 * @version 2.0.1
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Command permissions middleware utility system for Discord bot commands.
 * Provides role-based access control and common middleware patterns.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { MessageFlags } from 'discord.js';

import { messages } from '../config/messages.js';
import { handleError } from '../utils/errorHandler.js';
import logger from '../utils/logger.js';
import { safeReply } from '../utils/replyHelpers.js';
import {
  hasAdminPermission,
  hasHumanResourcesPermission,
} from '../utils/validators.js';

// ------------ TYPE SAFETY ANNOTATIONS
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 * @typedef {import('discord.js').Client} DiscordClient
 * @typedef {(interaction: ChatInputCommandInteraction, client: DiscordClient) => Promise<any>} CommandExecuteFn
 * @typedef {Object} CommandSecurityOptions
 * @property {string[]} [requiredRoles] - Required role types (admin/hr)
 * @property {boolean} [preventBotTarget] - Whether to block bot users as targets
 * @property {boolean} [deferReply] - Whether to defer the reply automatically (default: false)
 * @property {Array<number>} [flags] - Message flags for the reply (default: 0)
 */

// ------------ MAIN FUNCTION

/**
 * Command middleware factory for role-based security.
 *
 * @param {CommandSecurityOptions} [options={}] - Middleware configuration
 * @returns {(execute: CommandExecuteFn) => CommandExecuteFn}
 */
export function withCommandSecurity(options = {}) {
  return (/** @type {CommandExecuteFn} */ execute) =>
    async (interaction, client) => {
      // Only handle ChatInputCommandInteraction
      if (
        !interaction.isChatInputCommand?.() &&
        interaction.commandName === undefined
      ) {
        return;
      }
      try {
        // Get command name safely for logging
        const commandName =
          typeof interaction.isCommand === 'function' && interaction.isCommand()
            ? interaction.commandName
            : '[non-command interaction]';

        // Contextual logger for command
        /** @type {any} */
        const cmdLogger = logger.child({
          command: commandName,
          user: interaction.user.id,
          guild: interaction.guild?.id || 'DM',
        });

        // Member for permission checks
        const member = interaction.member;

        // Role-based permission checks
        if (options.requiredRoles?.length) {
          if (!member) {
            return await safeReply(interaction, {
              content: 'Unable to determine your server membership.',
              flags: [MessageFlags.Ephemeral],
            });
          }

          // Only proceed if member is a full GuildMember
          const isFullGuildMember =
            typeof member.permissions !== 'undefined' &&
            typeof member.roles !== 'undefined' &&
            typeof member.user !== 'undefined';

          if (!isFullGuildMember) {
            return await safeReply(interaction, {
              content:
                'Unable to verify your permissions. Please try again later.',
              flags: [MessageFlags.Ephemeral],
            });
          }

          const isGuildMember = (
            /** @type {import("discord.js").GuildMember | import("discord.js").APIInteractionGuildMember} */ m
          ) =>
            m &&
            typeof m.permissions !== 'undefined' &&
            typeof m.roles !== 'undefined' &&
            typeof m.user !== 'undefined';

          const hasAccess = options.requiredRoles.some(roleType => {
            if (!isGuildMember(member)) return false;
            /** @type {import('discord.js').GuildMember} */
            const guildMember = /** @type {any} */ (member);
            if (roleType === 'admin') return hasAdminPermission(guildMember);
            if (roleType === 'hr')
              return hasHumanResourcesPermission(guildMember);
            return false;
          });

          if (!hasAccess) {
            cmdLogger.warn({
              event: 'permission_denied',
              requiredRoles: options.requiredRoles.join(','),
            });
            return await safeReply(interaction, {
              content: messages.errors.permissionDenied,
              flags: [MessageFlags.Ephemeral],
            });
          }
        }

        // Proceed with command execution
        return await execute(interaction, client);
      } catch (error) {
        const commandName =
          typeof interaction.isCommand === 'function' && interaction.isCommand()
            ? interaction.commandName
            : '[non-command interaction]';
        const err = error instanceof Error ? error : new Error(String(error));
        await handleError(
          err,
          {
            command: commandName,
            user: interaction.user.id,
            guild: interaction.guild?.id,
          },
          client
        );

        return await safeReply(interaction, {
          content: messages.errors.internalError,
          flags: [MessageFlags.Ephemeral],
        });
      }
    };
}

// ------------ COMMAND MIDDLEWARE FACTORY
/**
 * Common middleware patterns for different command types.
 */
export const CommandMiddleware = {
  /**
   * Middleware for admin-only commands.
   * @param {CommandExecuteFn} execute - Command execution function
   * @returns {CommandExecuteFn}
   */
  adminOnly: execute =>
    withCommandSecurity({
      requiredRoles: ['admin'],
      deferReply: true,
      flags: [MessageFlags.Ephemeral],
    })(execute),

  /**
   * Middleware for HR-only commands.
   * @param {CommandExecuteFn} execute - Command execution function
   * @returns {CommandExecuteFn}
   */
  hrOnly: execute =>
    withCommandSecurity({
      requiredRoles: ['hr'],
      deferReply: true,
      flags: [MessageFlags.Ephemeral],
    })(execute),

  /**
   * Middleware for HR and admin commands.
   * @param {CommandExecuteFn} execute - Command execution function
   * @returns {CommandExecuteFn}
   */
  hrAndAdmin: execute =>
    withCommandSecurity({
      requiredRoles: ['hr', 'admin'],
      deferReply: true,
      flags: [MessageFlags.Ephemeral],
    })(execute),

  /**
   * Middleware for public commands with user targeting.
   * @param {CommandExecuteFn} execute - Command execution function
   * @returns {CommandExecuteFn}
   */
  preventBotTarget: execute =>
    withCommandSecurity({
      preventBotTarget: true,
      deferReply: false,
      flags: [MessageFlags.Ephemeral],
    })(execute),

  // Aliases for DRY usage
  admin: (/** @type {CommandExecuteFn} */ execute) =>
    CommandMiddleware.adminOnly(execute),
  hr: (/** @type {CommandExecuteFn} */ execute) =>
    CommandMiddleware.hrOnly(execute),
};

// ------------ EXPORT
export default withCommandSecurity;
