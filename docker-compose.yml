# ========================================================================== #
# DOCKER COMPOSE CONFIGURATION FOR DIS DISCORD BOT                         #
# ========================================================================== #

version: '3.8'

services:
  # ------------ DISCO<PERSON> BOT SERVICE
  dis-bot:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: dis-discord-bot
    restart: unless-stopped
    environment:
      # Application Configuration
      - NODE_ENV=production
      - APP_ENV=production
      - APP_NAME=dis-bot
      - VERSION=${VERSION:-3.1.1}
      
      # Discord Configuration
      - TOKEN=${TOKEN}
      - APPLICATION_ID=${APPLICATION_ID}
      - CLIENT_ID=${CLIENT_ID}
      - CLIENT_SECRET=${CLIENT_SECRET}
      - PUBLIC_KEY=${PUBLIC_KEY}
      
      # Database Configuration
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=${DB_USERNAME}
      - DB_DATABASE=${DB_DATABASE}
      - DB_PASSWORD=${DB_PASSWORD}
      
      # Security
      - USER_DATA_ENCRYPTION_KEY=${USER_DATA_ENCRYPTION_KEY}
      
      # Discord IDs
      - ADMIN_ROLE_ID=${ADMIN_ROLE_ID}
      - HR_ROLE_ID=${HR_ROLE_ID}
      - VERIFIED_ROLE_ID=${VERIFIED_ROLE_ID}
      - NEW_MEMBER_PINGS_ID=${NEW_MEMBER_PINGS_ID}
      - WELCOME_CHANNEL_ID=${WELCOME_CHANNEL_ID}
      - ADMIN_LOG_CHANNEL_ID=${ADMIN_LOG_CHANNEL_ID}
      - ERROR_LOG_CHANNEL_ID=${ERROR_LOG_CHANNEL_ID}
      - GENERAL_CHANNEL_ID=${GENERAL_CHANNEL_ID}
      
      # External APIs
      - CIRRUS_API_KEY=${CIRRUS_API_KEY}
      - CIRRUS_LOG_API_KEY=${CIRRUS_LOG_API_KEY}
      - ROBLOX_ASSETS_API_KEY=${ROBLOX_ASSETS_API_KEY}
      - ROBLOX_CLIENT_ID=${ROBLOX_CLIENT_ID}
      - ROBLOX_CLIENT_SECRET=${ROBLOX_CLIENT_SECRET}
      
      # Monitoring
      - SENTRY_DSN=${SENTRY_DSN}
      - HEALTH_CHECK_INTERVAL=300000
      - HEALTH_PORT=3000
      - ENABLE_HEALTH_ENDPOINT=true
      
      # Docker specific
      - DEPLOY_COMMANDS=${DEPLOY_COMMANDS:-false}
      - USE_BUNDLE=${USE_BUNDLE:-true}
    
    ports:
      - "3000:3000"  # Health check endpoint
    
    volumes:
      - bot_logs:/app/logs
      - bot_icons:/app/public/icons
    
    depends_on:
      mysql:
        condition: service_healthy
    
    networks:
      - dis-bot-network
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # ------------ MYSQL DATABASE SERVICE
  mysql:
    image: mysql:8.0
    container_name: dis-mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=${DB_DATABASE}
      - MYSQL_USER=${DB_USERNAME}
      - MYSQL_PASSWORD=${DB_PASSWORD}
    
    ports:
      - "3306:3306"
    
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
      - ./docker/mysql/conf:/etc/mysql/conf.d
    
    networks:
      - dis-bot-network
    
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # ------------ REDIS CACHE (OPTIONAL)
  redis:
    image: redis:7-alpine
    container_name: dis-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    
    ports:
      - "6379:6379"
    
    volumes:
      - redis_data:/data
    
    networks:
      - dis-bot-network
    
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

# ------------ VOLUMES
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  bot_logs:
    driver: local
  bot_icons:
    driver: local

# ------------ NETWORKS
networks:
  dis-bot-network:
    driver: bridge
