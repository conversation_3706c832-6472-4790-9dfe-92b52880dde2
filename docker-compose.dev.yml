# ========================================================================== #
# DOCKER COMPOSE DEVELOPMENT CONFIGURATION                                  #
# ========================================================================== #

version: '3.8'

services:
  # ------------ DISCORD BOT SERVICE (DEVELOPMENT)
  dis-bot-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: dis-discord-bot-dev
    restart: unless-stopped
    environment:
      # Application Configuration
      - NODE_ENV=development
      - APP_ENV=development
      - APP_NAME=dis-bot-dev
      - VERSION=${VERSION:-3.1.1}
      
      # Discord Configuration
      - TOKEN=${TOKEN}
      - APPLICATION_ID=${APPLICATION_ID}
      - CLIENT_ID=${CLIENT_ID}
      - CLIENT_SECRET=${CLIENT_SECRET}
      - PUBLIC_KEY=${PUBLIC_KEY}
      
      # Database Configuration
      - DB_HOST=mysql-dev
      - DB_PORT=3306
      - DB_USERNAME=${DB_USERNAME}
      - DB_DATABASE=${DB_DATABASE}_dev
      - DB_PASSWORD=${DB_PASSWORD}
      
      # Security
      - USER_DATA_ENCRYPTION_KEY=${USER_DATA_ENCRYPTION_KEY}
      
      # Discord IDs
      - ADMIN_ROLE_ID=${ADMIN_ROLE_ID}
      - HR_ROLE_ID=${HR_ROLE_ID}
      - VERIFIED_ROLE_ID=${VERIFIED_ROLE_ID}
      - NEW_MEMBER_PINGS_ID=${NEW_MEMBER_PINGS_ID}
      - WELCOME_CHANNEL_ID=${WELCOME_CHANNEL_ID}
      - ADMIN_LOG_CHANNEL_ID=${ADMIN_LOG_CHANNEL_ID}
      - ERROR_LOG_CHANNEL_ID=${ERROR_LOG_CHANNEL_ID}
      - GENERAL_CHANNEL_ID=${GENERAL_CHANNEL_ID}
      
      # External APIs
      - CIRRUS_API_KEY=${CIRRUS_API_KEY}
      - CIRRUS_LOG_API_KEY=${CIRRUS_LOG_API_KEY}
      - ROBLOX_ASSETS_API_KEY=${ROBLOX_ASSETS_API_KEY}
      - ROBLOX_CLIENT_ID=${ROBLOX_CLIENT_ID}
      - ROBLOX_CLIENT_SECRET=${ROBLOX_CLIENT_SECRET}
      
      # Monitoring
      - SENTRY_DSN=${SENTRY_DSN}
      - HEALTH_CHECK_INTERVAL=300000
      - HEALTH_PORT=3000
      - ENABLE_HEALTH_ENDPOINT=true
      
      # Development specific
      - DEPLOY_COMMANDS=true
      - USE_BUNDLE=false
    
    ports:
      - "3001:3000"  # Health check endpoint (different port for dev)
    
    volumes:
      - .:/app
      - /app/node_modules
      - dev_logs:/app/logs
    
    depends_on:
      mysql-dev:
        condition: service_healthy
    
    networks:
      - dis-bot-dev-network
    
    command: ["npm", "run", "dev"]

  # ------------ MYSQL DATABASE SERVICE (DEVELOPMENT)
  mysql-dev:
    image: mysql:8.0
    container_name: dis-mysql-dev
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=${DB_DATABASE}_dev
      - MYSQL_USER=${DB_USERNAME}
      - MYSQL_PASSWORD=${DB_PASSWORD}
    
    ports:
      - "3307:3306"  # Different port for dev
    
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
      - ./docker/mysql/conf:/etc/mysql/conf.d
    
    networks:
      - dis-bot-dev-network
    
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${MYSQL_ROOT_PASSWORD}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # ------------ REDIS CACHE (DEVELOPMENT)
  redis-dev:
    image: redis:7-alpine
    container_name: dis-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes
    
    ports:
      - "6380:6379"  # Different port for dev
    
    volumes:
      - redis_dev_data:/data
    
    networks:
      - dis-bot-dev-network

# ------------ VOLUMES
volumes:
  mysql_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  dev_logs:
    driver: local

# ------------ NETWORKS
networks:
  dis-bot-dev-network:
    driver: bridge
