# ========================================================================== #
# DEVELOPMENT DOCKERFILE FOR DIS DISCORD BOT                                #
# ========================================================================== #

FROM node:22-alpine AS development

# Set environment variables
ENV NODE_ENV=development
ENV APP_ENV=development

# Create app user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S botuser -u 1001

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    dumb-init \
    curl \
    git \
    python3 \
    make \
    g++

# Copy package files
COPY package.json yarn.lock ./

# Install all dependencies (including dev dependencies)
RUN yarn install --frozen-lockfile

# Create necessary directories
RUN mkdir -p logs public/icons && \
    chown -R botuser:nodejs /app

# Copy source code
COPY . .

# Switch to non-root user
USER botuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:${HEALTH_PORT:-3000}/health || exit 1

# Expose health check port
EXPOSE 3000

# Use dumb-init to handle signals properly
ENTRYPOINT ["/usr/bin/dumb-init", "--"]

# Start in development mode with nodemon
CMD ["npm", "run", "dev"]
