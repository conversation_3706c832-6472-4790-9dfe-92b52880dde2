# CI/CD Setup Guide

This guide covers setting up the complete CI/CD pipeline for the DIS Discord Bot using GitHub Actions.

## 🏗️ Pipeline Overview

The CI/CD pipeline consists of the following stages:

```mermaid
graph LR
    A[Code Push] --> B[Validate]
    B --> C[Lint]
    C --> D[Test]
    D --> E[Build]
    E --> F[Docker Build]
    F --> G{Branch?}
    G -->|develop| H[Deploy Staging]
    G -->|main| I[Deploy Production]
    H --> J[Health Check]
    I --> K[Health Check]
    K --> L[Create Release]
```

## 🔧 GitHub Secrets Configuration

### Required Secrets

Navigate to your GitHub repository → Settings → Secrets and variables → Actions, and add the following secrets:

#### Staging Environment Secrets

```bash
# Discord Configuration
STAGING_DISCORD_TOKEN=your_staging_bot_token
STAGING_APPLICATION_ID=your_staging_application_id
STAGING_CLIENT_ID=your_staging_client_id
STAGING_CLIENT_SECRET=your_staging_client_secret
STAGING_PUBLIC_KEY=your_staging_public_key

# Database Configuration
STAGING_DB_USERNAME=staging_db_user
STAGING_DB_DATABASE=dis_bot_staging
STAGING_DB_PASSWORD=staging_secure_password
STAGING_MYSQL_ROOT_PASSWORD=staging_root_password

# Security
STAGING_ENCRYPTION_KEY=your_32_character_staging_key

# Discord IDs (Staging Server)
STAGING_ADMIN_ROLE_ID=staging_admin_role_id
STAGING_HR_ROLE_ID=staging_hr_role_id
STAGING_VERIFIED_ROLE_ID=staging_verified_role_id
STAGING_NEW_MEMBER_PINGS_ID=staging_new_member_pings_id
STAGING_WELCOME_CHANNEL_ID=staging_welcome_channel_id
STAGING_ADMIN_LOG_CHANNEL_ID=staging_admin_log_channel_id
STAGING_ERROR_LOG_CHANNEL_ID=staging_error_log_channel_id
STAGING_GENERAL_CHANNEL_ID=staging_general_channel_id

# External APIs (Staging)
STAGING_CIRRUS_API_KEY=staging_cirrus_api_key
STAGING_CIRRUS_LOG_API_KEY=staging_cirrus_log_api_key
STAGING_ROBLOX_ASSETS_API_KEY=staging_roblox_assets_api_key
STAGING_ROBLOX_CLIENT_ID=staging_roblox_client_id
STAGING_ROBLOX_CLIENT_SECRET=staging_roblox_client_secret

# Monitoring (Staging)
STAGING_SENTRY_DSN=staging_sentry_dsn
```

#### Production Environment Secrets

```bash
# Discord Configuration
PRODUCTION_DISCORD_TOKEN=your_production_bot_token
PRODUCTION_APPLICATION_ID=your_production_application_id
PRODUCTION_CLIENT_ID=your_production_client_id
PRODUCTION_CLIENT_SECRET=your_production_client_secret
PRODUCTION_PUBLIC_KEY=your_production_public_key

# Database Configuration
PRODUCTION_DB_USERNAME=production_db_user
PRODUCTION_DB_DATABASE=dis_bot
PRODUCTION_DB_PASSWORD=production_secure_password
PRODUCTION_MYSQL_ROOT_PASSWORD=production_root_password

# Security
PRODUCTION_ENCRYPTION_KEY=your_32_character_production_key

# Discord IDs (Production Server)
PRODUCTION_ADMIN_ROLE_ID=production_admin_role_id
PRODUCTION_HR_ROLE_ID=production_hr_role_id
PRODUCTION_VERIFIED_ROLE_ID=production_verified_role_id
PRODUCTION_NEW_MEMBER_PINGS_ID=production_new_member_pings_id
PRODUCTION_WELCOME_CHANNEL_ID=production_welcome_channel_id
PRODUCTION_ADMIN_LOG_CHANNEL_ID=production_admin_log_channel_id
PRODUCTION_ERROR_LOG_CHANNEL_ID=production_error_log_channel_id
PRODUCTION_GENERAL_CHANNEL_ID=production_general_channel_id

# External APIs (Production)
PRODUCTION_CIRRUS_API_KEY=production_cirrus_api_key
PRODUCTION_CIRRUS_LOG_API_KEY=production_cirrus_log_api_key
PRODUCTION_ROBLOX_ASSETS_API_KEY=production_roblox_assets_api_key
PRODUCTION_ROBLOX_CLIENT_ID=production_roblox_client_id
PRODUCTION_ROBLOX_CLIENT_SECRET=production_roblox_client_secret

# Monitoring (Production)
PRODUCTION_SENTRY_DSN=production_sentry_dsn
```

#### Optional Notification Secrets

```bash
# Slack Integration (Optional)
SLACK_WEBHOOK=your_slack_webhook_url

# Teams Integration (Optional)
TEAMS_WEBHOOK=your_teams_webhook_url

# Email Notifications (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
```

## 🚀 Deployment Environments

### GitHub Environments Setup

1. Go to your repository → Settings → Environments
2. Create two environments:
   - `staging`
   - `production`

3. Configure environment protection rules:

#### Staging Environment

- **Deployment branches**: `develop` branch only
- **Environment secrets**: Use staging secrets
- **Reviewers**: Optional (for automatic deployments)

#### Production Environment

- **Deployment branches**: `main` branch only
- **Environment secrets**: Use production secrets
- **Reviewers**: Required (add team leads/admins)
- **Wait timer**: 5 minutes (optional safety delay)

## 🔄 Workflow Triggers

### Automatic Triggers

1. **Push to `develop`**: Triggers staging deployment
2. **Push to `main`**: Triggers production deployment
3. **Pull Request**: Runs validation, linting, and testing

### Manual Triggers

1. **Workflow Dispatch**: Manual deployment to staging or production
2. **Repository Dispatch**: External trigger via API

## 🧪 Testing in CI/CD

### Test Coverage Requirements

The pipeline enforces minimum test coverage:

- **Overall**: 60% minimum
- **Commands**: 70% minimum
- **Models**: 80% minimum
- **Utils**: 75% minimum

### Test Execution

```bash
# Unit Tests
npm run test:unit

# Integration Tests
npm run test:integration

# E2E Tests (future)
npm run test:e2e

# Coverage Report
npm run test:coverage
```

## 🐳 Docker Registry

### GitHub Container Registry

The pipeline automatically builds and pushes Docker images to GitHub Container Registry (ghcr.io).

#### Image Tags

- `latest`: Latest main branch build
- `develop`: Latest develop branch build
- `sha-<commit>`: Specific commit builds
- `v*`: Release tags

#### Pulling Images

```bash
# Latest production image
docker pull ghcr.io/dynamic-innovative-studio/dis-work-discord-bot:latest

# Specific version
docker pull ghcr.io/dynamic-innovative-studio/dis-work-discord-bot:sha-abc1234

# Development image
docker pull ghcr.io/dynamic-innovative-studio/dis-work-discord-bot:develop
```

## 🔍 Monitoring & Notifications

### Pipeline Monitoring

1. **GitHub Actions**: View workflow runs in the Actions tab
2. **Status Checks**: Required for pull requests
3. **Deployment Status**: Visible in the Environments section

### Notification Setup

#### Slack Notifications

Add to your Slack workspace:

```bash
# Webhook URL format
*****************************************************************************
```

Example notification payload:

```json
{
  "text": "🚀 DIS Bot deployed to production",
  "attachments": [
    {
      "color": "good",
      "fields": [
        {
          "title": "Environment",
          "value": "Production",
          "short": true
        },
        {
          "title": "Commit",
          "value": "abc1234",
          "short": true
        }
      ]
    }
  ]
}
```

## 🛠️ Troubleshooting

### Common CI/CD Issues

#### 1. Test Failures

```bash
# Check test output
cat test-results.xml

# Run tests locally
npm test

# Check coverage
npm run coverage:report
```

#### 2. Docker Build Failures

```bash
# Check Dockerfile syntax
docker build --no-cache .

# Verify base image
docker pull node:22.15.0-alpine

# Check build context
docker build --progress=plain .
```

#### 3. Deployment Failures

```bash
# Check environment variables
echo $STAGING_DISCORD_TOKEN | wc -c

# Verify secrets are set
# (Check in GitHub repository settings)

# Test Docker Compose locally
docker-compose config
```

#### 4. Health Check Failures

```bash
# Check service logs
docker-compose logs dis-bot

# Test health endpoint
curl -f http://localhost:3000/health

# Check database connectivity
docker-compose exec dis-bot ping mysql
```

### Debug Mode

Enable debug logging in GitHub Actions:

1. Go to repository Settings → Secrets
2. Add secret: `ACTIONS_STEP_DEBUG` = `true`
3. Add secret: `ACTIONS_RUNNER_DEBUG` = `true`

## 📋 Deployment Checklist

### Pre-Deployment

- [ ] All tests passing
- [ ] Code reviewed and approved
- [ ] Environment secrets configured
- [ ] Database migrations ready (if any)
- [ ] Monitoring alerts configured

### During Deployment

- [ ] Monitor deployment logs
- [ ] Verify health checks pass
- [ ] Check application metrics
- [ ] Validate core functionality

### Post-Deployment

- [ ] Verify bot is online in Discord
- [ ] Test critical commands
- [ ] Check error logs
- [ ] Monitor performance metrics
- [ ] Update documentation if needed

## 🔐 Security Best Practices

### Secrets Management

1. **Rotate secrets regularly** (every 90 days)
2. **Use different secrets** for each environment
3. **Never log secrets** in CI/CD output
4. **Limit secret access** to necessary workflows only

### Access Control

1. **Require reviews** for production deployments
2. **Use branch protection** rules
3. **Limit repository access** to team members only
4. **Enable two-factor authentication** for all team members

### Monitoring

1. **Set up alerts** for deployment failures
2. **Monitor resource usage** in production
3. **Track deployment frequency** and success rate
4. **Review security logs** regularly

## 📚 Additional Resources

- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Docker Documentation](https://docs.docker.com/)
- [GitHub Container Registry](https://docs.github.com/en/packages/working-with-a-github-packages-registry/working-with-the-container-registry)
- [Slack Webhooks](https://api.slack.com/messaging/webhooks)
- [Sentry Integration](https://docs.sentry.io/)
