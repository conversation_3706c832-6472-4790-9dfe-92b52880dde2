# Docker Deployment Guide

This guide covers deploying the DIS Discord Bot using Docker and Docker Compose.

## 📋 Prerequisites

- **Docker** v20.10+ and **Docker Compose** v2.0+
- **Git** for cloning the repository
- **Environment variables** configured (see [Environment Setup](#environment-setup))

## 🚀 Quick Start

### 1. <PERSON>lone and Setup

```bash
git clone https://github.com/Dynamic-Innovative-Studio/DIS-Work-Discord-Bot.git
cd DIS-Work-Discord-Bot
```

### 2. Environment Configuration

Copy the Docker environment template:

```bash
cp .env.docker .env
```

Edit `.env` with your actual values:

```bash
# Required Discord Configuration
TOKEN=your_discord_bot_token
APPLICATION_ID=your_application_id
CLIENT_ID=your_client_id
CLIENT_SECRET=your_client_secret
PUBLIC_KEY=your_public_key

# Database Configuration
DB_USERNAME=dis_bot_user
DB_DATABASE=dis_bot
DB_PASSWORD=your_secure_db_password
MYSQL_ROOT_PASSWORD=your_secure_root_password

# Security
USER_DATA_ENCRYPTION_KEY=your_32_character_encryption_key

# Discord IDs (get these from your Discord server)
ADMIN_ROLE_ID=your_admin_role_id
HR_ROLE_ID=your_hr_role_id
VERIFIED_ROLE_ID=your_verified_role_id
WELCOME_CHANNEL_ID=your_welcome_channel_id
ADMIN_LOG_CHANNEL_ID=your_admin_log_channel_id
ERROR_LOG_CHANNEL_ID=your_error_log_channel_id
GENERAL_CHANNEL_ID=your_general_channel_id

# External APIs (optional)
CIRRUS_API_KEY=your_cirrus_api_key
ROBLOX_CLIENT_ID=your_roblox_client_id
SENTRY_DSN=your_sentry_dsn
```

### 3. Deploy

#### Production Deployment

```bash
# Using the deployment script (recommended)
./scripts/deploy.sh --environment production

# Or manually with Docker Compose
docker-compose up -d
```

#### Development Deployment

```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f dis-bot-dev
```

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `APP_ENV` | Application environment | Yes | `production` |
| `TOKEN` | Discord bot token | Yes | - |
| `APPLICATION_ID` | Discord application ID | Yes | - |
| `DB_HOST` | Database hostname | Yes | `mysql` |
| `DB_USERNAME` | Database username | Yes | - |
| `DB_PASSWORD` | Database password | Yes | - |
| `USER_DATA_ENCRYPTION_KEY` | 32-char encryption key | Yes | - |
| `DEPLOY_COMMANDS` | Deploy slash commands on startup | No | `false` |
| `USE_BUNDLE` | Use bundled version | No | `true` |
| `HEALTH_PORT` | Health check port | No | `3000` |

### Docker Compose Profiles

#### Production (`docker-compose.yml`)

- Optimized for production use
- Multi-stage build with minimal image size
- Health checks enabled
- Persistent volumes for data
- Security hardening

#### Development (`docker-compose.dev.yml`)

- Hot reload with volume mounts
- Development dependencies included
- Debug logging enabled
- Separate database instance

## 🏗️ Architecture

```mmd
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Discord Bot   │    │      MySQL      │    │      Redis      │
│   (Node.js)     │◄──►│   (Database)    │    │    (Cache)      │
│                 │    │                 │    │   (Optional)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Docker Host   │
                    │    Network      │
                    └─────────────────┘
```

## 🔍 Monitoring & Health Checks

### Health Check Endpoint

The bot exposes a health check endpoint at `http://localhost:3000/health`:

```bash
# Check bot health
curl http://localhost:3000/health

# Response
{
  "status": "ok",
  "uptime": 3600
}
```

### Container Health

```bash
# Check container health
docker-compose ps

# View container logs
docker-compose logs dis-bot

# Follow logs in real-time
docker-compose logs -f dis-bot
```

### Database Health

```bash
# Check MySQL health
docker-compose exec mysql mysqladmin ping -h localhost -u root -p

# Connect to database
docker-compose exec mysql mysql -u dis_bot_user -p dis_bot
```

## 🛠️ Management Commands

### Service Management

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# Restart specific service
docker-compose restart dis-bot

# View service status
docker-compose ps

# Scale services (if needed)
docker-compose up -d --scale dis-bot=2
```

### Database Management

```bash
# Backup database
docker-compose exec mysql mysqldump -u dis_bot_user -p dis_bot > backup.sql

# Restore database
docker-compose exec -T mysql mysql -u dis_bot_user -p dis_bot < backup.sql

# Access MySQL shell
docker-compose exec mysql mysql -u dis_bot_user -p dis_bot
```

### Log Management

```bash
# View all logs
docker-compose logs

# View specific service logs
docker-compose logs dis-bot

# Follow logs with timestamps
docker-compose logs -f -t dis-bot

# View last 100 lines
docker-compose logs --tail=100 dis-bot
```

## 🔄 Updates & Maintenance

### Updating the Bot

```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
docker-compose down
docker-compose build --no-cache
docker-compose up -d

# Or use the deployment script
./scripts/deploy.sh --environment production
```

### Database Migrations

```bash
# Run database migrations (if any)
docker-compose exec dis-bot node scripts/migrate.js

# Or manually execute SQL
docker-compose exec mysql mysql -u dis_bot_user -p dis_bot < migrations/001_add_new_table.sql
```

### Cleanup

```bash
# Remove unused images
docker image prune -f

# Remove unused volumes
docker volume prune -f

# Remove unused networks
docker network prune -f

# Complete cleanup (be careful!)
docker system prune -af
```

## 🚨 Troubleshooting

### Common Issues

#### Bot Won't Start

1. **Check environment variables:**

   ```bash
   docker-compose config
   ```

2. **Check logs:**

   ```bash
   docker-compose logs dis-bot
   ```

3. **Verify Discord token:**

   ```bash
   # Test token validity
   curl -H "Authorization: Bot YOUR_TOKEN" https://discord.com/api/v10/users/@me
   ```

#### Database Connection Issues

1. **Check MySQL status:**

   ```bash
   docker-compose ps mysql
   docker-compose logs mysql
   ```

2. **Test connection:**

   ```bash
   docker-compose exec mysql mysqladmin ping -h localhost -u root -p
   ```

3. **Check network connectivity:**

   ```bash
   docker-compose exec dis-bot ping mysql
   ```

#### Permission Issues

1. **Check file permissions:**

   ```bash
   ls -la docker/entrypoint.sh
   chmod +x docker/entrypoint.sh
   ```

2. **Check Docker permissions:**

   ```bash
   sudo usermod -aG docker $USER
   # Logout and login again
   ```

### Performance Issues

1. **Monitor resource usage:**

   ```bash
   docker stats
   ```

2. **Check container limits:**

   ```bash
   docker-compose config
   ```

3. **Optimize memory usage:**

   ```bash
   # Add memory limits to docker-compose.yml
   deploy:
     resources:
       limits:
         memory: 512M
   ```

## 🔐 Security Considerations

### Environment Security

- Never commit `.env` files to version control
- Use strong passwords for database
- Rotate encryption keys regularly
- Use secrets management in production

### Container Security

- Run containers as non-root user (already configured)
- Keep base images updated
- Scan images for vulnerabilities
- Use minimal base images (Alpine Linux)

### Network Security

- Use internal Docker networks
- Expose only necessary ports
- Implement proper firewall rules
- Use TLS for external connections

## 📚 Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [Discord.js Documentation](https://discord.js.org/)
- [MySQL Docker Documentation](https://hub.docker.com/_/mysql)
