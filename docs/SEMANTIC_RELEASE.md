# Semantic Release Documentation

This document explains the automated release process using semantic-release for the DIS Work Discord Bot.

## Overview

Starting from version 3.0.1, this project uses [semantic-release](https://semantic-release.gitbook.io/) to automate the release process. This includes:

- Automatic version bumping based on commit messages
- Automatic changelog generation
- Automatic GitHub releases
- Automatic git tagging

## Configuration

### Files

- **`.releaserc.json`** - Main semantic-release configuration
- **`.github/workflows/semantic-release.yml`** - GitHub Actions workflow
- **`CHANGELOG.md`** - Auto-generated changelog

### Plugins Used

1. **@semantic-release/commit-analyzer** - Analyzes commits to determine release type
2. **@semantic-release/release-notes-generator** - Generates release notes
3. **@semantic-release/changelog** - Updates CHANGELOG.md
4. **@semantic-release/npm** - Handles package.json version (no publishing)
5. **@semantic-release/github** - Creates GitHub releases
6. **@semantic-release/git** - Commits changelog and version updates

## Conventional Commits

This project follows the [Conventional Commits](https://conventionalcommits.org/) specification.

### Commit Message Format

```zsh
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### Commit Types

| Type | Description | Release Type |
|------|-------------|--------------|
| `feat` | A new feature | Minor |
| `fix` | A bug fix | Patch |
| `docs` | Documentation only changes | Patch |
| `style` | Changes that do not affect the meaning of the code | Patch |
| `refactor` | A code change that neither fixes a bug nor adds a feature | Patch |
| `perf` | A code change that improves performance | Patch |
| `test` | Adding missing tests or correcting existing tests | Patch |
| `chore` | Changes to the build process or auxiliary tools | No release |
| `ci` | Changes to CI configuration files and scripts | No release |
| `build` | Changes that affect the build system or external dependencies | No release |

### Breaking Changes

To trigger a major version release, include `BREAKING CHANGE:` in the commit footer or use `!` after the type:

```bash
feat!: remove deprecated API endpoint

BREAKING CHANGE: The old /api/v1 endpoint has been removed. Use /api/v2 instead.
```

### Examples

#### Feature Addition (Minor Release)

```bash
feat(commands): add new moderation command

Add /timeout command for temporary user restrictions
```

#### Bug Fix (Patch Release)

```bash
fix(database): resolve connection timeout issues

Fix MySQL connection pool configuration to prevent timeouts
```

#### Documentation Update (Patch Release)

```bash
docs: update installation instructions

Add Node.js version requirements and MySQL setup steps
```

#### Breaking Change (Major Release)

```bash
feat!: migrate to Discord.js v14

BREAKING CHANGE: Updated to Discord.js v14. Bot permissions and intents have changed.
```

#### No Release

```bash
chore: update dependencies

Update development dependencies to latest versions
```

## Release Process

### Automatic Releases

1. **Commit Analysis**: semantic-release analyzes all commits since the last release
2. **Version Calculation**: Determines the next version based on commit types
3. **Changelog Generation**: Updates CHANGELOG.md with new entries
4. **Version Bump**: Updates package.json version
5. **Git Tag**: Creates a new git tag
6. **GitHub Release**: Creates a GitHub release with release notes
7. **Commit Changes**: Commits the updated changelog and version

### Manual Trigger

Releases are automatically triggered on pushes to the `main` branch. You can also manually trigger a release:

1. Go to GitHub Actions
2. Select the "Release" workflow
3. Click "Run workflow"

### Release Branches

- **main**: Production releases
- **develop**: Pre-releases (if configured)

## Workflow Integration

### GitHub Actions

The release workflow (`.github/workflows/semantic-release.yml`) runs:

1. **On push to main**: Automatic release
2. **Node.js 22.15.0**: Consistent environment
3. **Dependencies**: Installs with `yarn install --frozen-lockfile`
4. **Release**: Runs `yarn release`

### Required Secrets

- **GITHUB_TOKEN**: Automatically provided by GitHub Actions

### Permissions

The workflow needs:

- **contents: write** - To create releases and update files
- **issues: write** - To close issues referenced in commits
- **pull-requests: write** - To comment on PRs

## Best Practices

### Commit Messages

1. **Be descriptive**: Clearly explain what the change does
2. **Use imperative mood**: "Add feature" not "Added feature"
3. **Include scope**: Specify the area of change when relevant
4. **Reference issues**: Include issue numbers when applicable

### Examples of Good Commits

```bash
feat(auth): implement OAuth2 authentication
fix(commands): resolve permission check for admin commands
docs(api): add JSDoc comments to utility functions
test(validators): add unit tests for email validation
refactor(database): extract connection logic to separate module
```

### Examples of Bad Commits

```bash
update stuff
fix bug
changes
wip
asdf
```

### Development Workflow

1. **Create feature branch**: `git checkout -b feature/new-command`
2. **Make changes**: Implement your feature
3. **Commit with conventional format**: `git commit -m "feat(commands): add new help command"`
4. **Create pull request**: Target the `main` branch
5. **Merge after review**: semantic-release will handle the rest

## Troubleshooting

### Common Issues

#### No Release Generated

- **Cause**: No commits with release-triggering types since last release
- **Solution**: Ensure commits use `feat`, `fix`, or other release types

#### Release Failed

- **Cause**: Missing permissions or configuration issues
- **Solution**: Check GitHub Actions logs and verify GITHUB_TOKEN permissions

#### Wrong Version Bump

- **Cause**: Incorrect commit type or missing breaking change notation
- **Solution**: Use correct conventional commit format

#### Changelog Not Updated

- **Cause**: semantic-release/changelog plugin not configured
- **Solution**: Verify `.releaserc.json` includes changelog plugin

### Debug Commands

```bash
# Dry run to see what would be released
npx semantic-release --dry-run

# Run with debug output
npx semantic-release --debug

# Verify commit format
npx commitizen init cz-conventional-changelog --save-dev --save-exact
```

## Migration from Manual Releases

### Before Semantic Release (≤ 3.0.0)

- Manual version bumping in package.json
- Manual CHANGELOG.md updates
- Manual git tagging
- Manual GitHub releases

### After Semantic Release (≥ 3.0.1)

- Automatic version management
- Auto-generated changelog entries
- Automatic git tags
- Automatic GitHub releases with assets

### Historical Changelog

The manual changelog entries for versions ≤ 3.0.0 are preserved in CHANGELOG.md under the "Manual Change Log" section.

## Resources

- [Semantic Release Documentation](https://semantic-release.gitbook.io/)
- [Conventional Commits Specification](https://conventionalcommits.org/)
- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Commitizen CLI](https://github.com/commitizen/cz-cli) - Tool to help write conventional commits
