# C++ Code Standards

This document outlines the C++ coding standards and conventions that should be followed across all projects within the Dynamic Innovative Studio organization. These standards are designed to ensure consistency, readability, quality, and maintainability of the C++ codebase while following modern C++ best practices.

## Table of Contents

- [C++ Code Standards](#c-code-standards)
  - [Table of Contents](#table-of-contents)
  - [Overview](#overview)
    - [Core Principles](#core-principles)
  - [File Structure and Headers](#file-structure-and-headers)
    - [File Header Format](#file-header-format)
    - [Header Guidelines](#header-guidelines)
    - [File Organization Structure](#file-organization-structure)
  - [Code Organization](#code-organization)
    - [Section Comments](#section-comments)
    - [Include Organization](#include-organization)
    - [Namespace Organization](#namespace-organization)
  - [Naming Conventions](#naming-conventions)
    - [Variables and Functions](#variables-and-functions)
    - [Constants](#constants)
    - [Classes and Types](#classes-and-types)
    - [Member Variables](#member-variables)
  - [Variable Declarations](#variable-declarations)
    - [Modern C++ Variable Declarations](#modern-c-variable-declarations)
    - [Type Safety and Strong Types](#type-safety-and-strong-types)
  - [Functions and Methods](#functions-and-methods)
    - [Function Signatures and Attributes](#function-signatures-and-attributes)
    - [Lambda Expressions](#lambda-expressions)
  - [Classes and Objects](#classes-and-objects)
    - [Class Design Principles](#class-design-principles)
  - [Templates and Generics](#templates-and-generics)
    - [Template Design](#template-design)
    - [Template Metaprogramming](#template-metaprogramming)
  - [Memory Management](#memory-management)
    - [Smart Pointers and RAII](#smart-pointers-and-raii)
  - [Error Handling](#error-handling)
    - [Exception Safety and Error Handling](#exception-safety-and-error-handling)
  - [Concurrency and Threading](#concurrency-and-threading)
    - [Thread Safety and Synchronization](#thread-safety-and-synchronization)
  - [Comments and Documentation](#comments-and-documentation)
    - [Documentation Standards](#documentation-standards)
    - [Comment Guidelines](#comment-guidelines)
  - [Code Quality and Linting](#code-quality-and-linting)
    - [C++ Code Quality Tools](#c-code-quality-tools)
    - [Code Quality Practices](#code-quality-practices)
  - [Testing Standards](#testing-standards)
    - [Test File Structure](#test-file-structure)
  - [Performance Guidelines](#performance-guidelines)
    - [Optimization Techniques](#optimization-techniques)
  - [Security Considerations](#security-considerations)
    - [Secure Coding Practices](#secure-coding-practices)
  - [References](#references)

## Overview

C++ is a general-purpose programming language that supports procedural, object-oriented, and generic programming. These standards ensure that C++ code written within the DIS organization maintains high quality, consistency, and follows modern C++ practices (C++17/C++20 and beyond).

### Core Principles

- **Modern C++**: Use modern C++ features (C++17/C++20+) and best practices
- **RAII**: Resource Acquisition Is Initialization for automatic resource management
- **Type Safety**: Leverage C++'s strong type system for better code reliability
- **Performance**: Write efficient code while maintaining readability
- **Consistency**: Maintain consistent coding style across all C++ files
- **Safety**: Avoid common C++ pitfalls and undefined behavior
- **Maintainability**: Structure code for easy maintenance and updates

## File Structure and Headers

### File Header Format

All C++ files must include a standardized header comment block at the top of the file:

```cpp
/**
 * @file FILENAME.CPP
 *
 * @version 1.0.0
 * <AUTHOR>
 * @contributors
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * Brief description of the file's purpose and functionality.
 * Additional details about the module or component.
 */
```

### Header Guidelines

- **@file**: Use UPPERCASE filename with .CPP, .HPP, .H, or .CXX extension
- **@version**: Follow semantic versioning (MAJOR.MINOR.PATCH)
- **@author**: Primary author of the file
- **@contributors**: List additional contributors (one per line)
- **@copyright**: Always "Dynamic Innovative Studio"
- **@description**: Clear, concise description of file purpose

### File Organization Structure

```cpp
/**
 * @file USER_SERVICE.HPP
 *
 * @version 1.0.0
 * <AUTHOR>
 * @contributors
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * User service for managing user data and operations.
 * Provides CRUD operations and user validation functionality.
 */

#pragma once

// ------------ SYSTEM INCLUDES
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <optional>
#include <future>

// ------------ THIRD-PARTY INCLUDES
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>

// ------------ PROJECT INCLUDES
#include "user.hpp"
#include "database_connection.hpp"
#include "validation_error.hpp"
#include "not_found_error.hpp"

// ------------ FORWARD DECLARATIONS
namespace dis::database {
    class Connection;
}

// ------------ CONSTANTS
namespace dis::constants {
    constexpr std::size_t DEFAULT_PAGE_SIZE = 20;
    constexpr std::size_t MAX_PAGE_SIZE = 100;
    constexpr std::chrono::seconds CACHE_TTL{300};
}

// ------------ TYPE ALIASES
using UserPtr = std::shared_ptr<User>;
using UserList = std::vector<UserPtr>;
using UserMap = std::unordered_map<std::string, UserPtr>;

// ------------ MAIN IMPLEMENTATION
namespace dis::services {

class UserService {
public:
    explicit UserService(std::shared_ptr<database::Connection> db_connection);
    ~UserService() = default;

    // Non-copyable, movable
    UserService(const UserService&) = delete;
    UserService& operator=(const UserService&) = delete;
    UserService(UserService&&) = default;
    UserService& operator=(UserService&&) = default;

    [[nodiscard]] std::future<UserPtr> create_user(const nlohmann::json& user_data);
    [[nodiscard]] std::future<std::optional<UserPtr>> get_user_by_id(const std::string& user_id);

private:
    std::shared_ptr<database::Connection> db_connection_;
    std::shared_ptr<spdlog::logger> logger_;
    UserMap cache_;
};

} // namespace dis::services
```

## Code Organization

### Section Comments

Use standardized section comments to organize code within files:

```cpp
// ------------ SYSTEM INCLUDES
// ------------ THIRD-PARTY INCLUDES
// ------------ PROJECT INCLUDES
// ------------ FORWARD DECLARATIONS
// ------------ CONSTANTS
// ------------ TYPE ALIASES
// ------------ MAIN IMPLEMENTATION
```

### Include Organization

Organize includes in the following order:

1. **System/Standard library includes**
2. **Third-party library includes**
3. **Project-specific includes**

```cpp
// ------------ SYSTEM INCLUDES
#include <algorithm>
#include <chrono>
#include <iostream>
#include <memory>
#include <string>
#include <vector>
#include <unordered_map>
#include <optional>
#include <future>
#include <thread>
#include <mutex>

// ------------ THIRD-PARTY INCLUDES
#include <boost/algorithm/string.hpp>
#include <fmt/format.h>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>
#include <gtest/gtest.h>

// ------------ PROJECT INCLUDES
#include "config/app_config.hpp"
#include "database/connection.hpp"
#include "models/user.hpp"
#include "services/validation_service.hpp"
#include "utils/string_utils.hpp"
```

### Namespace Organization

Use nested namespaces for clear organization:

```cpp
// ------------ CONSTANTS
namespace dis::constants {
    constexpr std::size_t MAX_USERNAME_LENGTH = 50;
    constexpr std::size_t MIN_PASSWORD_LENGTH = 8;
    constexpr std::chrono::seconds DEFAULT_TIMEOUT{30};

    namespace database {
        constexpr std::size_t MAX_CONNECTIONS = 100;
        constexpr std::chrono::seconds CONNECTION_TIMEOUT{10};
    }

    namespace validation {
        constexpr std::string_view EMAIL_PATTERN = R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})";
        constexpr std::size_t MAX_EMAIL_LENGTH = 254;
    }
}

// ------------ TYPE ALIASES
namespace dis::types {
    using UserId = std::string;
    using UserPtr = std::shared_ptr<User>;
    using UserList = std::vector<UserPtr>;
    using UserMap = std::unordered_map<UserId, UserPtr>;
    using ValidationResult = std::pair<bool, std::string>;
}
```

## Naming Conventions

### Variables and Functions

Use **snake_case** for variables and functions:

```cpp
// Variables
std::string user_name = "john_doe";
bool is_authenticated = true;
std::unordered_map<std::string, std::string> user_preferences;
std::size_t total_count = 0;

// Functions
[[nodiscard]] double calculate_total_price(const std::vector<Item>& items) {
    return std::accumulate(items.begin(), items.end(), 0.0,
        [](double sum, const Item& item) { return sum + item.get_price(); });
}

[[nodiscard]] bool validate_user_input(const nlohmann::json& user_data) {
    // Implementation
    return true;
}

[[nodiscard]] std::future<std::optional<User>> fetch_user_data(const std::string& user_id) {
    // Implementation
    return {};
}
```

### Constants

Use **SCREAMING_SNAKE_CASE** for constants:

```cpp
constexpr std::size_t MAX_RETRY_ATTEMPTS = 3;
constexpr std::string_view API_BASE_URL = "https://api.example.com";
constexpr std::size_t DEFAULT_PAGE_SIZE = 20;
constexpr std::chrono::seconds CACHE_EXPIRY{3600};
```

### Classes and Types

Use **PascalCase** for classes, structs, and type names:

```cpp
class UserManager {
public:
    explicit UserManager(std::string database_url);

    [[nodiscard]] std::future<User> create_user(const UserData& data);

private:
    std::string database_url_;
    std::shared_ptr<spdlog::logger> logger_;
};

class ApiClient {
public:
    explicit ApiClient(std::string base_url, std::chrono::seconds timeout = std::chrono::seconds{30});

    [[nodiscard]] std::future<nlohmann::json> get(const std::string& endpoint);

private:
    std::string base_url_;
    std::chrono::seconds timeout_;
};

struct DatabaseConnection {
    std::string connection_string;
    std::size_t max_connections;
    std::chrono::seconds timeout;
};

enum class UserStatus {
    Active,
    Inactive,
    Pending,
    Suspended
};
```

### Member Variables

Use trailing underscore for private member variables:

```cpp
class DataProcessor {
public:
    explicit DataProcessor(std::size_t cache_size);

    [[nodiscard]] bool process_data(const std::vector<std::byte>& data);

private:
    // Private member variables with trailing underscore
    std::unordered_map<std::string, std::string> cache_;
    std::string secret_key_;
    std::vector<std::string> public_data_;
    std::mutex cache_mutex_;

    // Private methods
    [[nodiscard]] bool validate_data_(const std::vector<std::byte>& data) const;
    [[nodiscard]] std::string encrypt_data_(const std::string& data) const;
};
```

## Variable Declarations

### Modern C++ Variable Declarations

Use modern C++ features for variable declarations:

```cpp
// Auto type deduction
auto user_name = std::string{"john_doe"};
auto is_authenticated = true;
auto user_count = std::size_t{0};

// Structured bindings (C++17)
auto [success, error_message] = validate_user_data(user_data);
auto [iterator, inserted] = user_map.insert({user_id, user_ptr});

// Range-based for loops
for (const auto& user : user_list) {
    process_user(user);
}

for (auto&& [key, value] : user_preferences) {
    update_preference(key, value);
}

// Uniform initialization
std::vector<std::string> user_roles{"admin", "user", "guest"};
std::unordered_map<std::string, int> user_scores{
    {"alice", 95},
    {"bob", 87},
    {"charlie", 92}
};

// Optional values
std::optional<User> user = find_user_by_id(user_id);
if (user.has_value()) {
    process_user(user.value());
}

// Smart pointers
auto user_ptr = std::make_unique<User>("john", "<EMAIL>");
auto shared_service = std::make_shared<UserService>(db_connection);

// Const correctness
const auto& config = get_application_config();
const std::string& user_email = user.get_email();
```

### Type Safety and Strong Types

Use strong typing to prevent errors:

```cpp
// Strong type aliases
enum class UserId : std::uint64_t {};
enum class SessionId : std::uint64_t {};

// Prevent mixing different ID types
class UserService {
public:
    [[nodiscard]] std::optional<User> get_user(UserId user_id) const;
    [[nodiscard]] bool validate_session(SessionId session_id) const;

    // This would cause a compile error:
    // auto user = service.get_user(session_id); // Error: wrong type
};

// RAII wrapper classes
class FileHandle {
public:
    explicit FileHandle(const std::string& filename)
        : file_(std::fopen(filename.c_str(), "r")) {
        if (!file_) {
            throw std::runtime_error("Failed to open file: " + filename);
        }
    }

    ~FileHandle() {
        if (file_) {
            std::fclose(file_);
        }
    }

    // Non-copyable, movable
    FileHandle(const FileHandle&) = delete;
    FileHandle& operator=(const FileHandle&) = delete;
    FileHandle(FileHandle&& other) noexcept : file_(std::exchange(other.file_, nullptr)) {}
    FileHandle& operator=(FileHandle&& other) noexcept {
        if (this != &other) {
            if (file_) std::fclose(file_);
            file_ = std::exchange(other.file_, nullptr);
        }
        return *this;
    }

    [[nodiscard]] FILE* get() const noexcept { return file_; }

private:
    FILE* file_;
};
```

## Functions and Methods

### Function Signatures and Attributes

Use modern C++ function features:

```cpp
// [[nodiscard]] attribute for functions that return important values
[[nodiscard]] double calculate_discount(double price, double discount_percent) {
    if (price < 0.0 || discount_percent < 0.0 || discount_percent > 100.0) {
        throw std::invalid_argument("Invalid input parameters");
    }
    return price * (discount_percent / 100.0);
}

// [[maybe_unused]] for parameters that might not be used in all builds
void debug_log([[maybe_unused]] const std::string& message) {
#ifdef DEBUG
    std::cout << "DEBUG: " << message << std::endl;
#endif
}

// constexpr functions for compile-time evaluation
constexpr std::size_t fibonacci(std::size_t n) {
    return (n <= 1) ? n : fibonacci(n - 1) + fibonacci(n - 2);
}

// noexcept specification
[[nodiscard]] bool is_valid_email(const std::string& email) noexcept {
    try {
        // Email validation logic
        return email.find('@') != std::string::npos;
    } catch (...) {
        return false;
    }
}

// Perfect forwarding with universal references
template<typename T>
void process_data(T&& data) {
    auto processor = DataProcessor{};
    processor.handle(std::forward<T>(data));
}

// Function overloading with different const-ness
class UserRepository {
public:
    [[nodiscard]] User& get_user(const std::string& id) {
        return const_cast<User&>(static_cast<const UserRepository*>(this)->get_user(id));
    }

    [[nodiscard]] const User& get_user(const std::string& id) const {
        auto it = users_.find(id);
        if (it == users_.end()) {
            throw std::out_of_range("User not found: " + id);
        }
        return it->second;
    }

private:
    std::unordered_map<std::string, User> users_;
};
```

### Lambda Expressions

Use lambda expressions effectively:

```cpp
// Simple lambdas
auto users = get_all_users();

// Filter active users
auto active_users = users
    | std::views::filter([](const User& user) { return user.is_active(); })
    | std::ranges::to<std::vector>();

// Transform user data
auto user_names = users
    | std::views::transform([](const User& user) { return user.get_name(); })
    | std::ranges::to<std::vector>();

// Complex lambda with capture
auto create_validator = [&config = get_config()](const User& user) -> bool {
    return user.get_age() >= config.min_age &&
           user.get_email().length() <= config.max_email_length;
};

// Generic lambda (C++14)
auto generic_printer = [](const auto& value) {
    std::cout << value << std::endl;
};

// Lambda with explicit template parameters (C++20)
auto type_safe_converter = []<typename T>(const std::string& str) -> std::optional<T> {
    if constexpr (std::is_same_v<T, int>) {
        try {
            return std::stoi(str);
        } catch (...) {
            return std::nullopt;
        }
    } else if constexpr (std::is_same_v<T, double>) {
        try {
            return std::stod(str);
        } catch (...) {
            return std::nullopt;
        }
    }
    return std::nullopt;
};
```

## Classes and Objects

### Class Design Principles

Follow modern C++ class design principles:

```cpp
// Rule of Zero/Three/Five
class User {
public:
    // Constructor with member initializer list
    User(std::string name, std::string email, int age)
        : name_(std::move(name))
        , email_(std::move(email))
        , age_(age)
        , created_at_(std::chrono::system_clock::now()) {

        if (age_ < 0 || age_ > 150) {
            throw std::invalid_argument("Invalid age: " + std::to_string(age_));
        }
    }

    // Default destructor (Rule of Zero)
    ~User() = default;

    // Copy operations
    User(const User&) = default;
    User& operator=(const User&) = default;

    // Move operations
    User(User&&) = default;
    User& operator=(User&&) = default;

    // Const member functions
    [[nodiscard]] const std::string& get_name() const noexcept { return name_; }
    [[nodiscard]] const std::string& get_email() const noexcept { return email_; }
    [[nodiscard]] int get_age() const noexcept { return age_; }
    [[nodiscard]] bool is_adult() const noexcept { return age_ >= 18; }

    // Non-const member functions
    void set_email(std::string new_email) {
        if (new_email.empty()) {
            throw std::invalid_argument("Email cannot be empty");
        }
        email_ = std::move(new_email);
    }

    // Comparison operators (C++20 spaceship operator)
    [[nodiscard]] auto operator<=>(const User& other) const = default;

private:
    std::string name_;
    std::string email_;
    int age_;
    std::chrono::system_clock::time_point created_at_;
};

// Abstract base class with virtual destructor
class Shape {
public:
    virtual ~Shape() = default;

    [[nodiscard]] virtual double area() const = 0;
    [[nodiscard]] virtual double perimeter() const = 0;
    [[nodiscard]] virtual std::unique_ptr<Shape> clone() const = 0;

protected:
    Shape() = default;
    Shape(const Shape&) = default;
    Shape& operator=(const Shape&) = default;
    Shape(Shape&&) = default;
    Shape& operator=(Shape&&) = default;
};

// Concrete implementation
class Rectangle : public Shape {
public:
    Rectangle(double width, double height)
        : width_(width), height_(height) {
        if (width_ <= 0.0 || height_ <= 0.0) {
            throw std::invalid_argument("Width and height must be positive");
        }
    }

    [[nodiscard]] double area() const override {
        return width_ * height_;
    }

    [[nodiscard]] double perimeter() const override {
        return 2.0 * (width_ + height_);
    }

    [[nodiscard]] std::unique_ptr<Shape> clone() const override {
        return std::make_unique<Rectangle>(width_, height_);
    }

private:
    double width_;
    double height_;
};
```

## Templates and Generics

### Template Design

Use templates for generic and reusable code:

```cpp
// Function templates
template<typename T>
[[nodiscard]] constexpr T max_value(const T& a, const T& b) {
    return (a > b) ? a : b;
}

// Variadic templates
template<typename T, typename... Args>
[[nodiscard]] constexpr T max_value(const T& first, const Args&... args) {
    if constexpr (sizeof...(args) == 0) {
        return first;
    } else {
        return max_value(first, max_value(args...));
    }
}

// Class templates with SFINAE
template<typename T>
class Container {
    static_assert(std::is_copy_constructible_v<T>, "T must be copy constructible");

public:
    explicit Container(std::size_t capacity = 10) : data_(capacity) {}

    template<typename U = T>
    std::enable_if_t<std::is_default_constructible_v<U>, void>
    resize(std::size_t new_size) {
        data_.resize(new_size);
    }

    [[nodiscard]] const T& at(std::size_t index) const {
        if (index >= data_.size()) {
            throw std::out_of_range("Index out of range");
        }
        return data_[index];
    }

private:
    std::vector<T> data_;
};

// Concepts (C++20)
template<typename T>
concept Numeric = std::is_arithmetic_v<T>;

template<Numeric T>
[[nodiscard]] constexpr T square(const T& value) {
    return value * value;
}

// Template specialization
template<>
class Container<bool> {
public:
    explicit Container(std::size_t capacity = 10) : data_(capacity) {}

    void set(std::size_t index, bool value) {
        if (index >= data_.size()) {
            data_.resize(index + 1);
        }
        data_[index] = value;
    }

private:
    std::vector<bool> data_;
};
```

### Template Metaprogramming

Use template metaprogramming for compile-time computations:

```cpp
// Type traits
template<typename T>
struct is_smart_pointer : std::false_type {};

template<typename T>
struct is_smart_pointer<std::unique_ptr<T>> : std::true_type {};

template<typename T>
struct is_smart_pointer<std::shared_ptr<T>> : std::true_type {};

template<typename T>
inline constexpr bool is_smart_pointer_v = is_smart_pointer<T>::value;

// CRTP (Curiously Recurring Template Pattern)
template<typename Derived>
class Singleton {
public:
    static Derived& instance() {
        static Derived instance_;
        return instance_;
    }

protected:
    Singleton() = default;
    ~Singleton() = default;

    Singleton(const Singleton&) = delete;
    Singleton& operator=(const Singleton&) = delete;
    Singleton(Singleton&&) = delete;
    Singleton& operator=(Singleton&&) = delete;
};

class Logger : public Singleton<Logger> {
    friend class Singleton<Logger>;

public:
    void log(const std::string& message) {
        std::cout << "[LOG] " << message << std::endl;
    }

private:
    Logger() = default;
};
```

## Memory Management

### Smart Pointers and RAII

Use smart pointers and RAII for automatic memory management:

```cpp
// Unique ownership
class ResourceManager {
public:
    explicit ResourceManager(const std::string& resource_name)
        : resource_(std::make_unique<Resource>(resource_name)) {
        logger_->info("Created resource: {}", resource_name);
    }

    // Move-only semantics
    ResourceManager(const ResourceManager&) = delete;
    ResourceManager& operator=(const ResourceManager&) = delete;
    ResourceManager(ResourceManager&&) = default;
    ResourceManager& operator=(ResourceManager&&) = default;

    [[nodiscard]] const Resource& get_resource() const {
        if (!resource_) {
            throw std::runtime_error("Resource not available");
        }
        return *resource_;
    }

private:
    std::unique_ptr<Resource> resource_;
    std::shared_ptr<spdlog::logger> logger_ = spdlog::default_logger();
};

// Shared ownership
class ServiceRegistry {
public:
    template<typename T, typename... Args>
    std::shared_ptr<T> register_service(Args&&... args) {
        auto service = std::make_shared<T>(std::forward<Args>(args)...);
        services_[typeid(T).name()] = service;
        return service;
    }

    template<typename T>
    [[nodiscard]] std::shared_ptr<T> get_service() const {
        auto it = services_.find(typeid(T).name());
        if (it != services_.end()) {
            return std::static_pointer_cast<T>(it->second);
        }
        return nullptr;
    }

private:
    std::unordered_map<std::string, std::shared_ptr<void>> services_;
};

// Custom deleters
auto create_file_handle(const std::string& filename) {
    auto deleter = [](FILE* file) {
        if (file) {
            std::fclose(file);
        }
    };

    FILE* file = std::fopen(filename.c_str(), "r");
    if (!file) {
        throw std::runtime_error("Failed to open file: " + filename);
    }

    return std::unique_ptr<FILE, decltype(deleter)>(file, deleter);
}

// Memory pool for frequent allocations
template<typename T, std::size_t PoolSize = 1024>
class MemoryPool {
public:
    MemoryPool() {
        for (std::size_t i = 0; i < PoolSize - 1; ++i) {
            reinterpret_cast<Node*>(&pool_[i])->next =
                reinterpret_cast<Node*>(&pool_[i + 1]);
        }
        reinterpret_cast<Node*>(&pool_[PoolSize - 1])->next = nullptr;
        free_list_ = reinterpret_cast<Node*>(&pool_[0]);
    }

    [[nodiscard]] T* allocate() {
        if (!free_list_) {
            throw std::bad_alloc{};
        }

        Node* node = free_list_;
        free_list_ = free_list_->next;
        return reinterpret_cast<T*>(node);
    }

    void deallocate(T* ptr) {
        if (ptr) {
            Node* node = reinterpret_cast<Node*>(ptr);
            node->next = free_list_;
            free_list_ = node;
        }
    }

private:
    struct Node {
        Node* next;
    };

    alignas(T) std::byte pool_[PoolSize * sizeof(T)];
    Node* free_list_;
};
```

## Error Handling

### Exception Safety and Error Handling

Use modern C++ error handling techniques:

```cpp
// Custom exception hierarchy
class ApplicationError : public std::exception {
public:
    explicit ApplicationError(std::string message)
        : message_(std::move(message)) {}

    [[nodiscard]] const char* what() const noexcept override {
        return message_.c_str();
    }

private:
    std::string message_;
};

class ValidationError : public ApplicationError {
public:
    ValidationError(std::string field, std::string message)
        : ApplicationError("Validation error in field '" + field + "': " + message)
        , field_(std::move(field)) {}

    [[nodiscard]] const std::string& get_field() const noexcept { return field_; }

private:
    std::string field_;
};

class NotFoundError : public ApplicationError {
public:
    NotFoundError(std::string resource, std::string id)
        : ApplicationError(resource + " with ID '" + id + "' not found")
        , resource_(std::move(resource))
        , id_(std::move(id)) {}

private:
    std::string resource_;
    std::string id_;
};

// Expected/Result pattern (alternative to exceptions)
template<typename T, typename E = std::string>
class Expected {
public:
    // Success constructor
    Expected(T value) : value_(std::move(value)), has_value_(true) {}

    // Error constructor
    Expected(E error) : error_(std::move(error)), has_value_(false) {}

    [[nodiscard]] bool has_value() const noexcept { return has_value_; }
    [[nodiscard]] explicit operator bool() const noexcept { return has_value_; }

    [[nodiscard]] const T& value() const& {
        if (!has_value_) {
            throw std::runtime_error("Expected has no value");
        }
        return value_;
    }

    [[nodiscard]] T&& value() && {
        if (!has_value_) {
            throw std::runtime_error("Expected has no value");
        }
        return std::move(value_);
    }

    [[nodiscard]] const E& error() const& {
        if (has_value_) {
            throw std::runtime_error("Expected has no error");
        }
        return error_;
    }

    template<typename F>
    auto and_then(F&& func) -> Expected<std::invoke_result_t<F, T>, E> {
        if (has_value_) {
            return func(value_);
        } else {
            return Expected<std::invoke_result_t<F, T>, E>(error_);
        }
    }

private:
    union {
        T value_;
        E error_;
    };
    bool has_value_;
};

// Usage examples
[[nodiscard]] Expected<User, std::string> create_user(const std::string& name, const std::string& email) {
    if (name.empty()) {
        return Expected<User, std::string>("Name cannot be empty");
    }

    if (email.find('@') == std::string::npos) {
        return Expected<User, std::string>("Invalid email format");
    }

    return Expected<User, std::string>(User{name, email, 25});
}

// Exception safety guarantees
class SafeContainer {
public:
    void add_item(const std::string& item) {
        // Strong exception safety guarantee
        auto new_items = items_;  // Copy current state
        new_items.push_back(item);  // Modify copy

        // Only commit if no exception was thrown
        items_ = std::move(new_items);
        ++count_;
    }

    void process_items() {
        // Basic exception safety guarantee
        for (auto& item : items_) {
            try {
                process_item(item);
            } catch (const std::exception& e) {
                // Log error but continue processing other items
                logger_->error("Failed to process item '{}': {}", item, e.what());
            }
        }
    }

private:
    std::vector<std::string> items_;
    std::size_t count_ = 0;
    std::shared_ptr<spdlog::logger> logger_ = spdlog::default_logger();

    void process_item(const std::string& item) {
        // Implementation that might throw
    }
};
```

## Concurrency and Threading

### Thread Safety and Synchronization

Use modern C++ concurrency features:

```cpp
// Thread-safe singleton
class ThreadSafeCounter {
public:
    void increment() {
        std::lock_guard<std::mutex> lock(mutex_);
        ++count_;
    }

    void decrement() {
        std::lock_guard<std::mutex> lock(mutex_);
        --count_;
    }

    [[nodiscard]] std::size_t get_count() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return count_;
    }

private:
    mutable std::mutex mutex_;
    std::size_t count_ = 0;
};

// Producer-consumer pattern with condition variables
template<typename T>
class ThreadSafeQueue {
public:
    void push(T item) {
        std::lock_guard<std::mutex> lock(mutex_);
        queue_.push(std::move(item));
        condition_.notify_one();
    }

    [[nodiscard]] std::optional<T> try_pop() {
        std::lock_guard<std::mutex> lock(mutex_);
        if (queue_.empty()) {
            return std::nullopt;
        }

        T result = std::move(queue_.front());
        queue_.pop();
        return result;
    }

    [[nodiscard]] T wait_and_pop() {
        std::unique_lock<std::mutex> lock(mutex_);
        condition_.wait(lock, [this] { return !queue_.empty(); });

        T result = std::move(queue_.front());
        queue_.pop();
        return result;
    }

    [[nodiscard]] bool empty() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return queue_.empty();
    }

private:
    mutable std::mutex mutex_;
    std::queue<T> queue_;
    std::condition_variable condition_;
};

// Async task processing
class TaskProcessor {
public:
    explicit TaskProcessor(std::size_t num_threads = std::thread::hardware_concurrency()) {
        for (std::size_t i = 0; i < num_threads; ++i) {
            workers_.emplace_back([this] { worker_thread(); });
        }
    }

    ~TaskProcessor() {
        {
            std::lock_guard<std::mutex> lock(mutex_);
            stop_ = true;
        }
        condition_.notify_all();

        for (auto& worker : workers_) {
            if (worker.joinable()) {
                worker.join();
            }
        }
    }

    template<typename F, typename... Args>
    auto submit(F&& func, Args&&... args) -> std::future<std::invoke_result_t<F, Args...>> {
        using ReturnType = std::invoke_result_t<F, Args...>;

        auto task = std::make_shared<std::packaged_task<ReturnType()>>(
            std::bind(std::forward<F>(func), std::forward<Args>(args)...)
        );

        auto future = task->get_future();

        {
            std::lock_guard<std::mutex> lock(mutex_);
            if (stop_) {
                throw std::runtime_error("TaskProcessor is stopped");
            }
            tasks_.emplace([task] { (*task)(); });
        }

        condition_.notify_one();
        return future;
    }

private:
    void worker_thread() {
        while (true) {
            std::function<void()> task;

            {
                std::unique_lock<std::mutex> lock(mutex_);
                condition_.wait(lock, [this] { return stop_ || !tasks_.empty(); });

                if (stop_ && tasks_.empty()) {
                    return;
                }

                task = std::move(tasks_.front());
                tasks_.pop();
            }

            task();
        }
    }

    std::vector<std::thread> workers_;
    std::queue<std::function<void()>> tasks_;
    std::mutex mutex_;
    std::condition_variable condition_;
    bool stop_ = false;
};

// Atomic operations for lock-free programming
class LockFreeCounter {
public:
    void increment() {
        count_.fetch_add(1, std::memory_order_relaxed);
    }

    void decrement() {
        count_.fetch_sub(1, std::memory_order_relaxed);
    }

    [[nodiscard]] std::size_t get_count() const {
        return count_.load(std::memory_order_relaxed);
    }

private:
    std::atomic<std::size_t> count_{0};
};
```

## Comments and Documentation

### Documentation Standards

Use clear and concise documentation following the "no inline documentation" principle:

```cpp
// ------------ HELPER FUNCTIONS

// Calculate compound interest using the standard formula
[[nodiscard]] double calculate_compound_interest(
    double principal,
    double rate,
    double time,
    int frequency
) {
    return principal * std::pow(1.0 + rate / frequency, frequency * time);
}

// Validate email address using basic format checking
[[nodiscard]] bool is_valid_email(const std::string& email) noexcept {
    const std::regex email_pattern(R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})");
    return std::regex_match(email, email_pattern);
}

// ------------ MAIN IMPLEMENTATION

/**
 * Service class for managing payment operations.
 *
 * Provides secure payment processing with rate limiting,
 * validation, and comprehensive error handling.
 */
class PaymentProcessor {
public:
    /**
     * Initialize payment processor with configuration.
     *
     * @param config Payment processor configuration
     * @throws std::invalid_argument if configuration is invalid
     */
    explicit PaymentProcessor(const PaymentConfig& config);

    /**
     * Process a payment request.
     *
     * @param payment_request Payment details and amount
     * @return Future containing payment result
     * @throws ValidationError if payment data is invalid
     * @throws RateLimitError if rate limit exceeded
     */
    [[nodiscard]] std::future<PaymentResult> process_payment(
        const PaymentRequest& payment_request
    );

private:
    PaymentConfig config_;
    std::shared_ptr<spdlog::logger> logger_;
    std::unique_ptr<RateLimiter> rate_limiter_;

    // Validate payment request data
    void validate_payment_request_(const PaymentRequest& request) const;

    // Execute the actual payment processing
    [[nodiscard]] PaymentResult execute_payment_(const PaymentRequest& request) const;
};

/**
 * RAII wrapper for database transactions.
 *
 * Automatically commits on successful completion or
 * rolls back on exception or explicit rollback.
 */
class DatabaseTransaction {
public:
    /**
     * Begin a new database transaction.
     *
     * @param connection Database connection to use
     * @throws DatabaseError if transaction cannot be started
     */
    explicit DatabaseTransaction(std::shared_ptr<DatabaseConnection> connection);

    /**
     * Destructor automatically rolls back if not committed.
     */
    ~DatabaseTransaction();

    // Non-copyable, movable
    DatabaseTransaction(const DatabaseTransaction&) = delete;
    DatabaseTransaction& operator=(const DatabaseTransaction&) = delete;
    DatabaseTransaction(DatabaseTransaction&&) = default;
    DatabaseTransaction& operator=(DatabaseTransaction&&) = default;

    /**
     * Commit the transaction.
     *
     * @throws DatabaseError if commit fails
     */
    void commit();

    /**
     * Rollback the transaction.
     *
     * @throws DatabaseError if rollback fails
     */
    void rollback();

private:
    std::shared_ptr<DatabaseConnection> connection_;
    bool committed_ = false;
    bool rolled_back_ = false;
};
```

### Comment Guidelines

Use minimal but meaningful comments:

```cpp
// ------------ HELPER FUNCTIONS

[[nodiscard]] std::string generate_session_token() {
    // Use cryptographically secure random number generator
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 15);

    std::string token;
    token.reserve(32);

    // Generate 32-character hexadecimal token
    for (int i = 0; i < 32; ++i) {
        token += "0123456789abcdef"[dis(gen)];
    }

    return token;
}

[[nodiscard]] bool validate_user_permissions(
    const User& user,
    const std::vector<Permission>& required_permissions
) {
    const auto& user_permissions = user.get_permissions();

    // Check if user has all required permissions
    return std::all_of(
        required_permissions.begin(),
        required_permissions.end(),
        [&user_permissions](const Permission& perm) {
            return std::find(user_permissions.begin(), user_permissions.end(), perm)
                   != user_permissions.end();
        }
    );
}

// ------------ MAIN IMPLEMENTATION

class UserService {
public:
    explicit UserService(std::shared_ptr<UserRepository> repository)
        : repository_(std::move(repository))
        , logger_(spdlog::get("user_service")) {
    }

    [[nodiscard]] std::future<User> create_user(const UserData& user_data) {
        return std::async(std::launch::async, [this, user_data] {
            // Validate required fields
            validate_user_data_(user_data);

            // Check for existing user with same email
            if (auto existing_user = repository_->find_by_email(user_data.email)) {
                throw ConflictError("User with this email already exists");
            }

            // Create and save user
            auto user = User::create(user_data);
            auto saved_user = repository_->save(user);

            logger_->info("Created user: {}", saved_user.get_id());
            return saved_user;
        });
    }

private:
    std::shared_ptr<UserRepository> repository_;
    std::shared_ptr<spdlog::logger> logger_;

    void validate_user_data_(const UserData& data) const {
        if (data.name.empty()) {
            throw ValidationError("name", "Name cannot be empty");
        }

        if (!is_valid_email(data.email)) {
            throw ValidationError("email", "Invalid email format");
        }

        if (data.age < 0 || data.age > 150) {
            throw ValidationError("age", "Age must be between 0 and 150");
        }
    }
};
```

## Code Quality and Linting

### C++ Code Quality Tools

Use modern C++ code quality tools for consistent code standards:

```cpp
// Key tools for C++ code quality:
// - clang-format: Code formatting
// - clang-tidy: Static analysis and linting
// - cppcheck: Additional static analysis
// - AddressSanitizer: Memory error detection
// - Valgrind: Memory debugging and profiling
// - Google Test: Unit testing framework
```

### Code Quality Practices

```cpp
// Good: Use const correctness
class UserManager {
public:
    [[nodiscard]] const User& get_user(const std::string& id) const {
        auto it = users_.find(id);
        if (it == users_.end()) {
            throw NotFoundError("User", id);
        }
        return it->second;
    }

    void add_user(User user) {
        const auto id = user.get_id();
        users_.emplace(id, std::move(user));
    }

private:
    std::unordered_map<std::string, User> users_;
};

// Good: Use RAII and smart pointers
class DatabaseManager {
public:
    explicit DatabaseManager(const std::string& connection_string)
        : connection_(std::make_unique<DatabaseConnection>(connection_string)) {
        if (!connection_->is_valid()) {
            throw std::runtime_error("Invalid database connection");
        }
    }

    [[nodiscard]] QueryResult execute_query(const std::string& query) const {
        return connection_->execute(query);
    }

private:
    std::unique_ptr<DatabaseConnection> connection_;
};

// Good: Use modern C++ features
[[nodiscard]] std::vector<User> filter_active_users(const std::vector<User>& users) {
    std::vector<User> active_users;

    std::copy_if(
        users.begin(),
        users.end(),
        std::back_inserter(active_users),
        [](const User& user) { return user.is_active(); }
    );

    return active_users;
}
```

## Testing Standards

### Test File Structure

```cpp
/**
 * @file TEST_USER_SERVICE.CPP
 *
 * @version 1.0.0
 * <AUTHOR>
 * @contributors
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * Unit tests for UserService class.
 * Tests user creation, validation, and data management functionality.
 */

// ------------ SYSTEM INCLUDES
#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <memory>
#include <string>

// ------------ PROJECT INCLUDES
#include "user_service.hpp"
#include "user_repository.hpp"
#include "validation_error.hpp"
#include "conflict_error.hpp"

// ------------ TEST FIXTURES
class UserServiceTest : public ::testing::Test {
protected:
    void SetUp() override {
        mock_repository_ = std::make_shared<MockUserRepository>();
        user_service_ = std::make_unique<UserService>(mock_repository_);
    }

    void TearDown() override {
        user_service_.reset();
        mock_repository_.reset();
    }

    std::shared_ptr<MockUserRepository> mock_repository_;
    std::unique_ptr<UserService> user_service_;

    const UserData valid_user_data_{
        .name = "John Doe",
        .email = "<EMAIL>",
        .age = 30
    };
};

// ------------ MOCK CLASSES
class MockUserRepository : public UserRepository {
public:
    MOCK_METHOD(std::optional<User>, find_by_email, (const std::string& email), (const, override));
    MOCK_METHOD(User, save, (const User& user), (override));
    MOCK_METHOD(std::optional<User>, find_by_id, (const std::string& id), (const, override));
    MOCK_METHOD(void, remove, (const std::string& id), (override));
};

// ------------ TEST CASES
TEST_F(UserServiceTest, CreateUser_ValidData_ReturnsUser) {
    // Arrange
    const User expected_user{valid_user_data_};

    EXPECT_CALL(*mock_repository_, find_by_email(valid_user_data_.email))
        .WillOnce(::testing::Return(std::nullopt));

    EXPECT_CALL(*mock_repository_, save(::testing::_))
        .WillOnce(::testing::Return(expected_user));

    // Act
    auto future = user_service_->create_user(valid_user_data_);
    auto result = future.get();

    // Assert
    EXPECT_EQ(result.get_name(), valid_user_data_.name);
    EXPECT_EQ(result.get_email(), valid_user_data_.email);
    EXPECT_EQ(result.get_age(), valid_user_data_.age);
}

TEST_F(UserServiceTest, CreateUser_EmptyName_ThrowsValidationError) {
    // Arrange
    auto invalid_data = valid_user_data_;
    invalid_data.name = "";

    // Act & Assert
    auto future = user_service_->create_user(invalid_data);
    EXPECT_THROW(future.get(), ValidationError);
}

TEST_F(UserServiceTest, CreateUser_InvalidEmail_ThrowsValidationError) {
    // Arrange
    auto invalid_data = valid_user_data_;
    invalid_data.email = "invalid-email";

    // Act & Assert
    auto future = user_service_->create_user(invalid_data);
    EXPECT_THROW(future.get(), ValidationError);
}

TEST_F(UserServiceTest, CreateUser_ExistingEmail_ThrowsConflictError) {
    // Arrange
    const User existing_user{valid_user_data_};

    EXPECT_CALL(*mock_repository_, find_by_email(valid_user_data_.email))
        .WillOnce(::testing::Return(existing_user));

    // Act & Assert
    auto future = user_service_->create_user(valid_user_data_);
    EXPECT_THROW(future.get(), ConflictError);
}

// ------------ PARAMETERIZED TESTS
class UserValidationTest : public ::testing::TestWithParam<std::tuple<std::string, bool>> {};

TEST_P(UserValidationTest, EmailValidation) {
    // Arrange
    const auto& [email, expected_valid] = GetParam();

    // Act
    const bool is_valid = is_valid_email(email);

    // Assert
    EXPECT_EQ(is_valid, expected_valid);
}

INSTANTIATE_TEST_SUITE_P(
    EmailValidationCases,
    UserValidationTest,
    ::testing::Values(
        std::make_tuple("<EMAIL>", true),
        std::make_tuple("<EMAIL>", true),
        std::make_tuple("<EMAIL>", true),
        std::make_tuple("<EMAIL>", true),
        std::make_tuple("", false),
        std::make_tuple("invalid-email", false),
        std::make_tuple("@example.com", false),
        std::make_tuple("user@", false),
        std::make_tuple("user@.com", false)
    )
);
```

## Performance Guidelines

### Optimization Techniques

```cpp
// Good: Use move semantics to avoid unnecessary copies
class DataContainer {
public:
    void add_data(std::string data) {
        data_.push_back(std::move(data));  // Move instead of copy
    }

    void add_data(const std::string& data) {
        data_.push_back(data);  // Copy when necessary
    }

    [[nodiscard]] std::vector<std::string> get_data() && {
        return std::move(data_);  // Move when object is temporary
    }

    [[nodiscard]] const std::vector<std::string>& get_data() const& {
        return data_;  // Return reference for lvalue objects
    }

private:
    std::vector<std::string> data_;
};

// Good: Use reserve() for known container sizes
[[nodiscard]] std::vector<ProcessedData> process_large_dataset(
    const std::vector<RawData>& raw_data
) {
    std::vector<ProcessedData> processed_data;
    processed_data.reserve(raw_data.size());  // Avoid reallocations

    std::transform(
        raw_data.begin(),
        raw_data.end(),
        std::back_inserter(processed_data),
        [](const RawData& data) { return process_data(data); }
    );

    return processed_data;
}

// Good: Use string_view for read-only string parameters
[[nodiscard]] bool starts_with(std::string_view text, std::string_view prefix) {
    return text.size() >= prefix.size() &&
           text.substr(0, prefix.size()) == prefix;
}

// Good: Use constexpr for compile-time computations
constexpr std::size_t calculate_buffer_size(std::size_t element_count, std::size_t element_size) {
    return element_count * element_size + sizeof(std::size_t);  // Add header size
}

// Good: Use object pools for frequent allocations
template<typename T>
class ObjectPool {
public:
    template<typename... Args>
    [[nodiscard]] std::unique_ptr<T, std::function<void(T*)>> acquire(Args&&... args) {
        if (pool_.empty()) {
            return {new T(std::forward<Args>(args)...), [this](T* obj) { release(obj); }};
        }

        auto obj = std::move(pool_.back());
        pool_.pop_back();

        // Reinitialize object
        *obj = T(std::forward<Args>(args)...);

        return {obj.release(), [this](T* obj) { release(obj); }};
    }

private:
    void release(T* obj) {
        if (pool_.size() < max_pool_size_) {
            pool_.emplace_back(obj);
        } else {
            delete obj;
        }
    }

    std::vector<std::unique_ptr<T>> pool_;
    static constexpr std::size_t max_pool_size_ = 100;
};
```

## Security Considerations

### Secure Coding Practices

```cpp
// Input validation and sanitization
class SecureInputValidator {
public:
    [[nodiscard]] static bool validate_user_input(const std::string& input) {
        // Check for null bytes
        if (input.find('\0') != std::string::npos) {
            return false;
        }

        // Check length limits
        if (input.length() > MAX_INPUT_LENGTH) {
            return false;
        }

        // Check for dangerous patterns
        const std::vector<std::string> dangerous_patterns = {
            "../", "..\\", "<script", "javascript:", "data:"
        };

        for (const auto& pattern : dangerous_patterns) {
            if (input.find(pattern) != std::string::npos) {
                return false;
            }
        }

        return true;
    }

    [[nodiscard]] static std::string sanitize_filename(const std::string& filename) {
        std::string sanitized = filename;

        // Remove dangerous characters
        const std::string dangerous_chars = "<>:\"/\\|?*";
        for (char c : dangerous_chars) {
            sanitized.erase(std::remove(sanitized.begin(), sanitized.end(), c), sanitized.end());
        }

        // Limit length
        if (sanitized.length() > MAX_FILENAME_LENGTH) {
            sanitized = sanitized.substr(0, MAX_FILENAME_LENGTH);
        }

        return sanitized;
    }

private:
    static constexpr std::size_t MAX_INPUT_LENGTH = 1024;
    static constexpr std::size_t MAX_FILENAME_LENGTH = 255;
};

// Secure memory handling
class SecureString {
public:
    explicit SecureString(std::size_t size) : data_(size) {
        // Initialize with random data to prevent information leakage
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<unsigned char> dis(0, 255);

        for (auto& byte : data_) {
            byte = dis(gen);
        }
    }

    ~SecureString() {
        // Securely clear memory
        std::fill(data_.begin(), data_.end(), 0);
    }

    // Non-copyable to prevent accidental data exposure
    SecureString(const SecureString&) = delete;
    SecureString& operator=(const SecureString&) = delete;

    // Movable for efficiency
    SecureString(SecureString&&) = default;
    SecureString& operator=(SecureString&&) = default;

    [[nodiscard]] std::byte* data() noexcept { return data_.data(); }
    [[nodiscard]] const std::byte* data() const noexcept { return data_.data(); }
    [[nodiscard]] std::size_t size() const noexcept { return data_.size(); }

private:
    std::vector<std::byte> data_;
};

// Secure random number generation
class SecureRandom {
public:
    [[nodiscard]] static std::string generate_token(std::size_t length = 32) {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, 61);  // 0-9, A-Z, a-z

        const std::string chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

        std::string token;
        token.reserve(length);

        for (std::size_t i = 0; i < length; ++i) {
            token += chars[dis(gen)];
        }

        return token;
    }

    [[nodiscard]] static std::vector<std::byte> generate_bytes(std::size_t count) {
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<unsigned char> dis(0, 255);

        std::vector<std::byte> bytes;
        bytes.reserve(count);

        for (std::size_t i = 0; i < count; ++i) {
            bytes.emplace_back(static_cast<std::byte>(dis(gen)));
        }

        return bytes;
    }
};

// Buffer overflow prevention
template<std::size_t N>
class SafeBuffer {
public:
    SafeBuffer() = default;

    [[nodiscard]] bool write(const void* data, std::size_t size, std::size_t offset = 0) {
        if (offset + size > N) {
            return false;  // Prevent buffer overflow
        }

        std::memcpy(buffer_.data() + offset, data, size);
        used_size_ = std::max(used_size_, offset + size);
        return true;
    }

    [[nodiscard]] bool read(void* data, std::size_t size, std::size_t offset = 0) const {
        if (offset + size > used_size_) {
            return false;  // Prevent reading uninitialized data
        }

        std::memcpy(data, buffer_.data() + offset, size);
        return true;
    }

    [[nodiscard]] std::size_t size() const noexcept { return used_size_; }
    [[nodiscard]] std::size_t capacity() const noexcept { return N; }

private:
    std::array<std::byte, N> buffer_{};
    std::size_t used_size_ = 0;
};
```

## References

- [C++ Core Guidelines](https://isocpp.github.io/CppCoreGuidelines/CppCoreGuidelines)
- [Modern C++ Features](https://github.com/AnthonyCalandra/modern-cpp-features)
- [Google C++ Style Guide](https://google.github.io/styleguide/cppguide.html)
- [Effective Modern C++](https://www.oreilly.com/library/view/effective-modern-c/9781491908419/)
- [C++ Reference](https://en.cppreference.com/)
- [Clang-Tidy Checks](https://clang.llvm.org/extra/clang-tidy/checks/list.html)

> C++ code standards are enforced through clang-format, clang-tidy, and other static analysis tools. All developers must follow these standards to maintain code quality, safety, and consistency across the Dynamic Innovative Studio organization.
