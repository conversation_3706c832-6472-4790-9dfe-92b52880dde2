# Python Code Standards

This document outlines the Python coding standards and conventions that should be followed across all projects within the Dynamic Innovative Studio organization. These standards are designed to ensure consistency, readability, quality, and maintainability of the Python codebase while following modern Python best practices.

## Table of Contents

- [Python Code Standards](#python-code-standards)
  - [Table of Contents](#table-of-contents)
  - [Overview](#overview)
    - [Core Principles](#core-principles)
  - [File Structure and Headers](#file-structure-and-headers)
    - [File Header Format](#file-header-format)
    - [Header Guidelines](#header-guidelines)
    - [File Organization Structure](#file-organization-structure)
  - [Code Organization](#code-organization)
    - [Section Comments](#section-comments)
    - [Import Organization](#import-organization)
    - [Constants Declaration](#constants-declaration)
  - [Naming Conventions](#naming-conventions)
    - [Variables and Functions](#variables-and-functions)
    - [Constants](#constants)
    - [Classes](#classes)
    - [Private Members](#private-members)
  - [Variable Declarations](#variable-declarations)
    - [Type Hints](#type-hints)
    - [Default Values and None Handling](#default-values-and-none-handling)
  - [Functions and Methods](#functions-and-methods)
    - [Function Definitions](#function-definitions)
    - [Method Organization](#method-organization)
    - [Decorators and Properties](#decorators-and-properties)
  - [Classes and Objects](#classes-and-objects)
    - [Class Structure](#class-structure)
  - [Modules and Imports](#modules-and-imports)
    - [Import Organization](#import-organization-1)
    - [Module Structure](#module-structure)
  - [Type Hints](#type-hints-1)
    - [Advanced Type Hints](#advanced-type-hints)
  - [Error Handling](#error-handling)
    - [Custom Exception Classes](#custom-exception-classes)
  - [Modules and Imports](#modules-and-imports-1)
    - [Import Organization](#import-organization-2)
    - [Module Structure](#module-structure-1)
  - [Type Hints](#type-hints-2)
    - [Advanced Type Hints](#advanced-type-hints-1)
  - [Error Handling](#error-handling-1)
    - [Custom Exception Classes](#custom-exception-classes-1)
  - [Asynchronous Code](#asynchronous-code)
    - [Async/Await Patterns](#asyncawait-patterns)
  - [Comments and Documentation](#comments-and-documentation)
    - [Docstring Standards](#docstring-standards)
    - [Comment Guidelines](#comment-guidelines)
  - [Code Quality and Linting](#code-quality-and-linting)
    - [Python Linting Tools](#python-linting-tools)
    - [Code Quality Practices](#code-quality-practices)
  - [Testing Standards](#testing-standards)
    - [Test File Structure](#test-file-structure)
  - [Performance Guidelines](#performance-guidelines)
    - [Memory Management](#memory-management)
    - [Optimization Techniques](#optimization-techniques)
  - [Security Considerations](#security-considerations)
    - [Input Validation and Sanitization](#input-validation-and-sanitization)
    - [Secure Coding Practices](#secure-coding-practices)
  - [References](#references)

## Overview

Python is a high-level, interpreted programming language known for its simplicity and readability. These standards ensure that Python code written within the DIS organization maintains high quality, consistency, and follows modern Python practices including PEP 8 and beyond.

### Core Principles

- **Readability**: Code should be easy to read and understand
- **Consistency**: Maintain consistent coding style across all Python files
- **Pythonic**: Follow Python idioms and best practices
- **Type Safety**: Use type hints for better code reliability
- **Performance**: Follow performance best practices
- **Security**: Implement secure coding practices
- **Modern Standards**: Use modern Python features (3.8+)

## File Structure and Headers

### File Header Format

All Python files must include a standardized header comment block at the top of the file:

```python
"""
- file: FILENAME.PY

- version: 1.0.0
- author: BleckWolf25
- contributors:

- copyright: Dynamic Innovative Studio

- description:
Brief description of the file's purpose and functionality.
Additional details about the module or component.
"""
```

### Header Guidelines

- **file**: Use UPPERCASE filename with .PY extension
- **version**: Follow semantic versioning (MAJOR.MINOR.PATCH)
- **author**: Primary author of the file
- **contributors**: List additional contributors (one per line)
- **copyright**: Always "Dynamic Innovative Studio"
- **description**: Clear, concise description of file purpose

### File Organization Structure

```python
"""
- file: USER_SERVICE.PY

- version: 1.0.0
- author: BleckWolf25
- contributors:

- copyright: Dynamic Innovative Studio

- description:
User service for managing user data and operations.
Provides CRUD operations and user validation functionality.
"""

# ------------ IMPORTS
from __future__ import annotations

import logging
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, EmailStr, Field
import httpx
from sqlalchemy.orm import Session

from .exceptions import ValidationError, NotFoundError
from .models import User, UserRole
from .validators import validate_email, validate_password

# ------------ CONSTANTS
DEFAULT_PAGE_SIZE = 20
MAX_PAGE_SIZE = 100
CACHE_TTL_SECONDS = 300

# ------------ TYPE DEFINITIONS
UserDict = Dict[str, Any]
UserList = List[User]

# ------------ HELPER FUNCTIONS
def generate_user_id() -> str:
    """Generate a unique user ID."""
    return str(uuid.uuid4())

def format_user_name(first_name: str, last_name: str) -> str:
    """Format user's full name."""
    return f"{first_name.strip()} {last_name.strip()}".strip()

# ------------ MAIN IMPLEMENTATION
class UserService:
    """Service class for managing user operations."""

    def __init__(self, db_session: Session, api_client: httpx.AsyncClient) -> None:
        """Initialize the user service."""
        self.db_session = db_session
        self.api_client = api_client
        self.logger = logging.getLogger(__name__)
        self._cache: Dict[str, User] = {}

    async def create_user(self, user_data: UserDict) -> User:
        """Create a new user."""
        # Implementation
        pass

# ------------ EXPORTS
__all__ = [
    "UserService",
    "generate_user_id",
    "format_user_name",
    "UserDict",
    "UserList",
]
```

## Code Organization

### Section Comments

Use standardized section comments to organize code within files:

```python
# ------------ IMPORTS
# ------------ CONSTANTS
# ------------ TYPE DEFINITIONS
# ------------ HELPER FUNCTIONS
# ------------ MAIN IMPLEMENTATION
# ------------ EXPORTS
```

### Import Organization

Organize imports in the following order:

1. **Future imports**
2. **Standard library imports**
3. **Third-party library imports**
4. **Local application imports**

```python
# ------------ IMPORTS
# Future imports
from __future__ import annotations

# Standard library
import asyncio
import json
import logging
import os
import sys
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from uuid import uuid4

# Third-party libraries
import httpx
import pydantic
from fastapi import FastAPI, HTTPException
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Local application imports
from .config import settings
from .database import Base, get_db_session
from .exceptions import ValidationError
from .models import User, UserRole
from .schemas import UserCreate, UserUpdate
from .utils import hash_password, verify_password
```

### Constants Declaration

Define constants at the module level after imports:

```python
# ------------ CONSTANTS
API_VERSION = "v1"
MAX_FILE_SIZE_MB = 10
SUPPORTED_IMAGE_FORMATS = ["jpg", "jpeg", "png", "gif", "webp"]
DEFAULT_TIMEOUT_SECONDS = 30

# Error messages
ERROR_MESSAGES = {
    "INVALID_EMAIL": "Invalid email address format",
    "PASSWORD_TOO_SHORT": "Password must be at least 8 characters",
    "USER_NOT_FOUND": "User not found",
    "UNAUTHORIZED": "Unauthorized access",
}

# Configuration
DATABASE_CONFIG = {
    "pool_size": 10,
    "max_overflow": 20,
    "pool_timeout": 30,
    "pool_recycle": 3600,
}
```

## Naming Conventions

### Variables and Functions

Use **snake_case** for variables and functions:

```python
# Variables
user_name = "john_doe"
is_authenticated = True
user_preferences = {}
total_count = 0

# Functions
def calculate_total_price(items: List[Dict[str, Any]]) -> float:
    """Calculate total price of items."""
    return sum(item.get("price", 0.0) for item in items)

def validate_user_input(user_data: Dict[str, Any]) -> bool:
    """Validate user input data."""
    # Implementation
    return True

async def fetch_user_data(user_id: str) -> Optional[User]:
    """Fetch user data from database."""
    # Implementation
    return None
```

### Constants

Use **SCREAMING_SNAKE_CASE** for constants:

```python
MAX_RETRY_ATTEMPTS = 3
API_BASE_URL = "https://api.example.com"
DEFAULT_PAGE_SIZE = 20
CACHE_EXPIRY_SECONDS = 3600
```

### Classes

Use **PascalCase** for classes:

```python
class UserManager:
    """Manages user operations and data."""

    def __init__(self, database_url: str) -> None:
        self.database_url = database_url
        self.logger = logging.getLogger(__name__)

class ApiClient:
    """HTTP API client for external services."""

    def __init__(self, base_url: str, timeout: int = 30) -> None:
        self.base_url = base_url
        self.timeout = timeout

class DatabaseConnection:
    """Database connection manager."""
    pass
```

### Private Members

Use single underscore prefix for protected members and double underscore for private members:

```python
class DataProcessor:
    """Process and validate data."""

    def __init__(self) -> None:
        self._cache: Dict[str, Any] = {}  # Protected
        self.__secret_key = "secret"      # Private
        self.public_data: List[str] = []  # Public

    def _validate_data(self, data: Any) -> bool:
        """Protected method for data validation."""
        return isinstance(data, dict)

    def __encrypt_data(self, data: str) -> str:
        """Private method for data encryption."""
        # Implementation
        return data

    def process_data(self, data: Any) -> Any:
        """Public method to process data."""
        if not self._validate_data(data):
            raise ValidationError("Invalid data format")
        return self.__encrypt_data(str(data))
```

## Variable Declarations

### Type Hints

Always use type hints for better code clarity and IDE support:

```python
# Basic type hints
name: str = "John Doe"
age: int = 30
is_active: bool = True
score: float = 95.5

# Collection type hints
user_ids: List[str] = []
user_data: Dict[str, Any] = {}
coordinates: Tuple[float, float] = (0.0, 0.0)
unique_tags: Set[str] = set()

# Optional and Union types
user: Optional[User] = None
result: Union[str, int] = "success"
data: Optional[Dict[str, Any]] = None

# Function type hints
def process_users(
    users: List[User],
    filters: Optional[Dict[str, Any]] = None,
    limit: int = 100
) -> List[User]:
    """Process a list of users with optional filters."""
    # Implementation
    return users
```

### Default Values and None Handling

Use appropriate default values and handle None cases:

```python
# Default values
def create_user(
    name: str,
    email: str,
    age: int = 18,
    is_active: bool = True,
    roles: Optional[List[str]] = None
) -> User:
    """Create a new user with default values."""
    if roles is None:
        roles = ["user"]

    return User(
        name=name,
        email=email,
        age=age,
        is_active=is_active,
        roles=roles
    )

# None checking
def get_user_display_name(user: Optional[User]) -> str:
    """Get user display name with fallback."""
    if user is None:
        return "Anonymous"

    if user.name:
        return user.name

    return user.email or "Unknown User"
```

## Functions and Methods

### Function Definitions

Use clear function signatures with type hints and docstrings:

```python
def calculate_discount(
    price: float,
    discount_percent: float,
    max_discount: Optional[float] = None
) -> float:
    """Calculate discounted price with optional maximum discount.

    Args:
        price: Original price of the item
        discount_percent: Discount percentage (0-100)
        max_discount: Maximum discount amount (optional)

    Returns:
        Discounted price

    Raises:
        ValueError: If price is negative or discount_percent is invalid
    """
    if price < 0:
        raise ValueError("Price cannot be negative")

    if not 0 <= discount_percent <= 100:
        raise ValueError("Discount percent must be between 0 and 100")

    discount_amount = price * (discount_percent / 100)

    if max_discount is not None and discount_amount > max_discount:
        discount_amount = max_discount

    return price - discount_amount

async def fetch_user_profile(
    user_id: str,
    include_preferences: bool = True,
    timeout: float = 30.0
) -> Dict[str, Any]:
    """Fetch user profile data from external API.

    Args:
        user_id: Unique identifier for the user
        include_preferences: Whether to include user preferences
        timeout: Request timeout in seconds

    Returns:
        User profile data dictionary

    Raises:
        httpx.TimeoutException: If request times out
        httpx.HTTPStatusError: If API returns error status
    """
    async with httpx.AsyncClient(timeout=timeout) as client:
        response = await client.get(
            f"/api/users/{user_id}",
            params={"include_preferences": include_preferences}
        )
        response.raise_for_status()
        return response.json()
```

### Method Organization

Organize class methods in a consistent order:

```python
class UserRepository:
    """Repository for user data operations."""

    # Class variables
    DEFAULT_LIMIT = 100

    def __init__(self, db_session: Session) -> None:
        """Initialize the repository."""
        self.db_session = db_session
        self.logger = logging.getLogger(__name__)

    # Public methods
    def create(self, user_data: Dict[str, Any]) -> User:
        """Create a new user."""
        validated_data = self._validate_user_data(user_data)
        user = User(**validated_data)
        self.db_session.add(user)
        self.db_session.commit()
        self.logger.info(f"Created user with ID: {user.id}")
        return user

    def get_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID."""
        return self.db_session.query(User).filter(User.id == user_id).first()

    def update(self, user_id: str, updates: Dict[str, Any]) -> User:
        """Update user data."""
        user = self.get_by_id(user_id)
        if not user:
            raise NotFoundError(f"User with ID {user_id} not found")

        validated_updates = self._validate_user_data(updates, partial=True)
        for key, value in validated_updates.items():
            setattr(user, key, value)

        self.db_session.commit()
        self.logger.info(f"Updated user with ID: {user_id}")
        return user

    def delete(self, user_id: str) -> None:
        """Delete user by ID."""
        user = self.get_by_id(user_id)
        if not user:
            raise NotFoundError(f"User with ID {user_id} not found")

        self.db_session.delete(user)
        self.db_session.commit()
        self.logger.info(f"Deleted user with ID: {user_id}")

    # Protected methods
    def _validate_user_data(
        self,
        data: Dict[str, Any],
        partial: bool = False
    ) -> Dict[str, Any]:
        """Validate user data."""
        required_fields = ["name", "email"] if not partial else []

        for field in required_fields:
            if field not in data:
                raise ValidationError(f"Missing required field: {field}")

        if "email" in data and not self._is_valid_email(data["email"]):
            raise ValidationError("Invalid email format")

        return data

    def _is_valid_email(self, email: str) -> bool:
        """Validate email format."""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

    # Class methods
    @classmethod
    def from_config(cls, config: Dict[str, Any]) -> "UserRepository":
        """Create repository from configuration."""
        # Implementation
        pass

    # Static methods
    @staticmethod
    def generate_user_id() -> str:
        """Generate a unique user ID."""
        return str(uuid.uuid4())
```

### Decorators and Properties

Use decorators and properties appropriately:

```python
from functools import wraps
from typing import Callable, Any

def retry(max_attempts: int = 3, delay: float = 1.0):
    """Decorator to retry function calls on failure."""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        await asyncio.sleep(delay * (attempt + 1))

            raise last_exception
        return wrapper
    return decorator

class ApiClient:
    """HTTP API client with caching and retry logic."""

    def __init__(self, base_url: str, api_key: str) -> None:
        self._base_url = base_url
        self._api_key = api_key
        self._session: Optional[httpx.AsyncClient] = None

    @property
    def base_url(self) -> str:
        """Get the base URL."""
        return self._base_url

    @property
    def is_connected(self) -> bool:
        """Check if client is connected."""
        return self._session is not None and not self._session.is_closed

    @retry(max_attempts=3, delay=1.0)
    async def get(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """Make GET request with retry logic."""
        if not self.is_connected:
            await self.connect()

        response = await self._session.get(f"{self.base_url}{endpoint}", **kwargs)
        response.raise_for_status()
        return response.json()

    async def connect(self) -> None:
        """Establish connection."""
        self._session = httpx.AsyncClient(
            headers={"Authorization": f"Bearer {self._api_key}"}
        )

    async def disconnect(self) -> None:
        """Close connection."""
        if self._session:
            await self._session.aclose()
            self._session = None
```

## Classes and Objects

### Class Structure

Follow consistent class structure and inheritance patterns:

```python
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Protocol

# Abstract base class
class BaseService(ABC):
    """Abstract base class for all services."""

    def __init__(self, logger: Optional[logging.Logger] = None) -> None:
        self.logger = logger or logging.getLogger(self.__class__.__name__)

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize the service."""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup service resources."""
        pass

# Protocol for type checking
class Cacheable(Protocol):
    """Protocol for cacheable objects."""

    def get_cache_key(self) -> str:
        """Get cache key for the object."""
        ...

    def get_cache_ttl(self) -> int:
        """Get cache TTL in seconds."""
        ...

# Dataclass for simple data structures
@dataclass
class UserPreferences:
    """User preferences data structure."""

    theme: str = "light"
    language: str = "en"
    notifications_enabled: bool = True
    email_frequency: str = "daily"
    timezone: str = "UTC"
    custom_settings: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "theme": self.theme,
            "language": self.language,
            "notifications_enabled": self.notifications_enabled,
            "email_frequency": self.email_frequency,
            "timezone": self.timezone,
            "custom_settings": self.custom_settings,
        }

# Concrete implementation
class UserService(BaseService):
    """Service for managing user operations."""

    def __init__(
        self,
        repository: UserRepository,
        cache: Optional[Any] = None,
        logger: Optional[logging.Logger] = None
    ) -> None:
        super().__init__(logger)
        self.repository = repository
        self.cache = cache
        self._initialized = False

    async def initialize(self) -> None:
        """Initialize the user service."""
        if self._initialized:
            return

        await self.repository.initialize()
        if self.cache:
            await self.cache.initialize()

        self._initialized = True
        self.logger.info("UserService initialized")

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        if self.cache:
            await self.cache.cleanup()
        await self.repository.cleanup()

        self._initialized = False
        self.logger.info("UserService cleaned up")

    async def create_user(self, user_data: Dict[str, Any]) -> User:
        """Create a new user."""
        if not self._initialized:
            raise RuntimeError("Service not initialized")

        # Validate input
        self._validate_user_data(user_data)

        # Create user
        user = await self.repository.create(user_data)

        # Cache user if caching is enabled
        if self.cache:
            await self.cache.set(f"user:{user.id}", user, ttl=3600)

        self.logger.info(f"Created user: {user.id}")
        return user

    def _validate_user_data(self, data: Dict[str, Any]) -> None:
        """Validate user data."""
        required_fields = ["name", "email"]
        for field in required_fields:
            if field not in data or not data[field]:
                raise ValidationError(f"Missing required field: {field}")
```

## Modules and Imports

### Import Organization

Follow PEP 8 import guidelines with clear organization:

```python
# ------------ IMPORTS
# Future imports (always first)
from __future__ import annotations

# Standard library imports (alphabetical)
import asyncio
import json
import logging
import os
import sys
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from uuid import uuid4

# Related third-party imports (alphabetical)
import httpx
import pydantic
from fastapi import FastAPI, HTTPException, Depends
from sqlalchemy import create_engine, Column, String, Integer
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session

# Local application/library specific imports
from .config import settings
from .database import Base, get_db_session
from .exceptions import ValidationError, NotFoundError
from .models import User, UserRole, UserPreferences
from .schemas import UserCreate, UserUpdate, UserResponse
from .utils import hash_password, verify_password, generate_token
```

### Module Structure

Organize modules with clear **all** exports:

```python
"""
- file: USER_UTILS.PY

- version: 1.0.0
- author: BleckWolf25
- contributors:

- copyright: Dynamic Innovative Studio

- description:
Utility functions for user management operations.
Provides validation, formatting, and helper functions.
"""

# ------------ IMPORTS
from __future__ import annotations

import re
import hashlib
from typing import Optional, Dict, Any
from datetime import datetime

# ------------ CONSTANTS
EMAIL_REGEX = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
PASSWORD_MIN_LENGTH = 8
USERNAME_MIN_LENGTH = 3

# ------------ HELPER FUNCTIONS
def validate_email(email: str) -> bool:
    """Validate email address format."""
    return bool(EMAIL_REGEX.match(email)) if email else False

def validate_password(password: str) -> Dict[str, Any]:
    """Validate password strength."""
    errors = []

    if len(password) < PASSWORD_MIN_LENGTH:
        errors.append(f"Password must be at least {PASSWORD_MIN_LENGTH} characters")

    if not re.search(r'[A-Z]', password):
        errors.append("Password must contain at least one uppercase letter")

    return {
        "valid": len(errors) == 0,
        "errors": errors,
        "strength": _calculate_password_strength(password)
    }

def _calculate_password_strength(password: str) -> str:
    """Calculate password strength score."""
    score = 0

    if len(password) >= 8:
        score += 1
    if len(password) >= 12:
        score += 1

    if score <= 2:
        return "weak"
    elif score <= 4:
        return "medium"
    else:
        return "strong"

# ------------ EXPORTS
__all__ = [
    "validate_email",
    "validate_password",
]
```

## Type Hints

### Advanced Type Hints

Use comprehensive type hints for better code clarity:

```python
from typing import (
    Any, Dict, List, Optional, Union, Tuple, Set,
    Callable, Awaitable, TypeVar, Generic, Protocol,
    Literal, Final, ClassVar
)
from typing_extensions import TypedDict, NotRequired

# Type variables
T = TypeVar('T')
UserType = TypeVar('UserType', bound='User')

# Literal types
UserStatus = Literal['active', 'inactive', 'pending', 'suspended']
LogLevel = Literal['debug', 'info', 'warning', 'error', 'critical']

# TypedDict for structured dictionaries
class UserData(TypedDict):
    """Typed dictionary for user data."""
    id: str
    name: str
    email: str
    age: int
    status: UserStatus
    preferences: NotRequired[Dict[str, Any]]  # Optional field

# Protocol for structural typing
class Serializable(Protocol):
    """Protocol for serializable objects."""

    def to_dict(self) -> Dict[str, Any]:
        """Convert object to dictionary."""
        ...

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Serializable":
        """Create object from dictionary."""
        ...

# Generic classes
class Repository(Generic[T]):
    """Generic repository pattern."""

    def __init__(self, model_class: type[T]) -> None:
        self.model_class = model_class

    async def get_by_id(self, id: str) -> Optional[T]:
        """Get entity by ID."""
        # Implementation
        pass
```

## Error Handling

### Custom Exception Classes

Create a hierarchy of custom exceptions:

```python
# ------------ EXCEPTION CLASSES
class BaseApplicationError(Exception):
    """Base exception for all application errors."""

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.timestamp = datetime.now(timezone.utc)

    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary."""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details,
            "timestamp": self.timestamp.isoformat(),
        }

class ValidationError(BaseApplicationError):
    """Raised when data validation fails."""

    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        value: Optional[Any] = None
    ) -> None:
        details = {}
        if field:
            details["field"] = field
        if value is not None:
            details["value"] = str(value)

        super().__init__(message, "VALIDATION_ERROR", details)

class NotFoundError(BaseApplicationError):
    """Raised when a requested resource is not found."""

    def __init__(self, resource: str, identifier: str) -> None:
        message = f"{resource} with identifier '{identifier}' not found"
        details = {"resource": resource, "identifier": identifier}
        super().__init__(message, "NOT_FOUND", details)

# Error handling patterns
async def safe_execute(
    func: Callable[..., Awaitable[T]],
    *args,
    **kwargs
) -> Tuple[Optional[T], Optional[Exception]]:
    """Safely execute an async function and return result or error."""
    try:
        result = await func(*args, **kwargs)
        return result, None
    except Exception as e:
        return None, e
```

## Modules and Imports

### Import Organization

Follow PEP 8 import guidelines with clear organization:

```python
# ------------ IMPORTS
# Future imports (always first)
from __future__ import annotations

# Standard library imports (alphabetical)
import asyncio
import json
import logging
import os
import sys
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from uuid import uuid4

# Related third-party imports (alphabetical)
import httpx
import pydantic
from fastapi import FastAPI, HTTPException, Depends
from sqlalchemy import create_engine, Column, String, Integer
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session

# Local application/library specific imports
from .config import settings
from .database import Base, get_db_session
from .exceptions import ValidationError, NotFoundError
from .models import User, UserRole, UserPreferences
from .schemas import UserCreate, UserUpdate, UserResponse
from .utils import hash_password, verify_password, generate_token
```

### Module Structure

Organize modules with clear **all** exports:

```python
"""
- file: USER_UTILS.PY

- version: 1.0.0
- author: BleckWolf25
- contributors:

- copyright: Dynamic Innovative Studio

- description:
Utility functions for user management operations.
Provides validation, formatting, and helper functions.
"""

# ------------ IMPORTS
from __future__ import annotations

import re
import hashlib
from typing import Optional, Dict, Any
from datetime import datetime

# ------------ CONSTANTS
EMAIL_REGEX = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
PASSWORD_MIN_LENGTH = 8
USERNAME_MIN_LENGTH = 3

# ------------ HELPER FUNCTIONS
def validate_email(email: str) -> bool:
    """Validate email address format."""
    return bool(EMAIL_REGEX.match(email)) if email else False

def validate_password(password: str) -> Dict[str, Any]:
    """Validate password strength."""
    errors = []

    if len(password) < PASSWORD_MIN_LENGTH:
        errors.append(f"Password must be at least {PASSWORD_MIN_LENGTH} characters")

    if not re.search(r'[A-Z]', password):
        errors.append("Password must contain at least one uppercase letter")

    if not re.search(r'[a-z]', password):
        errors.append("Password must contain at least one lowercase letter")

    if not re.search(r'\d', password):
        errors.append("Password must contain at least one digit")

    return {
        "valid": len(errors) == 0,
        "errors": errors,
        "strength": _calculate_password_strength(password)
    }

def hash_password(password: str) -> str:
    """Hash password using secure algorithm."""
    import bcrypt
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def verify_password(password: str, hashed: str) -> bool:
    """Verify password against hash."""
    import bcrypt
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def format_username(username: str) -> str:
    """Format username to standard format."""
    return username.strip().lower()

def generate_user_slug(name: str) -> str:
    """Generate URL-friendly slug from user name."""
    import unicodedata

    # Normalize unicode characters
    slug = unicodedata.normalize('NFKD', name)
    slug = slug.encode('ascii', 'ignore').decode('ascii')

    # Convert to lowercase and replace spaces/special chars
    slug = re.sub(r'[^\w\s-]', '', slug).strip().lower()
    slug = re.sub(r'[-\s]+', '-', slug)

    return slug

def _calculate_password_strength(password: str) -> str:
    """Calculate password strength score."""
    score = 0

    if len(password) >= 8:
        score += 1
    if len(password) >= 12:
        score += 1
    if re.search(r'[A-Z]', password):
        score += 1
    if re.search(r'[a-z]', password):
        score += 1
    if re.search(r'\d', password):
        score += 1
    if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        score += 1

    if score <= 2:
        return "weak"
    elif score <= 4:
        return "medium"
    else:
        return "strong"

# ------------ EXPORTS
__all__ = [
    "validate_email",
    "validate_password",
    "hash_password",
    "verify_password",
    "format_username",
    "generate_user_slug",
]
```

## Type Hints

### Advanced Type Hints

Use comprehensive type hints for better code clarity:

```python
from typing import (
    Any, Dict, List, Optional, Union, Tuple, Set,
    Callable, Awaitable, TypeVar, Generic, Protocol,
    Literal, Final, ClassVar, overload
)
from typing_extensions import TypedDict, NotRequired

# Type variables
T = TypeVar('T')
UserType = TypeVar('UserType', bound='User')

# Literal types
UserStatus = Literal['active', 'inactive', 'pending', 'suspended']
LogLevel = Literal['debug', 'info', 'warning', 'error', 'critical']

# TypedDict for structured dictionaries
class UserData(TypedDict):
    """Typed dictionary for user data."""
    id: str
    name: str
    email: str
    age: int
    status: UserStatus
    preferences: NotRequired[Dict[str, Any]]  # Optional field

# Protocol for structural typing
class Serializable(Protocol):
    """Protocol for serializable objects."""

    def to_dict(self) -> Dict[str, Any]:
        """Convert object to dictionary."""
        ...

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Serializable":
        """Create object from dictionary."""
        ...

# Generic classes
class Repository(Generic[T]):
    """Generic repository pattern."""

    def __init__(self, model_class: type[T]) -> None:
        self.model_class = model_class

    async def get_by_id(self, id: str) -> Optional[T]:
        """Get entity by ID."""
        # Implementation
        pass

    async def create(self, data: Dict[str, Any]) -> T:
        """Create new entity."""
        # Implementation
        pass

# Function with complex type hints
async def process_users(
    users: List[UserData],
    processor: Callable[[UserData], Awaitable[UserData]],
    filter_func: Optional[Callable[[UserData], bool]] = None,
    batch_size: int = 100
) -> Tuple[List[UserData], List[str]]:
    """Process users in batches with optional filtering."""
    processed_users: List[UserData] = []
    errors: List[str] = []

    # Filter users if filter function provided
    if filter_func:
        users = [user for user in users if filter_func(user)]

    # Process in batches
    for i in range(0, len(users), batch_size):
        batch = users[i:i + batch_size]

        for user in batch:
            try:
                processed_user = await processor(user)
                processed_users.append(processed_user)
            except Exception as e:
                errors.append(f"Error processing user {user['id']}: {str(e)}")

    return processed_users, errors

# Function overloads
@overload
def get_user_info(user_id: str) -> Optional[UserData]:
    ...

@overload
def get_user_info(user_id: str, include_preferences: Literal[True]) -> Optional[UserData]:
    ...

@overload
def get_user_info(user_id: str, include_preferences: Literal[False]) -> Optional[Dict[str, Any]]:
    ...

def get_user_info(
    user_id: str,
    include_preferences: bool = False
) -> Optional[Union[UserData, Dict[str, Any]]]:
    """Get user information with optional preferences."""
    # Implementation
    pass
```

## Error Handling

### Custom Exception Classes

Create a hierarchy of custom exceptions:

```python
# ------------ EXCEPTION CLASSES
class BaseApplicationError(Exception):
    """Base exception for all application errors."""

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.timestamp = datetime.now(timezone.utc)

    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary."""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details,
            "timestamp": self.timestamp.isoformat(),
        }

class ValidationError(BaseApplicationError):
    """Raised when data validation fails."""

    def __init__(
        self,
        message: str,
        field: Optional[str] = None,
        value: Optional[Any] = None
    ) -> None:
        details = {}
        if field:
            details["field"] = field
        if value is not None:
            details["value"] = str(value)

        super().__init__(message, "VALIDATION_ERROR", details)

class NotFoundError(BaseApplicationError):
    """Raised when a requested resource is not found."""

    def __init__(self, resource: str, identifier: str) -> None:
        message = f"{resource} with identifier '{identifier}' not found"
        details = {"resource": resource, "identifier": identifier}
        super().__init__(message, "NOT_FOUND", details)

class ConflictError(BaseApplicationError):
    """Raised when a resource conflict occurs."""

    def __init__(self, message: str, conflicting_field: Optional[str] = None) -> None:
        details = {}
        if conflicting_field:
            details["conflicting_field"] = conflicting_field

        super().__init__(message, "CONFLICT", details)

class AuthenticationError(BaseApplicationError):
    """Raised when authentication fails."""

    def __init__(self, message: str = "Authentication failed") -> None:
        super().__init__(message, "AUTHENTICATION_ERROR")

# Error handling patterns
async def safe_execute(
    func: Callable[..., Awaitable[T]],
    *args,
    **kwargs
) -> Tuple[Optional[T], Optional[Exception]]:
    """Safely execute an async function and return result or error."""
    try:
        result = await func(*args, **kwargs)
        return result, None
    except Exception as e:
        return None, e
```

## Asynchronous Code

### Async/Await Patterns

Use proper async/await patterns for asynchronous operations:

```python
import asyncio
from typing import List, Dict, Any, Optional, AsyncGenerator
import httpx

# Basic async function
async def fetch_user_data(user_id: str) -> Dict[str, Any]:
    """Fetch user data from external API."""
    async with httpx.AsyncClient() as client:
        response = await client.get(f"/api/users/{user_id}")
        response.raise_for_status()
        return response.json()

# Concurrent execution
async def fetch_multiple_users(user_ids: List[str]) -> List[Dict[str, Any]]:
    """Fetch multiple users concurrently."""
    tasks = [fetch_user_data(user_id) for user_id in user_ids]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Filter out exceptions and return successful results
    return [result for result in results if not isinstance(result, Exception)]

# Async generator
async def stream_users(batch_size: int = 100) -> AsyncGenerator[List[User], None]:
    """Stream users in batches."""
    offset = 0

    while True:
        users = await fetch_user_batch(offset, batch_size)

        if not users:
            break

        yield users
        offset += batch_size

async def fetch_user_batch(offset: int, limit: int) -> List[User]:
    """Fetch a batch of users."""
    # Implementation
    return []
```

## Comments and Documentation

### Docstring Standards

Use Google-style docstrings for all functions, classes, and modules:

```python
def calculate_user_score(
    user: User,
    activities: List[Activity],
    weight_factor: float = 1.0
) -> float:
    """Calculate user engagement score based on activities.

    This function computes a weighted score based on user activities
    and applies the specified weight factor for normalization.

    Args:
        user: User object containing user information
        activities: List of user activities to analyze
        weight_factor: Multiplier for score normalization (default: 1.0)

    Returns:
        Calculated engagement score as a float between 0.0 and 100.0

    Raises:
        ValueError: If weight_factor is negative
        TypeError: If activities list contains invalid activity types

    Example:
        >>> user = User(id="123", name="John")
        >>> activities = [Activity(type="login", timestamp=datetime.now())]
        >>> score = calculate_user_score(user, activities, 1.2)
        >>> print(f"User score: {score}")
        User score: 85.5
    """
    if weight_factor < 0:
        raise ValueError("Weight factor must be non-negative")

    # Calculate base score from activities
    base_score = sum(activity.points for activity in activities)

    # Apply weight factor and normalize
    final_score = min(base_score * weight_factor, 100.0)

    return final_score

class UserAnalytics:
    """Analytics service for user behavior analysis.

    This class provides methods for analyzing user behavior patterns,
    calculating engagement metrics, and generating insights.

    Attributes:
        database: Database connection for data retrieval
        cache_ttl: Cache time-to-live in seconds

    Example:
        >>> analytics = UserAnalytics(database_connection)
        >>> metrics = await analytics.get_user_metrics("user_123")
        >>> print(metrics.engagement_score)
        85.5
    """

    def __init__(self, database: DatabaseConnection, cache_ttl: int = 3600) -> None:
        """Initialize analytics service.

        Args:
            database: Database connection instance
            cache_ttl: Cache expiration time in seconds
        """
        self.database = database
        self.cache_ttl = cache_ttl
        self._cache: Dict[str, Any] = {}
```

### Comment Guidelines

Use minimal but meaningful comments following the "no inline documentation" principle:

```python
# ------------ HELPER FUNCTIONS

def validate_user_permissions(user: User, required_permissions: List[str]) -> bool:
    """Validate user has required permissions."""
    user_permissions = set(user.permissions)
    required_permissions_set = set(required_permissions)

    # Check if user has all required permissions
    return required_permissions_set.issubset(user_permissions)

def calculate_cache_key(user_id: str, operation: str) -> str:
    """Generate cache key for user operations."""
    # Use SHA-256 hash for consistent key generation
    import hashlib
    key_string = f"{user_id}:{operation}"
    return hashlib.sha256(key_string.encode()).hexdigest()[:16]

# ------------ MAIN IMPLEMENTATION

class UserService:
    """Service for user management operations."""

    def __init__(self, repository: UserRepository) -> None:
        self.repository = repository
        self.logger = logging.getLogger(__name__)

    async def create_user(self, user_data: Dict[str, Any]) -> User:
        """Create new user with validation."""
        # Validate required fields
        self._validate_required_fields(user_data)

        # Check for existing user with same email
        existing_user = await self.repository.get_by_email(user_data["email"])
        if existing_user:
            raise ConflictError("User with this email already exists")

        # Create and save user
        user = User(**user_data)
        saved_user = await self.repository.create(user)

        self.logger.info(f"Created user: {saved_user.id}")
        return saved_user
```

## Code Quality and Linting

### Python Linting Tools

Follow the organization's Python linting configuration as defined in `docs/development/code-quality/py-linter.md`:

```python
# Key tools and their purposes:
# - Ruff: Fast Python linter and formatter
# - Black: Code formatter for consistent style
# - mypy: Static type checking
# - bandit: Security vulnerability scanning
# - pytest: Testing framework with coverage
```

### Code Quality Practices

```python
# Good: Clear variable names and type hints
def process_user_registration(
    user_data: Dict[str, Any],
    validation_rules: List[ValidationRule]
) -> User:
    """Process user registration with validation."""
    # Validate input data
    for rule in validation_rules:
        if not rule.validate(user_data):
            raise ValidationError(f"Validation failed: {rule.error_message}")

    # Create user instance
    user = User(
        id=generate_user_id(),
        name=user_data["name"],
        email=user_data["email"],
        created_at=datetime.now(timezone.utc)
    )

    return user

# Good: Proper error handling
async def fetch_user_safely(user_id: str) -> Optional[User]:
    """Fetch user with proper error handling."""
    try:
        user = await user_repository.get_by_id(user_id)
        return user
    except NotFoundError:
        logger.warning(f"User not found: {user_id}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error fetching user {user_id}: {str(e)}")
        raise

# Good: Use context managers for resource management
async def process_file_upload(file_path: Path) -> Dict[str, Any]:
    """Process uploaded file with proper resource management."""
    try:
        with open(file_path, 'rb') as file:
            content = file.read()

        # Process file content
        result = await analyze_file_content(content)

        return {
            "status": "success",
            "file_size": len(content),
            "analysis": result
        }
    except FileNotFoundError:
        raise ValidationError(f"File not found: {file_path}")
    except Exception as e:
        logger.error(f"Error processing file {file_path}: {str(e)}")
        raise
```

## Testing Standards

### Test File Structure

```python
"""
- file: TEST_USER_SERVICE.PY

- version: 1.0.0
- author: BleckWolf25
- contributors:

- copyright: Dynamic Innovative Studio

- description:
Unit tests for UserService class.
Tests user creation, validation, and data management functionality.
"""

# ------------ IMPORTS
import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timezone
from typing import Dict, Any

from src.services.user_service import UserService
from src.models.user import User
from src.exceptions import ValidationError, NotFoundError, ConflictError
from src.repositories.user_repository import UserRepository

# ------------ TEST FIXTURES
@pytest.fixture
def mock_user_repository():
    """Create mock user repository."""
    repository = Mock(spec=UserRepository)
    repository.get_by_id = AsyncMock()
    repository.get_by_email = AsyncMock()
    repository.create = AsyncMock()
    repository.update = AsyncMock()
    repository.delete = AsyncMock()
    return repository

@pytest.fixture
def user_service(mock_user_repository):
    """Create user service with mocked dependencies."""
    return UserService(repository=mock_user_repository)

@pytest.fixture
def sample_user_data():
    """Sample user data for testing."""
    return {
        "name": "John Doe",
        "email": "<EMAIL>",
        "age": 30
    }

@pytest.fixture
def sample_user():
    """Sample user instance for testing."""
    return User(
        id="user_123",
        name="John Doe",
        email="<EMAIL>",
        age=30,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc)
    )

# ------------ TEST CLASSES
class TestUserService:
    """Test cases for UserService."""

    @pytest.mark.asyncio
    async def test_create_user_success(
        self,
        user_service: UserService,
        mock_user_repository: Mock,
        sample_user_data: Dict[str, Any],
        sample_user: User
    ):
        """Test successful user creation."""
        # Arrange
        mock_user_repository.get_by_email.return_value = None
        mock_user_repository.create.return_value = sample_user

        # Act
        result = await user_service.create_user(sample_user_data)

        # Assert
        assert result == sample_user
        mock_user_repository.get_by_email.assert_called_once_with(sample_user_data["email"])
        mock_user_repository.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_user_email_conflict(
        self,
        user_service: UserService,
        mock_user_repository: Mock,
        sample_user_data: Dict[str, Any],
        sample_user: User
    ):
        """Test user creation with email conflict."""
        # Arrange
        mock_user_repository.get_by_email.return_value = sample_user

        # Act & Assert
        with pytest.raises(ConflictError, match="User with this email already exists"):
            await user_service.create_user(sample_user_data)

        mock_user_repository.create.assert_not_called()

    @pytest.mark.asyncio
    async def test_get_user_by_id_success(
        self,
        user_service: UserService,
        mock_user_repository: Mock,
        sample_user: User
    ):
        """Test successful user retrieval by ID."""
        # Arrange
        mock_user_repository.get_by_id.return_value = sample_user

        # Act
        result = await user_service.get_user_by_id("user_123")

        # Assert
        assert result == sample_user
        mock_user_repository.get_by_id.assert_called_once_with("user_123")

    @pytest.mark.asyncio
    async def test_get_user_by_id_not_found(
        self,
        user_service: UserService,
        mock_user_repository: Mock
    ):
        """Test user retrieval when user not found."""
        # Arrange
        mock_user_repository.get_by_id.return_value = None

        # Act
        result = await user_service.get_user_by_id("nonexistent_user")

        # Assert
        assert result is None
        mock_user_repository.get_by_id.assert_called_once_with("nonexistent_user")

# ------------ PARAMETRIZED TESTS
class TestUserValidation:
    """Test cases for user validation."""

    @pytest.mark.parametrize("invalid_email", [
        "",
        "invalid-email",
        "@example.com",
        "user@",
        "user@.com",
        None
    ])
    def test_invalid_email_validation(self, invalid_email):
        """Test validation with invalid email addresses."""
        from src.utils.validation import validate_email

        assert not validate_email(invalid_email)

    @pytest.mark.parametrize("valid_email", [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ])
    def test_valid_email_validation(self, valid_email):
        """Test validation with valid email addresses."""
        from src.utils.validation import validate_email

        assert validate_email(valid_email)
```

## Performance Guidelines

### Memory Management

```python
# Good: Use generators for large datasets
def process_large_dataset(data_source: str) -> Generator[Dict[str, Any], None, None]:
    """Process large dataset using generator to save memory."""
    with open(data_source, 'r') as file:
        for line in file:
            if line.strip():
                yield json.loads(line)

# Good: Use context managers for resource cleanup
async def process_database_records() -> List[Dict[str, Any]]:
    """Process database records with proper resource management."""
    async with get_database_connection() as conn:
        async with conn.cursor() as cursor:
            await cursor.execute("SELECT * FROM large_table")

            results = []
            async for row in cursor:
                processed_row = transform_row(row)
                results.append(processed_row)

            return results

# Good: Use __slots__ for memory-efficient classes
class Point:
    """Memory-efficient point class using __slots__."""
    __slots__ = ['x', 'y']

    def __init__(self, x: float, y: float) -> None:
        self.x = x
        self.y = y

    def distance_to(self, other: "Point") -> float:
        """Calculate distance to another point."""
        return ((self.x - other.x) ** 2 + (self.y - other.y) ** 2) ** 0.5

# Good: Use appropriate data structures
def count_word_frequencies(text: str) -> Dict[str, int]:
    """Count word frequencies using Counter for efficiency."""
    from collections import Counter
    words = text.lower().split()
    return dict(Counter(words))

# Good: Lazy evaluation with properties
class ExpensiveCalculation:
    """Class with lazy evaluation for expensive operations."""

    def __init__(self, data: List[float]) -> None:
        self.data = data
        self._mean: Optional[float] = None
        self._std_dev: Optional[float] = None

    @property
    def mean(self) -> float:
        """Calculate mean with lazy evaluation."""
        if self._mean is None:
            self._mean = sum(self.data) / len(self.data)
        return self._mean

    @property
    def std_dev(self) -> float:
        """Calculate standard deviation with lazy evaluation."""
        if self._std_dev is None:
            mean_val = self.mean
            variance = sum((x - mean_val) ** 2 for x in self.data) / len(self.data)
            self._std_dev = variance ** 0.5
        return self._std_dev
```

### Optimization Techniques

```python
# Good: Use list comprehensions for simple transformations
def process_user_ids(user_ids: List[str]) -> List[str]:
    """Process user IDs efficiently."""
    return [user_id.upper().strip() for user_id in user_ids if user_id]

# Good: Use set operations for membership testing
def filter_active_users(users: List[User], active_user_ids: Set[str]) -> List[User]:
    """Filter active users using set for O(1) lookup."""
    return [user for user in users if user.id in active_user_ids]

# Good: Use functools.lru_cache for expensive computations
from functools import lru_cache

@lru_cache(maxsize=128)
def fibonacci(n: int) -> int:
    """Calculate Fibonacci number with memoization."""
    if n < 2:
        return n
    return fibonacci(n - 1) + fibonacci(n - 2)

# Good: Use asyncio.gather for concurrent operations
async def fetch_user_profiles(user_ids: List[str]) -> List[Dict[str, Any]]:
    """Fetch multiple user profiles concurrently."""
    tasks = [fetch_user_profile(user_id) for user_id in user_ids]
    return await asyncio.gather(*tasks, return_exceptions=True)
```

## Security Considerations

### Input Validation and Sanitization

```python
import re
import html
from typing import Any, Dict, List
from urllib.parse import quote

# Input validation
def validate_user_input(data: Dict[str, Any]) -> Dict[str, Any]:
    """Validate and sanitize user input."""
    validated_data = {}

    # Validate email
    if 'email' in data:
        email = str(data['email']).strip().lower()
        if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
            raise ValidationError("Invalid email format")
        validated_data['email'] = email

    # Validate name (no special characters)
    if 'name' in data:
        name = str(data['name']).strip()
        if not re.match(r'^[a-zA-Z\s\-\'\.]+$', name):
            raise ValidationError("Name contains invalid characters")
        if len(name) > 100:
            raise ValidationError("Name too long")
        validated_data['name'] = name

    # Validate age
    if 'age' in data:
        try:
            age = int(data['age'])
            if not 0 <= age <= 150:
                raise ValidationError("Age must be between 0 and 150")
            validated_data['age'] = age
        except (ValueError, TypeError):
            raise ValidationError("Age must be a valid integer")

    return validated_data

# HTML sanitization
def sanitize_html_content(content: str) -> str:
    """Sanitize HTML content to prevent XSS."""
    # Escape HTML entities
    sanitized = html.escape(content)

    # Remove potentially dangerous patterns
    dangerous_patterns = [
        r'<script[^>]*>.*?</script>',
        r'javascript:',
        r'on\w+\s*=',
        r'<iframe[^>]*>.*?</iframe>',
    ]

    for pattern in dangerous_patterns:
        sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE | re.DOTALL)

    return sanitized

# SQL injection prevention
async def get_user_by_email_safe(email: str) -> Optional[User]:
    """Get user by email with SQL injection prevention."""
    # Use parameterized queries
    query = "SELECT * FROM users WHERE email = %s"

    async with get_database_connection() as conn:
        async with conn.cursor() as cursor:
            await cursor.execute(query, (email,))
            result = await cursor.fetchone()

            if result:
                return User(**result)
            return None

# Path traversal prevention
def safe_file_path(filename: str, base_directory: Path) -> Path:
    """Create safe file path preventing directory traversal."""
    # Remove dangerous characters and patterns
    safe_filename = re.sub(r'[^\w\-_\.]', '', filename)

    # Prevent empty filename
    if not safe_filename:
        raise ValidationError("Invalid filename")

    # Create full path and resolve
    full_path = (base_directory / safe_filename).resolve()

    # Ensure path is within base directory
    if not str(full_path).startswith(str(base_directory.resolve())):
        raise ValidationError("Path traversal attempt detected")

    return full_path
```

### Secure Coding Practices

```python
import secrets
import hashlib
import hmac
from cryptography.fernet import Fernet
from typing import bytes

# Secure random generation
def generate_secure_token(length: int = 32) -> str:
    """Generate cryptographically secure random token."""
    return secrets.token_urlsafe(length)

def generate_secure_password(length: int = 16) -> str:
    """Generate secure random password."""
    alphabet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
    return ''.join(secrets.choice(alphabet) for _ in range(length))

# Secure password hashing
def hash_password_secure(password: str, salt: Optional[bytes] = None) -> Tuple[str, bytes]:
    """Hash password using secure algorithm with salt."""
    import bcrypt

    if salt is None:
        salt = bcrypt.gensalt()

    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8'), salt

def verify_password_secure(password: str, hashed: str) -> bool:
    """Verify password against secure hash."""
    import bcrypt
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

# Secure data encryption
class SecureDataManager:
    """Manage secure data encryption and decryption."""

    def __init__(self, encryption_key: Optional[bytes] = None) -> None:
        if encryption_key is None:
            encryption_key = Fernet.generate_key()
        self.cipher = Fernet(encryption_key)

    def encrypt_data(self, data: str) -> bytes:
        """Encrypt sensitive data."""
        return self.cipher.encrypt(data.encode('utf-8'))

    def decrypt_data(self, encrypted_data: bytes) -> str:
        """Decrypt sensitive data."""
        return self.cipher.decrypt(encrypted_data).decode('utf-8')

# Secure API key validation
def validate_api_key(provided_key: str, stored_key_hash: str) -> bool:
    """Validate API key using constant-time comparison."""
    # Hash the provided key
    provided_hash = hashlib.sha256(provided_key.encode()).hexdigest()

    # Use constant-time comparison to prevent timing attacks
    return hmac.compare_digest(provided_hash, stored_key_hash)

# Environment variable security
def get_secure_config() -> Dict[str, str]:
    """Get configuration from environment variables securely."""
    import os

    required_vars = ['DATABASE_URL', 'SECRET_KEY', 'API_KEY']
    config = {}

    for var in required_vars:
        value = os.getenv(var)
        if not value:
            raise RuntimeError(f"Required environment variable {var} not set")
        config[var] = value

    return config
```

## References

- [PEP 8 – Style Guide for Python Code](https://peps.python.org/pep-0008/)
- [PEP 484 – Type Hints](https://peps.python.org/pep-0484/)
- [Python Documentation](https://docs.python.org/3/)
- [Ruff Documentation](https://docs.astral.sh/ruff/)
- [Black Documentation](https://black.readthedocs.io/)
- [mypy Documentation](https://mypy.readthedocs.io/)
- [pytest Documentation](https://docs.pytest.org/)
- [Python Security Best Practices](https://python.org/dev/security/)

> Python code standards are enforced through Ruff, Black, mypy, and other linting tools as configured in the py-linter documentation. All developers must follow these standards to maintain code quality, security, and consistency across the Dynamic Innovative Studio organization.
