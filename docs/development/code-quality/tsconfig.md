# TSConfig - TypeScript Configuration

TSConfig helps developers define and maintain consistent, strict, and modern TypeScript settings across all projects.

## Purpose

The purpose of this document is to outline the TypeScript configuration standards that should be followed across all projects within the Dynamic Innovative Studio organization. These settings ensure maximum type safety, compatibility with modern tooling (including ESLint), and maintainability of the codebase.

## Recommended and Most Used Configuration

Below is a strict, modern `tsconfig.json` configuration designed to work seamlessly with the ESLint configuration provided in this organization. This setup enforces best practices, strict type checking, and modern module resolution.

```jsonc
{
  // Modern, strict TypeScript configuration for React + TypeScript projects
  "compilerOptions": {
    "target": "ES2022", // Use latest stable ECMAScript target
    "lib": ["DOM", "DOM.Iterable", "ES2022"],
    "module": "ESNext",
    "moduleResolution": "NodeNext",
    "allowImportingTsExtensions": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true, // Let build tools handle emitting
    "jsx": "react-jsx", // Use the new JSX transform
    "strict": true, // Enable all strict type-checking options
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    "noUncheckedIndexedAccess": true,
    "exactOptionalPropertyTypes": true,
    "skipLibCheck": true, // Speeds up build, safe for most projects
    "types": ["node", "jest"], // Add test and node globals
    "incremental": true
  },
  "include": ["src", "tests", "**/*.ts", "**/*.tsx"],
  "exclude": ["node_modules", "dist", "build", "public"]
}
```

### Key Strictness Features

- **strict**: Enables all strict type-checking options.
- **noImplicitOverride**, **noPropertyAccessFromIndexSignature**, **noUncheckedIndexedAccess**, **exactOptionalPropertyTypes**: Enforce best practices and catch subtle bugs.
- **noEmit**: TypeScript only checks types; actual build output is handled by your bundler (e.g., Vite, Next.js, etc.).
- **moduleResolution: NodeNext**: Ensures compatibility with modern ESM and CommonJS packages.
- **jsx: react-jsx**: Uses the modern JSX transform (no need to import React in every file).

### Compatibility with ESLint

- This configuration is designed to work with the ESLint setup in `eslint.config.js`.
- The `project` field in ESLint's TypeScript parser should point to this `tsconfig.json`.
- Both tools will enforce strictness and modern best practices.

## References

- [TypeScript Configuration Options](https://www.typescriptlang.org/tsconfig)
- [TypeScript Handbook: tsconfig.json](https://www.typescriptlang.org/docs/handbook/tsconfig-json.html)
- [TypeScript + ESLint](https://typescript-eslint.io/)

> All projects within the Dynamic Innovative Studio organization must use this strict `tsconfig.json` as a base. Adjust only if absolutely necessary for your project needs.
