# Prettier - Code Formatting

Prettier helps to enforce a consistent style and formatting in your codebase. It automatically formats code to ensure consistency and readability across different developers.

## Purpose

The purpose of this document is to outline the coding quality and conventions that should be followed across all projects within the Dynamic Innovative Studio organization. These rules are designed to ensure consistency, readability, quality and maintainability of the codebase.

## Recommended and most used configuration

```.prettierrc
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false,
  "bracketSpacing": true,
  "bracketSameLine": false,
  "arrowParens": "avoid",
  "endOfLine": "lf",
  "quoteProps": "as-needed",
  "jsxSingleQuote": true,
  "proseWrap": "preserve",
  "htmlWhitespaceSensitivity": "css",
  "embeddedLanguageFormatting": "auto",
  "overrides": [
    {
      "files": "*.html",
      "options": {
        "singleQuote": false
      }
    }
  ]
}
```

## Recommended and most used prettier ignore configuration

```.prettierignore
# Ignore all files in the dist directory
dist/

# Ignore all files in the build directory
build/

# Ignore all files in the logs directory
logs/
*.log

# Ignore all files in the coverage directory
coverage/

# Ignore all files in the docs directory
docs/

# Ignore all files in the node_modules directory
node_modules/

# Ignore all files in the public directory
public/

# Ignore all files in the tests directory
tests/

# Ignore all files in the .git directory
.git/

# Ignore all environment files
.env
.env.*

# Ignore all files in the .firebase directory
.firebase/

# Ignore all lock files
*.lock

# Ignore all drawio files
*.draw
```

## References

- [Prettier](https://prettier.io/)
- [Prettier for VS Code](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)
- [Prettier Configuration](https://prettier.io/docs/en/configuration.html)
- [Prettier Options](https://prettier.io/docs/en/options.html)

> Prettier is used on all projects within the Dynamic Innovative Studio organization. Please make sure to install the Prettier extension for your editor/IDE.
