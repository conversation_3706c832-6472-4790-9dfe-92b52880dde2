# EditorConfig - Consistent Coding Styles

EditorConfig helps developers define and maintain consistent coding styles between different editors and IDEs.

## Purpose

The purpose of this document is to outline the coding styles and conventions that should be followed across all projects within the Dynamic Innovative Studio organization. These styles are designed to ensure consistency, readability, and maintainability of the codebase.

## Recommended and most used configuration

```.editorconfig
# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# JavaScript, TypeScript, JSX, TSX files
[*.{js,jsx,ts,tsx,mjs,cjs}]
indent_style = space
indent_size = 2

# JSON files
[*.json]
indent_style = space
indent_size = 2

# CSS, SCSS files
[*.{css,scss,sass}]
indent_style = space
indent_size = 2

# HTML files
[*.html]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false

# Package files
[package.json]
indent_style = space
indent_size = 2

# Configuration files
[*.{yml,yaml}]
indent_style = space
indent_size = 2
```

## References

- [EditorConfig](https://editorconfig.org/)
- [EditorConfig for VS Code](https://marketplace.visualstudio.com/items?itemName=EditorConfig.EditorConfig)

> Editor Config is used on all projects within the Dynamic Innovative Studio organization. Please make sure to install the EditorConfig extension for your editor/IDE.
