# Python Linting - Code Quality

Python linting helps to enforce a consistent style and code quality in your Python codebase and to avoid common errors. This document outlines the comprehensive Python linting setup for Dynamic Innovative Studio.

## Purpose

The purpose of this document is to outline the Python linting tools and configurations that should be followed across all projects within the Dynamic Innovative Studio organization. These tools are designed to ensure consistency, readability, quality, and maintainability of the Python codebase.

## Recommended Python Linting Stack

### Core Tools

- **Ruff**: Ultra-fast Python linter and formatter (replaces flake8, isort, and more)
- **Black**: Uncompromising Python code formatter
- **mypy**: Static type checker for Python
- **bandit**: Security linter for Python
- **safety**: Dependency vulnerability scanner

### Tool Configuration

#### Ruff Configuration

Create a `pyproject.toml` file in your project root:

```toml
[tool.ruff]
# Enable pycodestyle (`E`) and Pyflakes (`F`) codes by default.
select = [
    "E",      # pycodestyle errors
    "W",      # pycodestyle warnings
    "F",      # Pyflakes
    "I",      # isort
    "N",      # pep8-naming
    "D",      # pydocstyle
    "UP",     # pyupgrade
    "YTT",    # flake8-2020
    "ANN",    # flake8-annotations
    "S",      # flake8-bandit
    "BLE",    # flake8-blind-except
    "FBT",    # flake8-boolean-trap
    "B",      # flake8-bugbear
    "A",      # flake8-builtins
    "COM",    # flake8-commas
    "C4",     # flake8-comprehensions
    "DTZ",    # flake8-datetimez
    "T10",    # flake8-debugger
    "DJ",     # flake8-django
    "EM",     # flake8-errmsg
    "EXE",    # flake8-executable
    "FA",     # flake8-future-annotations
    "ISC",    # flake8-implicit-str-concat
    "ICN",    # flake8-import-conventions
    "G",      # flake8-logging-format
    "INP",    # flake8-no-pep420
    "PIE",    # flake8-pie
    "T20",    # flake8-print
    "PYI",    # flake8-pyi
    "PT",     # flake8-pytest-style
    "Q",      # flake8-quotes
    "RSE",    # flake8-raise
    "RET",    # flake8-return
    "SLF",    # flake8-self
    "SLOT",   # flake8-slots
    "SIM",    # flake8-simplify
    "TID",    # flake8-tidy-imports
    "TCH",    # flake8-type-checking
    "INT",    # flake8-gettext
    "ARG",    # flake8-unused-arguments
    "PTH",    # flake8-use-pathlib
    "ERA",    # eradicate
    "PD",     # pandas-vet
    "PGH",    # pygrep-hooks
    "PL",     # Pylint
    "TRY",    # tryceratops
    "FLY",    # flynt
    "NPY",    # NumPy-specific rules
    "PERF",   # Perflint
    "FURB",   # refurb
    "LOG",    # flake8-logging
    "RUF",    # Ruff-specific rules
]

ignore = [
    "D100",   # Missing docstring in public module
    "D104",   # Missing docstring in public package
    "D107",   # Missing docstring in __init__
    "ANN101", # Missing type annotation for self in method
    "ANN102", # Missing type annotation for cls in classmethod
    "COM812", # Trailing comma missing (conflicts with Black)
    "ISC001", # Implicitly concatenated string literals (conflicts with Black)
]

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "migrations",
]

# Same as Black.
line-length = 88
indent-width = 4

# Assume Python 3.8+
target-version = "py38"

[tool.ruff.lint.per-file-ignores]
# Tests can use magic values, assertions, and print statements
"**/tests/**/*.py" = [
    "S101",   # Use of assert
    "T201",   # Print found
    "ANN",    # Type annotations
    "D",      # Docstrings
    "PLR2004", # Magic value used in comparison
]

# __init__.py files can have unused imports
"__init__.py" = ["F401"]

# Settings files can have unused imports and long lines
"**/settings/**/*.py" = ["F401", "E501"]
"**/config/**/*.py" = ["F401", "E501"]

[tool.ruff.lint.mccabe]
# Unlike Flake8, default to a complexity level of 10.
max-complexity = 10

[tool.ruff.lint.pydocstyle]
# Use Google-style docstrings.
convention = "google"

[tool.ruff.lint.pylint]
max-args = 5
max-branches = 12
max-returns = 6
max-statements = 50

[tool.ruff.lint.isort]
known-first-party = ["your_project_name"]
force-single-line = false
force-sort-within-sections = true
single-line-exclusions = ["typing"]

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"
```

#### Black Configuration

Add Black configuration to `pyproject.toml`:

```toml
[tool.black]
line-length = 88
target-version = ['py38', 'py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''
```

#### mypy Configuration

Add mypy configuration to `pyproject.toml`:

```toml
[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

# Per-module options
[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false

[[tool.mypy.overrides]]
module = [
    "django.*",
    "requests.*",
    "pytest.*",
]
ignore_missing_imports = true
```

#### Bandit Configuration

Add bandit configuration to `pyproject.toml`:

```toml
[tool.bandit]
exclude_dirs = ["tests", "test_*.py", "*_test.py"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection_process_args in tests

[tool.bandit.assert_used]
skips = ["*_test.py", "test_*.py"]
```

#### pytest Configuration

Add pytest configuration to `pyproject.toml`:

```toml
[tool.pytest.ini_options]
minversion = "6.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
    "--cov-fail-under=80",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
```

#### Coverage Configuration

Add coverage configuration to `pyproject.toml`:

```toml
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__init__.py",
    "*/migrations/*",
    "*/venv/*",
    "*/virtualenv/*",
    "*/.venv/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
skip_covered = false
```

## Installation and Setup

### Installing the Tools

Create a `requirements-dev.txt` file:

```txt
# Linting and formatting
ruff>=0.1.0
black>=23.0.0
mypy>=1.5.0
bandit>=1.7.0
safety>=2.3.0

# Testing
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
pytest-asyncio>=0.21.0

# Type stubs
types-requests>=2.31.0
types-PyYAML>=6.0.0
types-python-dateutil>=2.8.0
```

Install the development dependencies:

```bash
pip install -r requirements-dev.txt
```

### Pre-commit Hooks

Create a `.pre-commit-config.yaml` file:

```yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: debug-statements

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.1.0
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
      - id: ruff-format

  - repo: https://github.com/psf/black
    rev: 23.9.1
    hooks:
      - id: black

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.5.1
    hooks:
      - id: mypy
        additional_dependencies: [types-all]

  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: ["-c", "pyproject.toml"]
        additional_dependencies: ["bandit[toml]"]

  - repo: https://github.com/gitguardian/ggshield
    rev: v1.25.0
    hooks:
      - id: ggshield
        language: python
        stages: [commit]
```

Install pre-commit:

```bash
pip install pre-commit
pre-commit install
```

## IDE Integration

### VS Code Configuration

Create `.vscode/settings.json`:

```json
{
  "python.defaultInterpreterPath": "./venv/bin/python",
  "python.linting.enabled": true,
  "python.linting.ruffEnabled": true,
  "python.linting.mypyEnabled": true,
  "python.linting.banditEnabled": true,
  "python.formatting.provider": "black",
  "python.formatting.blackArgs": ["--line-length=88"],
  "python.sortImports.args": ["--profile", "black"],
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  },
  "python.testing.pytestEnabled": true,
  "python.testing.unittestEnabled": false,
  "python.testing.pytestArgs": ["tests"],
  "[python]": {
    "editor.defaultFormatter": "ms-python.black-formatter",
    "editor.formatOnSave": true,
    "editor.codeActionsOnSave": {
      "source.organizeImports": true
    }
  }
}
```

### PyCharm Configuration

1. **File > Settings > Tools > External Tools**
2. Add Ruff, Black, and mypy as external tools
3. **File > Settings > Editor > Code Style > Python**
   - Set line length to 88
   - Enable "Use tab character" = false
   - Set tab size and indent to 4

## CI/CD Integration

### GitHub Actions Workflow

Create `.github/workflows/python-ci.yml`:

```yaml
name: Python CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  lint-and-test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12"]

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements-dev.txt
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

      - name: Lint with Ruff
        run: |
          ruff check .
          ruff format --check .

      - name: Format with Black
        run: black --check .

      - name: Type check with mypy
        run: mypy .

      - name: Security check with bandit
        run: bandit -r . -f json -o bandit-report.json

      - name: Dependency vulnerability check
        run: safety check --json --output safety-report.json

      - name: Test with pytest
        run: |
          pytest --cov=src --cov-report=xml --cov-report=html

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          flags: unittests
          name: codecov-umbrella
```

## Command Line Usage

### Daily Development Commands

```bash
# Format code
black .
ruff format .

# Lint code
ruff check .

# Fix auto-fixable issues
ruff check . --fix

# Type checking
mypy .

# Security scanning
bandit -r .

# Run tests with coverage
pytest --cov=src

# Run all checks (create a Makefile or script)
make lint  # or ./scripts/lint.sh
```

### Makefile Example

Create a `Makefile`:

```makefile
.PHONY: lint format type-check security test all-checks install-dev

install-dev:
    pip install -r requirements-dev.txt
    pre-commit install

format:
    black .
    ruff format .
    ruff check . --fix

lint:
    ruff check .

type-check:
    mypy .

security:
    bandit -r .
    safety check

test:
    pytest --cov=src --cov-report=term-missing

all-checks: format lint type-check security test
    @echo "All checks passed!"

clean:
    find . -type f -name "*.pyc" -delete
    find . -type d -name "__pycache__" -delete
    rm -rf .coverage htmlcov/ .pytest_cache/ .mypy_cache/ .ruff_cache/
```

## Tool-Specific Guidelines

### Ruff Best Practices

- Use `ruff check . --fix` to automatically fix issues
- Configure per-file ignores for specific cases
- Use `# noqa: <code>` comments sparingly for legitimate exceptions
- Regularly update Ruff to get new rules and improvements

### Black Best Practices

- Don't fight Black's formatting decisions
- Use `# fmt: off` and `# fmt: on` only when absolutely necessary
- Configure line length consistently across all tools (88 characters)

### mypy Best Practices

- Start with basic type checking and gradually increase strictness
- Use `# type: ignore` comments with specific error codes
- Add type stubs for third-party libraries without types
- Use `typing.TYPE_CHECKING` for import-only type annotations

### Bandit Best Practices

- Review security warnings carefully
- Use `# nosec` comments with justification for false positives
- Regularly update security rules and patterns
- Integrate with CI/CD for continuous security monitoring

## References

- [Ruff Documentation](https://docs.astral.sh/ruff/)
- [Black Documentation](https://black.readthedocs.io/)
- [mypy Documentation](https://mypy.readthedocs.io/)
- [Bandit Documentation](https://bandit.readthedocs.io/)
- [pytest Documentation](https://docs.pytest.org/)
- [Pre-commit Documentation](https://pre-commit.com/)

> Python linting tools are enforced through pre-commit hooks, CI/CD pipelines, and code reviews. All developers must follow these linting standards to maintain code quality and consistency across the Dynamic Innovative Studio organization.
