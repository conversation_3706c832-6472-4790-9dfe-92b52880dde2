# 🚀 Developer Onboarding Checklist

Welcome to the DIS Discord Bot development team! This checklist will help you get set up and productive quickly.

## 📋 Pre-Development Setup

### ✅ Environment Setup

- [ ] **Install Node.js v22.15.0+**
  - Download from [nodejs.org](https://nodejs.org/)
  - Verify: `node --version`

- [ ] **Install Yarn (recommended)**
  - Install: `npm install -g yarn`
  - Verify: `yarn --version`

- [ ] **Install Git**
  - Download from [git-scm.com](https://git-scm.com/)
  - Verify: `git --version`

- [ ] **Install Docker (optional but recommended)**
  - Download from [docker.com](https://docker.com/)
  - Verify: `docker --version && docker-compose --version`

- [ ] **Install VS Code (recommended)**
  - Download from [code.visualstudio.com](https://code.visualstudio.com/)
  - Install recommended extensions (see below)

### ✅ Repository Setup

- [ ] **Clone the repository**

  ```bash
  git clone https://github.com/Dynamic-Innovative-Studio/DIS-Work-Discord-Bot.git
  cd DIS-Work-Discord-Bot
  ```

- [ ] **Install dependencies**

  ```bash
  yarn install
  ```

- [ ] **Set up environment variables**

  ```bash
  cp .env.example .env
  # Edit .env with your actual values
  ```

- [ ] **Create required directories**

  ```bash
  mkdir -p logs public/icons backups
  ```

### ✅ VS Code Configuration

- [ ] **Open the workspace**

  ```bash
  code .vscode/dis-bot.code-workspace
  ```

- [ ] **Install recommended extensions** (should prompt automatically):
  - [ ] ESLint (`dbaeumer.vscode-eslint`)
  - [ ] Prettier (`esbenp.prettier-vscode`)
  - [ ] Docker (`ms-azuretools.vscode-docker`)
  - [ ] GitLens (`eamodio.gitlens`)
  - [ ] Jest (`orta.vscode-jest`)
  - [ ] Error Lens (`usernamehw.errorlens`)
  - [ ] Todo Tree (`gruntfuggly.todo-tree`)
  - [ ] GitHub Actions (`github.vscode-github-actions`)
  - [ ] Code Spell Checker (`streetsidesoftware.code-spell-checker`)
  - [ ] Markdown All in One (`yzhang.markdown-all-in-one`)

- [ ] **Configure VS Code settings** (should be automatic with workspace)
  - [ ] Auto-format on save enabled
  - [ ] ESLint integration working
  - [ ] Prettier formatting working

## 🔧 Discord Bot Setup

### ✅ Discord Developer Portal

- [ ] **Create Discord Application**
  - Go to [Discord Developer Portal](https://discord.com/developers/applications)
  - Click "New Application"
  - Name it appropriately (e.g., "DIS Bot - Dev")

- [ ] **Configure Bot Settings**
  - [ ] Go to "Bot" section
  - [ ] Create bot user
  - [ ] Copy bot token
  - [ ] Enable necessary intents:
    - [ ] Presence Intent
    - [ ] Server Members Intent
    - [ ] Message Content Intent

- [ ] **Get Application Credentials**
  - [ ] Copy Application ID
  - [ ] Copy Client ID
  - [ ] Go to "OAuth2" → "General"
  - [ ] Copy Client Secret
  - [ ] Copy Public Key

- [ ] **Set Bot Permissions**
  - [ ] Go to "OAuth2" → "URL Generator"
  - [ ] Select "bot" and "applications.commands" scopes
  - [ ] Select required permissions:
    - [ ] Send Messages
    - [ ] Use Slash Commands
    - [ ] Manage Messages
    - [ ] Read Message History
    - [ ] Add Reactions
    - [ ] Manage Roles (if needed)

### ✅ Test Discord Server

- [ ] **Create or use test server**
  - [ ] Create a new Discord server for testing
  - [ ] Or get access to existing development server

- [ ] **Invite bot to server**
  - [ ] Use OAuth2 URL from Discord Developer Portal
  - [ ] Ensure bot has necessary permissions

- [ ] **Get Discord IDs**
  - [ ] Enable Developer Mode in Discord
  - [ ] Right-click and copy IDs for:
    - [ ] Server ID
    - [ ] Channel IDs (welcome, admin-log, error-log, general)
    - [ ] Role IDs (admin, hr, verified, etc.)

## 🗄️ Database Setup

### ✅ Option A: Local MySQL

- [ ] **Install MySQL 8.0+**
  - Download from [mysql.com](https://dev.mysql.com/downloads/)
  - Or use package manager: `brew install mysql` (macOS)

- [ ] **Create database and user**

  ```sql
  CREATE DATABASE dis_bot;
  CREATE USER 'dis_bot_user'@'localhost' IDENTIFIED BY 'your_password';
  GRANT ALL PRIVILEGES ON dis_bot.* TO 'dis_bot_user'@'localhost';
  FLUSH PRIVILEGES;
  ```

- [ ] **Test connection**

  ```bash
  mysql -u dis_bot_user -p dis_bot
  ```

### ✅ Option B: Docker MySQL (Recommended)

- [ ] **Start Docker MySQL**

  ```bash
  yarn docker:compose:dev
  ```

- [ ] **Verify database is running**

  ```bash
  docker-compose ps
  ```

## 🧪 Testing & Verification

### ✅ Environment Verification

- [ ] **Test environment variables**

  ```bash
  yarn validate:env  # If this script exists
  # Or manually check .env file
  ```

- [ ] **Run linting**

  ```bash
  yarn lint
  ```

- [ ] **Run tests**

  ```bash
  yarn test
  ```

- [ ] **Check test coverage**

  ```bash
  yarn test:coverage
  ```

### ✅ Bot Functionality

- [ ] **Deploy slash commands**

  ```bash
  yarn new:cmds
  ```

- [ ] **Start bot in development mode**

  ```bash
  yarn dev
  ```

- [ ] **Test basic functionality**
  - [ ] Bot comes online in Discord
  - [ ] Slash commands appear in server
  - [ ] Test a simple command (e.g., `/ping`)
  - [ ] Check logs for errors

- [ ] **Test debugging**
  - [ ] Set a breakpoint in VS Code
  - [ ] Start debugger (F5)
  - [ ] Trigger the breakpoint
  - [ ] Verify debugging works

## 📚 Knowledge & Documentation

### ✅ Codebase Familiarization

- [ ] **Read main documentation**
  - [ ] [README.md](../README.md)
  - [ ] [SETUP.md](../SETUP.md)
  - [ ] [Architecture docs](./architecture/)

- [ ] **Explore project structure**
  - [ ] `src/` - Main source code
  - [ ] `src/commands/` - Slash commands
  - [ ] `src/events/` - Discord event handlers
  - [ ] `src/models/` - Database models
  - [ ] `src/utils/` - Utility functions
  - [ ] `tests/` - Test files
  - [ ] `docs/` - Documentation

- [ ] **Understand key concepts**
  - [ ] Command structure and registration
  - [ ] Event handling system
  - [ ] Database models and queries
  - [ ] Error handling and logging
  - [ ] Security and permissions

### ✅ Development Workflow

- [ ] **Learn Git workflow**
  - [ ] Understand branching strategy
  - [ ] Learn commit message conventions
  - [ ] Practice creating pull requests

- [ ] **Understand testing approach**
  - [ ] Unit tests with Jest
  - [ ] Test coverage requirements
  - [ ] Mocking strategies

- [ ] **Learn deployment process**
  - [ ] Docker containerization
  - [ ] CI/CD pipeline
  - [ ] Environment management

## 🎯 First Tasks

### ✅ Beginner Tasks

- [ ] **Fix a simple bug**
  - Look for issues labeled "good first issue"
  - Practice the full development workflow

- [ ] **Add a simple test**
  - Find an untested function
  - Write a unit test for it

- [ ] **Improve documentation**
  - Add JSDoc comments to a function
  - Update README if needed

- [ ] **Create a simple command**
  - Follow existing command patterns
  - Add proper error handling
  - Include tests

### ✅ Intermediate Tasks

- [ ] **Implement a feature request**
  - Choose from backlog
  - Design and implement solution
  - Add comprehensive tests

- [ ] **Optimize performance**
  - Profile bot performance
  - Identify bottlenecks
  - Implement improvements

- [ ] **Enhance error handling**
  - Improve error messages
  - Add better logging
  - Handle edge cases

## 🤝 Team Integration

### ✅ Communication

- [ ] **Join team channels**
  - [ ] Development Discord server
  - [ ] GitHub repository access
  - [ ] Any team communication tools

- [ ] **Meet the team**
  - [ ] Schedule intro meetings
  - [ ] Understand roles and responsibilities
  - [ ] Learn team processes

- [ ] **Set up notifications**
  - [ ] GitHub notifications
  - [ ] Discord mentions
  - [ ] CI/CD alerts

### ✅ Development Standards

- [ ] **Code style guidelines**
  - [ ] ESLint configuration
  - [ ] Prettier formatting
  - [ ] Naming conventions

- [ ] **Review process**
  - [ ] Pull request templates
  - [ ] Code review checklist
  - [ ] Approval requirements

- [ ] **Quality standards**
  - [ ] Test coverage requirements
  - [ ] Documentation standards
  - [ ] Performance benchmarks

## ✅ Final Checklist

- [ ] **All tools installed and working**
- [ ] **Environment properly configured**
- [ ] **Bot running successfully**
- [ ] **Tests passing**
- [ ] **Documentation read and understood**
- [ ] **First contribution made**
- [ ] **Team introductions completed**

## 🎉 Congratulations

You're now ready to contribute to the DIS Discord Bot!

### Next Steps

1. **Pick your first task** from the GitHub issues
2. **Create a feature branch** for your work
3. **Make your changes** following the coding standards
4. **Write tests** for your changes
5. **Submit a pull request** for review
6. **Iterate based on feedback**

### Getting Help

- **Technical questions**: Ask in the development Discord channel
- **Process questions**: Reach out to team leads
- **Urgent issues**: Contact <<EMAIL>>

Welcome to the team! 🚀
