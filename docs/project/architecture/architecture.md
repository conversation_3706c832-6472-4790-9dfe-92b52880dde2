# Architecture Documentation

This document provides a comprehensive overview of the DIS Discord Bot architecture, including system design, component interactions, data flow, and architectural patterns.

## Table of Contents

1. [System Overview](#system-overview)
2. [Architecture Patterns](#architecture-patterns)
3. [Core Components](#core-components)
4. [Data Layer](#data-layer)
5. [Security Architecture](#security-architecture)
6. [Process Management](#process-management)
7. [Configuration Management](#configuration-management)
8. [Error Handling & Monitoring](#error-handling--monitoring)
9. [Development Patterns](#development-patterns)
10. [Deployment Architecture](#deployment-architecture)

## System Overview

The DIS Discord Bot is a Node.js application built with Discord.js v14+ that provides automated onboarding, message management, and administrative tools for Dynamic Innovative Studio's Discord server.

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Discord API Gateway                      │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                  DIS Discord Bot                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐│
│  │   Events    │ │  Commands   │ │      Middleware         ││
│  │             │ │             │ │                         ││
│  │ • ready     │ │ • admin/    │ │ • Authentication        ││
│  │ • message   │ │ • member/   │ │ • Command Handler       ││
│  │ • member+   │ │ • hr/       │ │ • Error Handler         ││
│  │ • interact  │ │             │ │ • Rate Limiting         ││
│  └─────────────┘ └─────────────┘ └─────────────────────────┘│
│                                                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐│
│  │  Services   │ │   Models    │ │       Utilities         ││
│  │             │ │             │ │                         ││
│  │ • Onboard   │ │ • User      │ │ • Logger                ││
│  │ • Message   │ │ • Message   │ │ • Database              ││
│  │             │ │             │ │ • Validators            ││
│  │             │ │             │ │ • Cache                 ││
│  └─────────────┘ └─────────────┘ └─────────────────────────┘│
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                   MySQL Database                           │
│                                                             │
│  • dis_internal_workers (Users)                           │
│  • dis_internal_messages (Messages)                       │
│  • Encrypted sensitive data                               │
└─────────────────────────────────────────────────────────────┘
```

## Architecture Patterns

### 1. Layered Architecture

The bot follows a layered architecture pattern with clear separation of concerns:

- **Presentation Layer**: Discord.js event handlers and command interfaces
- **Business Logic Layer**: Services and middleware for core functionality
- **Data Access Layer**: Models and database utilities
- **Infrastructure Layer**: Configuration, logging, and external integrations

### 2. Event-Driven Architecture

The system is built around Discord.js events:

```javascript
// Event registration pattern
client.on('ready', () => readyHandler(client));
client.on('messageCreate', (message) => messageHandler(message));
client.on('guildMemberAdd', (member) => onboardingHandler(member));
client.on('interactionCreate', (interaction) => commandHandler(interaction, client));
```

### 3. Command Pattern

Commands are implemented using the Command Pattern with middleware:

```javascript
// Command structure
export default {
  data: new SlashCommandBuilder()
    .setName('command')
    .setDescription('Description'),
  execute: CommandHandler.standard('command', async (interaction, client) => {
    // Command logic
  })
};
```

### 4. Middleware Pattern

Middleware provides cross-cutting concerns:

```javascript
// Middleware composition
const execute = CommandHandler.admin('broadcast',
  withCommandSecurity({ requiredRoles: ['admin'] })(
    async (interaction, client) => {
      // Command implementation
    }
  )
);
```

## Core Components

### 1. Entry Points

#### index.js

- Main application entry point
- Discord client initialization
- Event registration
- Graceful shutdown handling
- IPC communication setup

#### launch.js

- Process launcher and manager
- Restart logic and throttling
- Signal handling
- Health monitoring

### 2. Event System

#### Event Handlers

- **ready.js**: Bot initialization and startup tasks
- **messageCreate.js**: DM processing and onboarding flow
- **guildMemberAdd.js**: New member welcome and onboarding trigger
- **interactionCreate.js**: Command routing and execution

#### Event Flow

```zsh
Discord Event → Event Handler → Middleware → Business Logic → Response
```

### 3. Command System

#### Command Structure

```
src/commands/
├── admin/          # Administrator commands
│   ├── broadcast.js
│   ├── manageEmployee.js
│   ├── message.js
│   ├── onboardingProgress.js
│   ├── restart.js
│   ├── shutdown.js
│   ├── stats.js
│   └── viewMessages.js
└── member/         # Member commands
    ├── help.js
    ├── ping.js
    └── register.js
```

#### Command Loading

Commands are dynamically loaded at startup:

```javascript
// Command loading pattern
const commandFiles = fs.readdirSync(commandsPath)
  .filter(file => file.endsWith('.js'));

for (const file of commandFiles) {
  const command = await import(filePath);
  client.commands.set(command.default.data.name, command.default);
}
```

### 4. Middleware System

#### Authentication Middleware

- Role-based access control
- Permission validation
- Bot target prevention

#### Command Handler Middleware

- Cooldown management
- Error handling
- Logging and metrics
- Safe reply handling

#### Security Middleware

- Input validation
- Rate limiting
- Audit logging

## Data Layer

### 1. Database Architecture

#### MySQL Schema

```sql
-- Users table
CREATE TABLE dis_internal_workers (
  id_dis_internal_workers VARCHAR(255) PRIMARY KEY,
  tag VARCHAR(255),
  email TEXT,                    -- Encrypted
  nickname VARCHAR(255),
  robloxUsername VARCHAR(255),
  timeZone VARCHAR(255),
  verified TINYINT(1),
  joinedAt DATETIME,
  verifiedAt DATETIME,
  onboardingStartTime DATETIME,
  metadata JSON,
  onboardingStatus VARCHAR(50),
  lastInteraction DATETIME
);

-- Messages table
CREATE TABLE dis_internal_messages (
  id VARCHAR(255) PRIMARY KEY,
  userId VARCHAR(255),
  content TEXT,
  type ENUM('incoming', 'outgoing'),
  authorId VARCHAR(255),
  timestamp DATETIME,
  metadata JSON
);
```

#### Connection Management

- Connection pooling with mysql2
- Retry logic for failed connections
- Graceful connection cleanup
- Health checks and monitoring

### 2. Data Models

#### User Model

```javascript
class User {
  constructor(userData) {
    this.id = userData.id;
    this.tag = userData.tag;
    this.email = userData.email;        // Encrypted in storage
    this.verified = userData.verified;
    // ... other properties
  }

  toObject() {
    return {
      id_dis_internal_workers: this.id,
      email: this.email ? encrypt(this.email) : null,
      // ... encrypted sensitive data
    };
  }

  static fromMySQL(row) {
    return new User({
      id: row.id_dis_internal_workers,
      email: row.email ? decrypt(row.email) : null,
      // ... decrypted data
    });
  }
}
```

#### Message Model

```javascript
class Message {
  constructor(messageData) {
    this.id = messageData.id;
    this.userId = messageData.userId;
    this.content = messageData.content;
    this.type = messageData.type;       // 'incoming' | 'outgoing'
    this.timestamp = messageData.timestamp;
  }

  static createIncoming(userId, content, metadata = {}) {
    return new Message({
      id: `${userId}-${Date.now()}`,
      userId,
      content,
      type: 'incoming',
      timestamp: new Date(),
      metadata
    });
  }
}
```

### 3. Data Access Layer

#### Database Class

- Singleton pattern for connection management
- Query execution with parameter sanitization
- Transaction support
- Error handling and logging

#### Repository Pattern

```javascript
class Database {
  async getUser(userId) {
    const rows = await this.query(
      'SELECT * FROM dis_internal_workers WHERE id_dis_internal_workers = ?',
      [userId]
    );
    return rows.length > 0 ? User.fromMySQL(rows[0]) : null;
  }

  async saveUser(user) {
    const userData = this._prepareForMySQL(user.toObject());
    await this.query(
      'INSERT INTO dis_internal_workers (...) VALUES (...) ON DUPLICATE KEY UPDATE ...',
      Object.values(userData)
    );
  }
}
```

## Security Architecture

### 1. Data Encryption

#### Sensitive Data Protection

```javascript
// AES-256-GCM encryption for sensitive data
function encrypt(text) {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher('aes-256-gcm', ENCRYPTION_KEY);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  const authTag = cipher.getAuthTag();
  return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
}
```

#### Key Management

- Environment variable storage
- Key rotation support
- Separate keys for different data types

### 2. Access Control

#### Role-Based Permissions

```javascript
// Permission hierarchy
const ROLES = {
  ADMIN: ['admin'],           // Full access
  HR: ['hr', 'admin'],        // HR + Admin access
  MEMBER: ['member', 'hr', 'admin']  // Basic access
};

function hasAdminPermission(member) {
  return member.roles.cache.has(botConfig.roles.admin) ||
         member.permissions.has(PermissionFlagsBits.Administrator);
}
```

#### Command Security

- Role validation middleware
- Permission checks before execution
- Audit logging for sensitive operations

### 3. Input Validation

#### Validation Utilities

```javascript
// Email validation with RFC-5322 compliance
function isValidEmail(email) {
  const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
  return emailRegex.test(email);
}

// Discord ID validation
function isValidDiscordId(id) {
  return /^\d{17,19}$/.test(id);
}
```

#### Sanitization

- Input sanitization for all user data
- SQL injection prevention
- XSS protection for stored content

## Process Management

### 1. Application Lifecycle

#### Startup Sequence

```zsh
1. Environment validation
2. Database connection
3. Discord client initialization
4. Command loading
5. Event registration
6. Ready state
```

#### Shutdown Sequence

```zsh
1. Signal reception (SIGTERM/SIGINT)
2. Stop accepting new requests
3. Complete ongoing operations
4. Close database connections
5. Disconnect Discord client
6. Process exit
```

### 2. Process Monitoring

#### Health Checks

- Database connectivity
- Discord API status
- Memory usage monitoring
- Error rate tracking

#### Restart Logic

```javascript
// Restart throttling
const launcherConfig = {
  maxRestartAttempts: 5,
  restartCooldown: 60000,    // 1 minute
  restartTimeout: 10000      // 10 seconds
};
```

### 3. IPC Communication

#### Launcher-Bot Communication

```javascript
// IPC setup for process management
ipc.config.id = 'dis-bot-launcher';
ipc.serve(() => {
  ipc.server.on('restart', () => {
    gracefulRestart();
  });
  ipc.server.on('shutdown', () => {
    gracefulShutdown();
  });
});
```

## Configuration Management

### 1. Configuration Structure

#### Centralized Configuration

```javascript
// src/config/config.js
export const botConfig = {
  // Discord settings
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.DirectMessages
  ],

  // Database configuration
  database: {
    dbHost: process.env.DB_HOST,
    dbUser: process.env.DB_USER,
    dbPassword: process.env.DB_PASSWORD,
    dbName: process.env.DB_NAME,
    connectionLimit: 10,
    queueLimit: 0
  },

  // Role and channel IDs
  roles: {
    admin: process.env.ADMIN_ROLE_ID,
    hr: process.env.HR_ROLE_ID,
    verified: process.env.VERIFIED_ROLE_ID
  },

  // Feature flags and settings
  cooldowns: {
    commands: 3000,
    commandCooldowns: {
      ping: 10000,
      broadcast: 600000
    }
  }
};
```

### 2. Environment Management

#### Environment Variables

```bash
# Core Discord settings
TOKEN=your_bot_token
CLIENT_ID=your_client_id
APPLICATION_ID=your_application_id

# Database configuration
DB_HOST=localhost
DB_USER=bot_user
DB_PASSWORD=secure_password
DB_NAME=dis_bot

# Security keys
USER_DATA_ENCRYPTION_KEY=your_encryption_key

# Feature configuration
APP_ENV=production
LOG_LEVEL=info
```

#### Environment Validation

```javascript
function validateEnvironment() {
  const required = ['TOKEN', 'CLIENT_ID', 'DB_HOST', 'DB_USER', 'DB_PASSWORD'];
  const missing = required.filter(key => !process.env[key]);

  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
}
```

## Error Handling & Monitoring

### 1. Logging Architecture

#### Structured Logging

```javascript
// Pino-based logging with multiple transports
const logger = pino({
  level: 'info',
  formatters: {
    level(label) {
      return { level: label.toUpperCase() };
    }
  },
  transport: {
    targets: [
      { target: 'pino/file', options: { destination: './logs/bot.log' } },
      { target: 'pino-pretty', options: { colorize: true } }
    ]
  }
});
```

#### Log Levels and Context

```javascript
// Contextual logging
logger.info({
  event: 'command_execute',
  command: 'broadcast',
  user: interaction.user.id,
  guild: interaction.guild?.id
}, 'Broadcast command executed');
```

### 2. Error Handling Strategy

#### Centralized Error Handler

```javascript
export async function handleError(error, context = {}, client, options = {}) {
  // Log to console and file
  logger.error(error.message, {
    ...context,
    stack: error.stack
  });

  // Report to Sentry
  Sentry.captureException(error);

  // Notify Discord error channel
  const errorEmbed = new EmbedBuilder()
    .setColor('#F04747')
    .setTitle('Bot Error')
    .setDescription(error.message)
    .addFields([
      { name: 'Command', value: context.command || 'N/A' },
      { name: 'User', value: context.user || 'N/A' }
    ]);

  await errorChannel.send({ embeds: [errorEmbed] });
}
```

#### Error Recovery

- Graceful degradation for non-critical errors
- Automatic retry for transient failures
- Circuit breaker pattern for external services

### 3. Monitoring & Observability

#### Metrics Collection

- Command usage statistics
- Error rates and types
- Performance metrics
- Database query performance

#### Health Monitoring

```javascript
// Health check endpoint
async function healthCheck() {
  const checks = {
    database: await checkDatabaseConnection(),
    discord: client.readyAt !== null,
    memory: process.memoryUsage().heapUsed < MAX_MEMORY
  };

  return {
    status: Object.values(checks).every(Boolean) ? 'healthy' : 'unhealthy',
    checks,
    timestamp: new Date().toISOString()
  };
}
```

## Development Patterns

### 1. Code Organization

#### File Structure Conventions

```zsh
src/
├── commands/           # Command implementations
│   ├── admin/         # Admin-only commands
│   └── member/        # Member commands
├── events/            # Discord event handlers
├── middleware/        # Cross-cutting concerns
├── models/           # Data models
├── services/         # Business logic
├── utils/            # Utility functions
└── config/           # Configuration files
```

#### Naming Conventions

- Files: kebab-case (e.g., `command-handler.js`)
- Classes: PascalCase (e.g., `UserModel`)
- Functions: camelCase (e.g., `validateEmail`)
- Constants: UPPER_SNAKE_CASE (e.g., `MAX_RETRY_ATTEMPTS`)

### 2. Testing Patterns

#### Test Structure

```zsh
tests/
├── unit/             # Unit tests
├── integration/      # Integration tests
├── helpers/          # Test utilities
│   ├── mocks.js     # Mock objects
│   └── fixtures.js  # Test data
└── setup/           # Test configuration
```

#### Testing Utilities

```javascript
// Mock Discord interaction
export function createMockInteraction(options = {}) {
  return {
    user: { id: '123456789', tag: 'TestUser#1234' },
    guild: { id: '987654321' },
    reply: jest.fn(),
    followUp: jest.fn(),
    editReply: jest.fn(),
    ...options
  };
}
```

### 3. Code Quality

#### TypeScript Integration

```javascript
// JSDoc for type safety
/**
 * @typedef {import('discord.js').ChatInputCommandInteraction} ChatInputCommandInteraction
 * @typedef {import('discord.js').Client} DiscordClient
 */

/**
 * @param {ChatInputCommandInteraction} interaction
 * @param {DiscordClient} client
 */
async function executeCommand(interaction, client) {
  // Implementation
}
```

#### ESLint Configuration

- Security rules for Node.js
- Discord.js specific patterns
- Code style enforcement

## Deployment Architecture

### 1. Container Strategy

#### Docker Configuration

```dockerfile
FROM node:22-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
EXPOSE 3000

CMD ["node", "launch.js"]
```

#### Multi-stage Builds

- Development stage with dev dependencies
- Production stage with optimized image
- Health check integration

### 2. Environment Management

#### Development Environment

- Local MySQL instance
- Environment variable files
- Hot reloading for development

#### Production Environment

- Managed database service
- Secret management
- Load balancing considerations

### 3. CI/CD Pipeline

#### Build Pipeline

```yaml
# GitHub Actions example
name: Build and Test
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '22'
      - run: npm ci
      - run: npm test
      - run: npm run lint
```

#### Deployment Pipeline

- Automated testing
- Security scanning
- Database migrations
- Rolling deployments

### 4. Monitoring & Alerting

#### Production Monitoring

- Application performance monitoring
- Error tracking with Sentry
- Log aggregation
- Uptime monitoring

#### Alerting Strategy

- Critical error notifications
- Performance degradation alerts
- Resource usage monitoring
- Database connection issues

---

## Service Architecture Patterns

### 1. Onboarding Service

The onboarding service implements a state machine pattern for user verification:

```javascript
// Onboarding flow state management
const onboardingStates = {
  PENDING: 'pending',
  EMAIL_COLLECTION: 'email_collection',
  EMAIL_CONFIRMATION: 'email_confirmation',
  NICKNAME_COLLECTION: 'nickname_collection',
  TIMEZONE_COLLECTION: 'timezone_collection',
  ROBLOX_COLLECTION: 'roblox_collection',
  ROBLOX_CONFIRMATION: 'roblox_confirmation',
  COMPLETED: 'completed'
};
```

#### Flow Management

- Session-based state tracking
- Timeout handling for inactive sessions
- Data validation at each step
- Rollback capabilities for errors

### 2. Message Service

The message service provides abstraction for Discord messaging:

```javascript
// Message service interface
class MessageService {
  async sendMessage(target, content) {
    // Unified messaging interface
  }

  async sendDM(user, content) {
    // Direct message handling
  }

  async broadcastMessage(users, content) {
    // Bulk messaging with rate limiting
  }
}
```

### 3. Cache Management

Implements a multi-tier caching strategy:

```javascript
// Cache hierarchy
const cacheStrategy = {
  L1: 'memory',      // Fast access for frequently used data
  L2: 'redis',       // Shared cache for distributed systems
  L3: 'database'     // Persistent storage
};
```

## Performance Considerations

### 1. Database Optimization

#### Query Optimization

- Indexed columns for frequent lookups
- Connection pooling for concurrent requests
- Query result caching
- Prepared statements for security

#### Schema Design

- Normalized structure for data integrity
- Denormalized views for read performance
- Partitioning for large datasets
- Archive strategies for old data

### 2. Memory Management

#### Resource Monitoring

```javascript
// Memory usage tracking
function trackMemoryUsage() {
  const usage = process.memoryUsage();
  logger.info({
    heapUsed: Math.round(usage.heapUsed / 1024 / 1024),
    heapTotal: Math.round(usage.heapTotal / 1024 / 1024),
    external: Math.round(usage.external / 1024 / 1024)
  }, 'Memory usage');
}
```

#### Garbage Collection

- Periodic cleanup of expired cache entries
- Session timeout management
- Connection pool maintenance
- Event listener cleanup

### 3. Rate Limiting

#### Discord API Limits

- Global rate limit handling
- Per-route rate limit tracking
- Queue management for bulk operations
- Exponential backoff for retries

#### Custom Rate Limiting

```javascript
// Command-specific rate limiting
const rateLimits = {
  broadcast: { requests: 1, window: 600000 },  // 1 per 10 minutes
  message: { requests: 5, window: 60000 },     // 5 per minute
  ping: { requests: 10, window: 60000 }        // 10 per minute
};
```

---

This architecture documentation provides a comprehensive overview of the DIS Discord Bot's design and implementation. The system is built with scalability, maintainability, and security in mind, following modern Node.js and Discord.js best practices.
