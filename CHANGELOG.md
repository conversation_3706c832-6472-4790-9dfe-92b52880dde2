# Dynamic Innovative Studio Discord Work APP - Change Log

---

## Why a Change Log?

The change log is a record of all the changes made to the project. It helps in tracking the progress and understanding the history of the project. It also helps in identifying when a specific change was made and by whom. This is especially useful for debugging and maintaining the project.

---

## How to Use Change Log?

The change log is divided into sections based on the type of change made. Each section contains a list of changes made in that category. The changes are listed in reverse chronological order, with the most recent changes at the top. Each change includes a description of what was changed and who made the change.
The change log is updated regularly to reflect the latest changes made to the project. It is important to keep the change log up to date to ensure that it accurately reflects the current state of the project.

---

### Automated Change Log

Starting from version 3.0.1, this changelog is automatically generated using semantic-release based on conventional commits. The changelog follows the [Conventional Commits](https://conventionalcommits.org/) specification.

**Commit Types:**

- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation only changes
- `style`: Changes that do not affect the meaning of the code
- `refactor`: A code change that neither fixes a bug nor adds a feature
- `perf`: A code change that improves performance
- `test`: Adding missing tests or correcting existing tests
- `chore`: Changes to the build process or auxiliary tools

---

### Manual Change Log (Pre-3.0.1)

Old versions that aren't listed here are not being maintained & won't be listed here anymore.

#### Version 3.0.0 (2025-10-01)

- [x] Feature: Added `.editorconfig`, Prettier, and `.prettierignore` for consistent and automated code formatting.
- [x] Feature: Created `.npmrc` to lock npm engine behavior.
- [x] Feature: Established `CHANGELOG.md` to track all version changes.
- [x] Feature: Added new scripts — `log-clear.js`, `ts-check.js`, `getEmail.js`, and `onboarding.js`.
- [x] Feature: Introduced `help.js` utility for generating help embeds based on user roles.
- [x] Feature: Migrated project database from Firebase to MySQL.
- [x] Feature: Setup of Jest testing framework (no tests written yet).
- [x] Bugfix: Fixed `commandHandler.js` to ensure commands are executed in the correct context.
- [x] Bugfix: Resolved issues with `log.js` to ensure proper logging of all events.
- [x] Improvement: Enhanced `commandHandler.js` for more reliable command execution and better error handling.
- [x] Improvement: Several new commands added to expand user functionality.
- [x] Refactor: Refactored codebase to follow JavaScript 2025 standards with full type annotations.
- [x] Documentation: Improved documentation for all code files, including detailed descriptions of functions and parameters.
- [x] Other: ESLint configuration improved with stricter and clearer rule definitions.

---

## Automated Releases
