# 🤖 DIS Discord Bot – Developer Setup Guide

Welcome to the DIS Discord Bot development environment! This comprehensive guide will get you up and running with the best developer experience possible.

---

## 🎯 Quick Start (TL;DR)

```bash
# Clone and setup
git clone https://github.com/Dynamic-Innovative-Studio/DIS-Work-Discord-Bot.git
cd DIS-Work-Discord-Bot

# Install dependencies
yarn install

# Setup environment
cp .env.example .env
# Edit .env with your values

# Start development
yarn dev
```

---

## 📦 Prerequisites

### Required Software

- **Node.js** v22.15.0+ ([Download](https://nodejs.org/))
- **Yarn** v1.22+ (recommended) or npm v10+
- **Git** v2.30+ ([Download](https://git-scm.com/))

### Optional (for full development experience)

- **Docker** v20.10+ & **Docker Compose** v2.0+ ([Download](https://docker.com/))
- **MySQL** v8+ (or use Docker)
- **VS Code** with recommended extensions

### Discord Requirements

- **Discord Bot Token** from [Discord Developer Portal](https://discord.com/developers/applications)
- **Application ID**, **Client ID**, **Client Secret**, and **Public Key**
- **Server permissions** for your bot

---

## 🚀 Installation & Setup

### 1. Clone the Repository

```bash
git clone https://github.com/Dynamic-Innovative-Studio/DIS-Work-Discord-Bot.git
cd DIS-Work-Discord-Bot
```

### 2. Install Dependencies

```bash
# Using Yarn (recommended)
yarn install

# Or using npm
npm install
```

### 3. Environment Configuration

#### Option A: Standard Setup

```bash
# Copy the example environment file
cp .env.example .env

# Edit the file with your actual values
nano .env  # or use your preferred editor
```

#### Option B: Docker Setup

```bash
# Copy the Docker environment template
cp .env.docker .env

# Edit with your Docker-specific values
nano .env
```

### 4. Required Environment Variables

Edit your `.env` file with these required values:

```bash
# ===== DISCORD CONFIGURATION =====
TOKEN=your_discord_bot_token
APPLICATION_ID=your_application_id
CLIENT_ID=your_client_id
CLIENT_SECRET=your_client_secret
PUBLIC_KEY=your_public_key

# ===== DATABASE CONFIGURATION =====
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=your_db_username
DB_DATABASE=dis_bot
DB_PASSWORD=your_db_password

# ===== SECURITY =====
USER_DATA_ENCRYPTION_KEY=your_32_character_encryption_key

# ===== DISCORD IDS =====
ADMIN_ROLE_ID=your_admin_role_id
HR_ROLE_ID=your_hr_role_id
VERIFIED_ROLE_ID=your_verified_role_id
WELCOME_CHANNEL_ID=your_welcome_channel_id
ADMIN_LOG_CHANNEL_ID=your_admin_log_channel_id
ERROR_LOG_CHANNEL_ID=your_error_log_channel_id
GENERAL_CHANNEL_ID=your_general_channel_id
```

### 5. Create Required Directories

```bash
mkdir -p logs public/icons backups
```

---

## 🛠️ Development Environment

### VS Code Setup (Recommended)

1. **Open the workspace:**

   ```bash
   code .vscode/dis-bot.code-workspace
   ```

2. **Install recommended extensions** when prompted, or manually:

   - ESLint
   - Prettier
   - Docker
   - GitLens
   - Jest
   - Error Lens
   - Todo Tree

3. **Use the integrated debugger:**
   - Press `F5` or go to Run & Debug
   - Select "🚀 Launch Discord Bot"
   - Set breakpoints and debug your code

### Available Development Commands

```bash
# Development
yarn dev                    # Start bot in development mode with hot reload
yarn dev:debug             # Start with debugging enabled
yarn dev:verbose           # Start with verbose logging

# Testing
yarn test                   # Run all tests
yarn test:watch            # Run tests in watch mode
yarn test:coverage         # Run tests with coverage report
yarn test:unit             # Run only unit tests
yarn test:integration      # Run only integration tests

# Code Quality
yarn lint                   # Check code for linting errors
yarn lint:fix              # Fix auto-fixable linting errors
yarn format                 # Format code with Prettier
yarn type-check            # Check TypeScript types

# Building
yarn build                  # Build the project
yarn build:watch           # Build in watch mode
yarn clean                  # Clean build artifacts

# Database
yarn db:migrate            # Run database migrations
yarn db:seed               # Seed database with test data
yarn db:reset              # Reset database

# Discord Commands
yarn new:cmds              # Deploy slash commands to Discord
yarn deploy:commands       # Same as above

# Coverage & Reports
yarn coverage:report       # Generate coverage report
yarn coverage:open         # Open coverage report in browser
yarn coverage:track        # Track coverage trends
```

---

## 🐳 Docker Development

### Quick Docker Setup

```bash
# Start development environment
yarn docker:compose:dev

# View logs
yarn docker:compose:dev:logs

# Stop environment
yarn docker:compose:dev:down
```

### Full Docker Commands

```bash
# Build images
yarn docker:build          # Build production image
yarn docker:build:dev      # Build development image

# Development environment
yarn docker:compose:dev    # Start dev environment
yarn docker:compose:dev:logs  # View dev logs
yarn docker:compose:dev:down  # Stop dev environment

# Production environment
yarn docker:compose:up     # Start production environment
yarn docker:compose:down   # Stop production environment
yarn docker:compose:logs   # View production logs

# Deployment
yarn deploy:staging         # Deploy to staging
yarn deploy:production      # Deploy to production
```

---

## 🧪 Testing & Quality Assurance

### Running Tests

```bash
# Run all tests
yarn test

# Run specific test file
yarn test User.test.js

# Run tests with coverage
yarn test:coverage

# Run tests in watch mode (great for TDD)
yarn test:watch

# Run tests with debugging
yarn test:debug
```

### Code Quality Checks

```bash
# Lint your code
yarn lint

# Fix linting issues automatically
yarn lint:fix

# Format code
yarn format

# Check types (if using TypeScript)
yarn type-check

# Run all quality checks
yarn quality:check
```

### Coverage Reports

```bash
# Generate and view coverage report
yarn coverage:report

# Open coverage report in browser
yarn coverage:open

# Track coverage trends over time
yarn coverage:track
```

---

## 🏁 Running the Bot

### Development Mode

```bash
# Standard development mode
yarn dev

# With debugging enabled
yarn dev:debug

# With verbose logging
yarn dev:verbose
```

### Production Mode

```bash
# Build and start
yarn build
yarn start

# Or use the production script
yarn start:prod
```

### Using VS Code Debugger

1. Open VS Code
2. Go to Run & Debug (Ctrl+Shift+D)
3. Select "🚀 Launch Discord Bot"
4. Press F5 or click the play button
5. Set breakpoints and debug your code

---

## 🩺 Health Monitoring

### Health Check Endpoint

The bot exposes a health check endpoint at:

```text
http://localhost:3000/health
```

### Monitoring Commands

```bash
# Check bot status
curl http://localhost:3000/health

# View real-time logs
yarn logs:follow

# Check Docker container health
docker-compose ps

# View Docker logs
docker-compose logs -f dis-bot
```

---

## 🛡️ Troubleshooting

### Common Issues

#### 1. Bot Won't Start

**Problem:** Bot fails to start with environment variable errors.

**Solution:**

```bash
# Check your .env file
cat .env

# Validate environment variables
yarn validate:env

# Check for missing variables
yarn env:check
```

#### 2. Database Connection Issues

**Problem:** Cannot connect to MySQL database.

**Solutions:**

```bash
# Check MySQL is running
mysql -u root -p

# Test database connection
yarn db:test

# Reset database (if needed)
yarn db:reset

# Use Docker MySQL
yarn docker:compose:dev
```

#### 3. Slash Commands Not Appearing

**Problem:** Commands don't show up in Discord.

**Solutions:**

```bash
# Deploy commands
yarn new:cmds

# Check bot permissions in Discord server
# Ensure bot has "applications.commands" scope

# Clear Discord cache (Discord app)
# Ctrl+Shift+I → Application → Storage → Clear Storage
```

#### 4. Tests Failing

**Problem:** Tests are failing unexpectedly.

**Solutions:**

```bash
# Clear Jest cache
yarn test:clear-cache

# Run tests with verbose output
yarn test:verbose

# Run specific failing test
yarn test --testNamePattern="test name"

# Check test environment
yarn test:env
```

#### 5. Docker Issues

**Problem:** Docker containers won't start.

**Solutions:**

```bash
# Check Docker is running
docker --version

# Clean Docker cache
docker system prune -f

# Rebuild containers
yarn docker:build --no-cache

# Check container logs
yarn docker:compose:logs
```

### Debug Mode

Enable debug mode for detailed logging:

```bash
# Set debug environment variable
export DEBUG=dis-bot:*

# Or add to your .env file
echo "DEBUG=dis-bot:*" >> .env

# Start bot with debug logging
yarn dev:debug
```

### Log Files

Check log files for detailed error information:

```bash
# View latest log file
tail -f logs/combined.log

# View error logs only
tail -f logs/error.log

# Search logs for specific errors
grep "ERROR" logs/combined.log
```

---

## 📚 Additional Resources

### Documentation

- **[Docker Deployment Guide](docs/deployment/DOCKER_DEPLOYMENT.md)** - Complete Docker setup
- **[CI/CD Setup Guide](docs/deployment/CI_CD_SETUP.md)** - GitHub Actions pipeline
- **[Architecture Documentation](docs/architecture/)** - System design and structure
- **[API Documentation](docs/api/)** - Command and event documentation

### Development Tools

- **[VS Code Workspace](.vscode/dis-bot.code-workspace)** - Pre-configured workspace
- **[Code Snippets](.vscode/snippets.code-snippets)** - Useful code templates
- **[Debug Configurations](.vscode/launch.json)** - Ready-to-use debug setups
- **[Task Definitions](.vscode/tasks.json)** - Automated development tasks

### External Links

- **[Discord.js Documentation](https://discord.js.org/)** - Discord.js library docs
- **[Discord Developer Portal](https://discord.com/developers/applications)** - Bot management
- **[MySQL Documentation](https://dev.mysql.com/doc/)** - Database documentation
- **[Docker Documentation](https://docs.docker.com/)** - Container documentation

---

## 🤝 Contributing

### Development Workflow

1. **Create a feature branch:**

   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes and test:**

   ```bash
   yarn test
   yarn lint
   ```

3. **Commit your changes:**

   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

4. **Push and create a pull request:**

   ```bash
   git push origin feature/your-feature-name
   ```

### Code Standards

- **ESLint** for code linting
- **Prettier** for code formatting
- **Jest** for testing
- **JSDoc** for documentation
- **Conventional Commits** for commit messages

---

## 📞 Support & Help

### Getting Help

- **Internal Issues:** Contact <<EMAIL>>
- **Bug Reports:** Create an issue on GitHub
- **Feature Requests:** Create an issue with the "enhancement" label
- **Documentation:** Check the `docs/` directory

### Community

- **Discord Server:** [Join our development server](https://discord.gg/your-server)
- **GitHub Discussions:** [Ask questions and share ideas](https://github.com/your-repo/discussions)

---

## 🎉 You're Ready

Congratulations! You now have a fully configured development environment for the DIS Discord Bot.

**Next steps:**

1. Start the bot with `yarn dev`
2. Open VS Code and explore the codebase
3. Run the tests with `yarn test`
4. Check out the documentation in the `docs/` folder
5. Start building awesome features! 🚀

---

© 2025 Dynamic Innovative Studio. All Rights Reserved.
Confidential – Internal Use Only.
