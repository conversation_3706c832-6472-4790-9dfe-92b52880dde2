{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "🚀 Launch Discord Bot", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/launch.js", "outFiles": ["${workspaceFolder}/dist/**/*.js"], "env": {"NODE_ENV": "development", "DEBUG": "dis-bot:*"}, "console": "integratedTerminal", "internalConsoleOptions": "openOnSessionStart", "restart": true, "runtimeArgs": ["--inspect"]}, {"type": "node", "request": "launch", "name": "🔧 Launch Bot (Direct)", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/src/index.js", "outFiles": ["${workspaceFolder}/dist/**/*.js"], "env": {"NODE_ENV": "development", "DEBUG": "dis-bot:*"}, "console": "integratedTerminal", "internalConsoleOptions": "openOnSessionStart"}, {"type": "node", "request": "launch", "name": "📄 Run Current File", "program": "${file}", "skipFiles": ["<node_internals>/**"], "env": {"NODE_ENV": "development"}, "console": "integratedTerminal"}, {"type": "node", "request": "launch", "name": "🧪 Jest Current File", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["${fileBasenameNoExtension}", "--config", "jest.config.js", "--verbose", "--no-cache"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "env": {"NODE_ENV": "test"}}, {"type": "node", "request": "launch", "name": "🧪 Jest All Tests", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--config", "jest.config.js", "--verbose", "--coverage"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "env": {"NODE_ENV": "test"}}, {"type": "node", "request": "launch", "name": "🚀 Deploy Commands", "program": "${workspaceFolder}/src/deploy-commands.js", "skipFiles": ["<node_internals>/**"], "env": {"NODE_ENV": "development"}, "console": "integratedTerminal"}, {"type": "node", "request": "launch", "name": "📊 Coverage Report", "program": "${workspaceFolder}/scripts/simple-coverage-report.js", "skipFiles": ["<node_internals>/**"], "console": "integratedTerminal"}, {"type": "node", "request": "attach", "name": "🔗 Attach to Process", "port": 9229, "restart": true, "localRoot": "${workspaceFolder}", "remoteRoot": "/app", "skipFiles": ["<node_internals>/**"]}], "compounds": [{"name": "🚀 Launch Bot + Tests", "configurations": ["🚀 Launch Discord Bot", "🧪 Jest All Tests"], "stopAll": true}]}