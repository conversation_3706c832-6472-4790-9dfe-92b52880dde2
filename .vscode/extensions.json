{
  "recommendations": [
    // ===== CORE DEVELOPMENT =====
    "dbaeumer.vscode-eslint",
    "esbenp.prettier-vscode",
    "editorconfig.editorconfig",
    "visualstudioexptteam.vscodeintellicode",
    "ms-vscode.vscode-typescript-next",

    // ===== NODE.JS & JAVASCRIPT =====
    "christian-kohler.npm-intellisense",
    "christian-kohler.path-intellisense",
    "xabikos.javascriptsnippets",
    "ms-vscode.vscode-json",
    "bradlc.vscode-tailwindcss",

    // ===== DISCORD BOT DEVELOPMENT =====
    "crystal-spider.jsdoc-generator",
    "aaron-bond.better-comments",
    "usernamehw.errorlens",
    "gruntfuggly.todo-tree",

    // ===== DOCKER & DEVOPS =====
    "ms-azuretools.vscode-docker",
    "ms-vscode-remote.remote-containers",
    "ms-vscode-remote.remote-ssh",
    "github.vscode-github-actions",
    "redhat.vscode-yaml",

    // ===== GIT & VERSION CONTROL =====
    "eamodio.gitlens",
    "github.vscode-pull-request-github",
    "github.codespaces",

    // ===== TESTING & DEBUGGING =====
    "ms-vscode.test-adapter-converter",
    "hbenl.vscode-test-explorer",
    "ms-vscode.vscode-node-debug2",

    // ===== DOCUMENTATION =====
    "yzhang.markdown-all-in-one",
    "shd101wyy.markdown-preview-enhanced",
    "davidanson.vscode-markdownlint",
    "bierner.markdown-mermaid",

    // ===== PRODUCTIVITY =====
    "vscode-icons-team.vscode-icons",
    "streetsidesoftware.code-spell-checker",
    "formulahendry.auto-rename-tag",
    "formulahendry.auto-close-tag",

    // ===== ENVIRONMENT =====
    "mikestead.dotenv",
    "ms-vscode.vscode-json"
  ],
  "unwantedRecommendations": [
    "ms-vscode.vscode-typescript",
    "hookyqr.beautify",
    "ms-vscode.vscode-css-languageservice"
  ]
}
