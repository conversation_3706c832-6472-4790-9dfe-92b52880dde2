{"version": "2.0.0", "tasks": [{"label": "🚀 Start Bot (Development)", "type": "npm", "script": "dev", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$eslint-stylish"], "runOptions": {"runOn": "folderOpen"}}, {"label": "🧪 Run Tests", "type": "npm", "script": "test", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": ["$eslint-stylish"]}, {"label": "🧪 Run Tests with Coverage", "type": "npm", "script": "test:coverage", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": ["$eslint-stylish"]}, {"label": "🧪 Watch Tests", "type": "npm", "script": "test:watch", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "isBackground": true, "problemMatcher": ["$eslint-stylish"]}, {"label": "🔧 Lint Code", "type": "npm", "script": "lint", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$eslint-stylish"]}, {"label": "🔧 Fix <PERSON>", "type": "npm", "script": "lint:fix", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$eslint-stylish"]}, {"label": "🎨 Format Code", "type": "npm", "script": "format", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "📦 Build Project", "type": "npm", "script": "build", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$tsc"]}, {"label": "🚀 Deploy Commands", "type": "npm", "script": "new:cmds", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "📊 Coverage Report", "type": "npm", "script": "coverage:report", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "🐳 Docker Build", "type": "npm", "script": "docker:build", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "🐳 Docker Compose Up", "type": "npm", "script": "docker:compose:up", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "🐳 Docker Compose Down", "type": "npm", "script": "docker:compose:down", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "🐳 Docker Development", "type": "npm", "script": "docker:compose:dev", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "🔍 Type Check", "type": "shell", "command": "npx tsc --noEmit", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": ["$tsc"]}, {"label": "🧹 Clean Build", "type": "shell", "command": "rm -rf dist coverage .nyc_output", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "📦 Install Dependencies", "type": "shell", "command": "yarn install", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "🔄 <PERSON><PERSON>", "dependsOrder": "sequence", "dependsOn": ["🧹 Clean Build", "📦 Build Project", "🚀 Start Bot (Development)"]}]}