# ========================================================================== #
# GENERAL IGNORES (FOR ALL PROJECTS)                                         #
# ========================================================================== #

# IDE and Editor files
.idea/
.vscode/*
!.vscode/extensions.json
!.vscode/launch.json
!.vscode/settings.json
!.vscode/tasks.json
*.sublime-*
*.swp
*.swo
.DS_Store
Thumbs.db
.history/

# Temporary files
*.tmp
*.temp
*.bak
*.backup
*~
tmp/
temp/

# Log files
*.log
logs/
log/

# Environment variables and secrets
.env
.env.*
!.env.example
.env.local
.env.development.local
.env.test.local
.env.production.local
*.pem
*.key
*.cert
*.p12
*.pfx

# Build and output directories
dist/
build/
out/
output/
target/
bin/
obj/

# Dependency directories (general)
vendor/
.bundle/

# Package manager files
yarn-error.log
.pnpm-debug.log*

# Testing and coverage
coverage/
.coverage
.nyc_output
.pytest_cache/
htmlcov/

# Cache directories
.cache/
.temp/
.eslintcache
.stylelintcache
.parcel-cache/

# Miscellaneous
.DS_Store
Thumbs.db
*.zip
*.tar.gz
*.tgz
*.rar
*.7z
*.pdf
*.exe
*.dll
*.so
*.dylib
*.class

# ========================================================================== #
# PROJECT-SPECIFIC IGNORES (UNCOMMENT AS NEEDED)                             #
# ========================================================================== #

# ===== NODE.JS =====
# Uncomment this section for Node.js projects

node_modules/
jspm_packages/
bower_components/
.npm
*.tsbuildinfo
.node_repl_history
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# ===== CUSTOM PROJECT IGNORES =====

# Docker
# ========================================================================== #
# DOCKER IGNORES                                                            #
# ========================================================================== #
*.tar
*.tar.gz
*.img
*.container
