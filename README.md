# 🤖 DIS Work Bot

[![DIS Logo](public/icons/DIS_Original_logo.png)](https://dynamic-innovative-studio.firebaseapp.com)

> **Secure, extensible, and operations-focused Discord bot for onboarding and internal team management at Dynamic Innovative Studio.**

---

[![CI Status](https://github.com/Dynamic-Innovative-Studio/DIS-Work-Discord-Bot/actions/workflows/ci.yml/badge.svg)](https://github.com/Dynamic-Innovative-Studio/DIS-Work-Discord-Bot/actions/workflows/ci.yml)
[![CD Status](https://github.com/Dynamic-Innovative-Studio/DIS-Work-Discord-Bot/actions/workflows/cd.yml/badge.svg)](https://github.com/Dynamic-Innovative-Studio/DIS-Work-Discord-Bot/actions/workflows/cd.yml)
[![Node.js](https://img.shields.io/badge/node.js-18%20%7C%2020%20%7C%2022-green)](https://nodejs.org/)
[![Discord.js](https://img.shields.io/badge/discord.js-v14-blueviolet)](https://discord.js.org)

---

**DIS Main Work Bot** is a modern Discord bot for onboarding, DM management, and internal team operations at Dynamic Innovative Studio.

---

## 🚀 Overview

DIS Main Work Bot automates onboarding, manages direct messages, and provides robust admin/HR tools for communication, moderation, and employee management. It is designed for reliability, security, and extensibility.

---

## 🌟 Features

- **Automated Onboarding**: DM-based workflow for new users, with email and optional Roblox username collection.
- **Secure Data Handling**: Validates and encrypts sensitive data (e.g., email).
- **Admin & HR Tools**: Broadcast DMs, view message history, manage employee records, and more.
- **Role-Based Access**: Commands and features gated by Discord roles (Admin, HR, Member).
- **Structured Logging & Monitoring**: Pino logger, Sentry error tracking, and health checks.
- **Modular Command System**: Easily add or update commands using Discord.js v14+.
- **Extensible Architecture**: Clean separation of commands, events, models, services, and utilities.

---

## 🛠️ Tech Stack

- **Node.js**: v22.15.0+
- **Discord.js**: v14
- **MySQL**: Data storage, v8+
- **Pino**: Logging
- **Sentry**: Error monitoring
- **Yarn/NPM**: Package management

---

## 🏗️ Project Structure

```zsh
.
├── CHANGELOG.md        # Changelog
├── coverage            # Jest coverage reports
├── docs                # Documentation
├── eslint.config.js     # ESLint configuration
├── index.js            # Second entry point
├── jest.config.js       # Jest Configuration
├── launch.js           # Main entry point
├── logs                # Log files
├── package.json        # Package configuration
├── public              # Static files
├── README.md           # Project README
├── scripts             # Scripts for development
├── SETUP.md            # Setup instructions
├── src
│   ├── apis            # API
│   ├── commands        # Slash commands
│   │   ├── admin       # Admin/&HR commands
│   │   └── member      # Member commands
│   ├── config           # Configuration files
│   ├── deploy-commands.js # Command deployment script
│   ├── events          # Events
│   ├── middleware      # Middleware (Command Security, Handlers, api, etc)
│   ├── models          # Database & template models
│   ├── services        # Services
│   └── utils           # Utility functions
└── yarn.lock           # Yarn lock file
```

---

## 🛡️ Security & Validation

- **Email**: RFC-5322 regex validation
- **Roblox Username**: Optional, sanitized
- **Input Sanitization**: All user input is sanitized
- **Role Checks**: Admin/HR/member permissions enforced
- **Data Encryption**: Sensitive data (e.g., email) encrypted at rest

---

## 🚀 Getting Started

### Prerequisites

- Node.js v22.15.0+
- Discord Bot Token
- MySQL (for data)

### Setup

1. Clone the repo and install dependencies:

   ```bash
   git clone https://github.com/Dynamic-Innovative-Studio/DIS-Work-Discord-Bot.git
   cd DIS-Work-Discord-Bot
   yarn install
   # or
   npm install
   ```

2. Copy `.env` and fill in required variables:

   ```bash
   cp .env.example .env
   # Edit .env with your Discord/MySQL credentials
   ```

3. Deploy slash commands:

   ```bash
   yarn run new:commands
   # or
   npm run new:commands
   ```

4. Start the bot:

   ```bash
   yarn dev
   # or
   npm run dev
   ```

### Production Build & Deployment

#### 🐳 Docker Deployment (Recommended)

1. **Quick Start with Docker:**

   ```bash
   # Clone the repository
   git clone https://github.com/Dynamic-Innovative-Studio/DIS-Work-Discord-Bot.git
   cd DIS-Work-Discord-Bot

   # Copy and configure environment
   cp .env.docker .env
   # Edit .env with your actual values

   # Deploy with Docker Compose
   docker-compose up -d
   ```

2. **Development with Docker:**

   ```bash
   # Start development environment
   npm run docker:compose:dev

   # View logs
   npm run docker:compose:dev:logs
   ```

3. **Available Docker Commands:**

   ```bash
   npm run docker:build              # Build production image
   npm run docker:build:dev          # Build development image
   npm run docker:compose:up         # Start production services
   npm run docker:compose:down       # Stop production services
   npm run deploy:staging            # Deploy to staging
   npm run deploy:production         # Deploy to production
   ```

#### 📦 Traditional Deployment

> **Note:** If your bot loads commands/events dynamically from the filesystem, you must deploy the full source code (not just the `dist/` bundle). The esbuild bundle will not include dynamically loaded files.

1. **Install dependencies (production only):**

   ```bash
   yarn install --production
   # or
   npm ci --only=production
   ```

2. **Ensure required folders exist:**
   - `logs/` (for logs)

3. **Start the bot:**

   ```bash
   yarn start
   # or
   npm run start
   ```

4. **If you want to use the esbuild bundle, you must statically import all commands/events, or configure esbuild to include them.**

- The bundled output will be in the `dist/` directory.
- You can deploy only the `dist/`, `.env`, and `node_modules` (production only) to your server.

---

## 🩺 Monitoring & Health

- **Logging**: Pino logger for structured logs
- **Error Tracking**: Sentry integration (if configured)
- **Health Check**: HTTP endpoint at `http://localhost:3000/health`
- **Docker Health Checks**: Built-in container health monitoring
- **CI/CD Pipeline**: Automated testing and deployment

---

## 🚀 CI/CD Pipeline

The project includes a comprehensive CI/CD pipeline with GitHub Actions:

### Pipeline Stages

1. **Validation** - Branch naming and commit message validation
2. **Linting** - Code quality checks with ESLint and Prettier
3. **Testing** - Unit tests with coverage reporting
4. **Building** - Application build and Docker image creation
5. **Deployment** - Automated deployment to staging and production

### Deployment Environments

- **Staging**: Automatically deployed from `develop` branch
- **Production**: Automatically deployed from `main` branch
- **Manual**: Deploy to any environment via workflow dispatch

### Docker Registry

Images are automatically built and pushed to GitHub Container Registry:

```bash
# Pull latest production image
docker pull ghcr.io/dynamic-innovative-studio/dis-work-discord-bot:latest

# Pull development image
docker pull ghcr.io/dynamic-innovative-studio/dis-work-discord-bot:develop
```

---

## 📚 Documentation

- **Setup & Configuration**: [SETUP.md](./SETUP.md)
- **Docker Deployment**: [docs/deployment/DOCKER_DEPLOYMENT.md](./docs/deployment/DOCKER_DEPLOYMENT.md)
- **CI/CD Setup**: [docs/deployment/CI_CD_SETUP.md](./docs/deployment/CI_CD_SETUP.md)
- **Architecture & Development**: See `docs/` directory

---

## 📝 Usage

- `/register` – Start onboarding (DM-based)
- `/help` – Show available commands
- `/broadcast` – (Admin) DM all users or by role
- `/message` – (Admin) DM a specific user
- `/viewmessages` – (Admin/HR) View DM history with a user
- `/manageemployee` – (Admin/HR) View employee details
- `/onboarding` – (Admin/HR) View onboarding progress

---

## 🔐 Security

- Secrets and tokens are never committed
- All user input is validated and sanitized
- Admin commands require appropriate Discord roles
- Follows Discord rate limits and best practices

---

## 🤝 Contributing

See `docs/org_CONTRIBUTING.md` for contribution guidelines.

---

## 📞 Support

For internal questions or issues, contact:
<<EMAIL>>

- © 2025 Dynamic Innovative Studio. All Rights Reserved.
  Confidential – Internal Use Only.
