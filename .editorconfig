# top-most EditorConfig file
root = true

# All files
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# JavaScript, TypeScript, JSX, TSX files
[*.{js,jsx,ts,tsx,mjs,cjs}]
indent_style = space
indent_size = 2

# JSON files
[*.json]
indent_style = space
indent_size = 2

# CSS, SCSS files
[*.{css,scss,sass}]
indent_style = space
indent_size = 2

# HTML files
[*.html]
indent_style = space
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false

# Package files
[package.json]
indent_style = space
indent_size = 2

# Configuration files
[*.{yml,yaml}]
indent_style = space
indent_size = 2
