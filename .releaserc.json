{"branches": ["main"], "repositoryUrl": "https://github.com/Dynamic-Innovative-Studio/DIS-Work-Discord-Bot.git", "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", ["@semantic-release/changelog", {"changelogFile": "CHANGELOG.md", "changelogTitle": "# Dynamic Innovative Studio Discord Work APP - Change Log\n\n---\n\n## Why a Change Log?\n\nThe change log is a record of all the changes made to the project. It helps in tracking the progress and understanding the history of the project. It also helps in identifying when a specific change was made and by whom. This is especially useful for debugging and maintaining the project.\n\n---\n\n## How to Use Change Log?\n\nThe change log is divided into sections based on the type of change made. Each section contains a list of changes made in that category. The changes are listed in reverse chronological order, with the most recent changes at the top. Each change includes a description of what was changed and who made the change.\nThe change log is updated regularly to reflect the latest changes made to the project. It is important to keep the change log up to date to ensure that it accurately reflects the current state of the project.\n\n---\n\n### Automated Change Log\n\nStarting from version 3.0.1, this changelog is automatically generated using semantic-release based on conventional commits. The changelog follows the [Conventional Commits](https://conventionalcommits.org/) specification.\n\n**Commit Types:**\n- `feat`: A new feature\n- `fix`: A bug fix\n- `docs`: Documentation only changes\n- `style`: Changes that do not affect the meaning of the code\n- `refactor`: A code change that neither fixes a bug nor adds a feature\n- `perf`: A code change that improves performance\n- `test`: Adding missing tests or correcting existing tests\n- `chore`: Changes to the build process or auxiliary tools\n\n---\n\n### Manual Change Log (Pre-3.0.1)\n\nOld versions that aren't listed here are not being maintained & won't be listed here anymore.\n\n#### Version 3.0.0 (2025-10-01)\n\n- [x] Feature: Added `.editorconfig`, Prettier, and `.prettierignore` for consistent and automated code formatting.\n- [x] Feature: Created `.npmrc` to lock npm engine behavior.\n- [x] Feature: Established `CHANGELOG.md` to track all version changes.\n- [x] Feature: Added new scripts — `log-clear.js`, `ts-check.js`, `getEmail.js`, and `onboarding.js`.\n- [x] Feature: Introduced `help.js` utility for generating help embeds based on user roles.\n- [x] Feature: Migrated project database from Firebase to MySQL.\n- [x] Feature: Setup of Jest testing framework (no tests written yet).\n- [x] Bugfix: Fixed `commandHandler.js` to ensure commands are executed in the correct context.\n- [x] Bugfix: Resolved issues with `log.js` to ensure proper logging of all events.\n- [x] Improvement: Enhanced `commandHandler.js` for more reliable command execution and better error handling.\n- [x] Improvement: Several new commands added to expand user functionality.\n- [x] Refactor: Refactored codebase to follow JavaScript 2025 standards with full type annotations.\n- [x] Documentation: Improved documentation for all code files, including detailed descriptions of functions and parameters.\n- [x] Other: ESLint configuration improved with stricter and clearer rule definitions.\n\n---\n\n## Automated Releases"}], ["@semantic-release/npm", {"npmPublish": false}], ["@semantic-release/github", {"assets": [{"path": "dist/**", "label": "Distribution files"}]}], ["@semantic-release/git", {"assets": ["CHANGELOG.md", "package.json"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}]]}