#!/bin/bash

# ========================================================================== #
# DEPLOYMENT SCRIPT FOR DIS DISCORD BOT                                     #
# ========================================================================== #

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

# Default values
ENVIRONMENT="production"
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"
BACKUP_DB=true
HEALTH_CHECK=true
ROLLBACK_ON_FAILURE=true

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -f|--compose-file)
            COMPOSE_FILE="$2"
            shift 2
            ;;
        --env-file)
            ENV_FILE="$2"
            shift 2
            ;;
        --no-backup)
            BACKUP_DB=false
            shift
            ;;
        --no-health-check)
            HEALTH_CHECK=false
            shift
            ;;
        --no-rollback)
            ROLLBACK_ON_FAILURE=false
            shift
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  -e, --environment ENV     Deployment environment (default: production)"
            echo "  -f, --compose-file FILE   Docker Compose file (default: docker-compose.yml)"
            echo "  --env-file FILE           Environment file (default: .env)"
            echo "  --no-backup               Skip database backup"
            echo "  --no-health-check         Skip health check"
            echo "  --no-rollback             Don't rollback on failure"
            echo "  -h, --help                Show this help message"
            exit 0
            ;;
        *)
            error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(development|staging|production)$ ]]; then
    error "Invalid environment: $ENVIRONMENT. Must be development, staging, or production."
    exit 1
fi

# Check if required files exist
if [[ ! -f "$COMPOSE_FILE" ]]; then
    error "Docker Compose file not found: $COMPOSE_FILE"
    exit 1
fi

if [[ ! -f "$ENV_FILE" ]]; then
    error "Environment file not found: $ENV_FILE"
    exit 1
fi

# Function to backup database
backup_database() {
    if [[ "$BACKUP_DB" == true ]]; then
        log "Creating database backup..."
        
        local backup_dir="backups/$(date +'%Y-%m-%d')"
        local backup_file="$backup_dir/dis-bot-backup-$(date +'%Y%m%d-%H%M%S').sql"
        
        mkdir -p "$backup_dir"
        
        # Extract database credentials from env file
        local db_user=$(grep "^DB_USERNAME=" "$ENV_FILE" | cut -d'=' -f2)
        local db_password=$(grep "^DB_PASSWORD=" "$ENV_FILE" | cut -d'=' -f2)
        local db_name=$(grep "^DB_DATABASE=" "$ENV_FILE" | cut -d'=' -f2)
        local db_host=$(grep "^DB_HOST=" "$ENV_FILE" | cut -d'=' -f2 | sed 's/mysql/localhost/')
        
        if docker-compose -f "$COMPOSE_FILE" exec -T mysql mysqldump -u"$db_user" -p"$db_password" "$db_name" > "$backup_file"; then
            success "Database backup created: $backup_file"
        else
            error "Failed to create database backup"
            return 1
        fi
    else
        log "Skipping database backup"
    fi
}

# Function to pull latest images
pull_images() {
    log "Pulling latest Docker images..."
    if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" pull; then
        success "Docker images pulled successfully"
    else
        error "Failed to pull Docker images"
        return 1
    fi
}

# Function to deploy services
deploy_services() {
    log "Deploying services..."
    
    # Stop existing services gracefully
    log "Stopping existing services..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down --timeout 30
    
    # Start services
    log "Starting services..."
    if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d; then
        success "Services started successfully"
    else
        error "Failed to start services"
        return 1
    fi
}

# Function to perform health check
health_check() {
    if [[ "$HEALTH_CHECK" == true ]]; then
        log "Performing health check..."
        
        local max_attempts=20
        local attempt=1
        local health_url="http://localhost:3000/health"
        
        # Wait for services to start
        sleep 30
        
        while [[ $attempt -le $max_attempts ]]; do
            if curl -f "$health_url" >/dev/null 2>&1; then
                success "Health check passed"
                return 0
            else
                warn "Health check failed (attempt $attempt/$max_attempts)"
                sleep 15
                ((attempt++))
            fi
        done
        
        error "Health check failed after $max_attempts attempts"
        return 1
    else
        log "Skipping health check"
        return 0
    fi
}

# Function to rollback deployment
rollback() {
    if [[ "$ROLLBACK_ON_FAILURE" == true ]]; then
        warn "Rolling back deployment..."
        
        # Stop current services
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down --timeout 30
        
        # Restore from backup if available
        local latest_backup=$(find backups -name "*.sql" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -d' ' -f2-)
        
        if [[ -n "$latest_backup" && -f "$latest_backup" ]]; then
            log "Restoring database from backup: $latest_backup"
            
            # Start only MySQL for restoration
            docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d mysql
            sleep 30
            
            # Restore database
            local db_user=$(grep "^DB_USERNAME=" "$ENV_FILE" | cut -d'=' -f2)
            local db_password=$(grep "^DB_PASSWORD=" "$ENV_FILE" | cut -d'=' -f2)
            local db_name=$(grep "^DB_DATABASE=" "$ENV_FILE" | cut -d'=' -f2)
            
            docker-compose -f "$COMPOSE_FILE" exec -T mysql mysql -u"$db_user" -p"$db_password" "$db_name" < "$latest_backup"
            
            success "Database restored from backup"
        fi
        
        warn "Rollback completed. Please investigate the deployment failure."
    else
        log "Rollback disabled. Manual intervention required."
    fi
}

# Function to cleanup old images
cleanup() {
    log "Cleaning up old Docker images..."
    docker image prune -f
    success "Cleanup completed"
}

# Main deployment function
main() {
    log "🚀 Starting deployment to $ENVIRONMENT environment"
    log "Using compose file: $COMPOSE_FILE"
    log "Using environment file: $ENV_FILE"
    
    # Create backup
    if ! backup_database; then
        error "Backup failed. Aborting deployment."
        exit 1
    fi
    
    # Pull latest images
    if ! pull_images; then
        error "Failed to pull images. Aborting deployment."
        exit 1
    fi
    
    # Deploy services
    if ! deploy_services; then
        error "Deployment failed."
        rollback
        exit 1
    fi
    
    # Perform health check
    if ! health_check; then
        error "Health check failed."
        rollback
        exit 1
    fi
    
    # Cleanup
    cleanup
    
    success "🎉 Deployment to $ENVIRONMENT completed successfully!"
    log "Services are running and healthy."
}

# Trap signals for cleanup
trap 'error "Deployment interrupted"; rollback; exit 1' INT TERM

# Run main function
main "$@"
