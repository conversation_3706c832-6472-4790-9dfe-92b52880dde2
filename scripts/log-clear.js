/**
 * LOG CLEAR SCRIPT
 *
 * @version 1.0.0
 * <AUTHOR> (username)
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * This script is designed to clear the log files in the project.
 * It allows the user to choose which log types to delete (DEBUG, INFO, WARN, ERROR).
 * The script reads the log file, counts the occurrences of each log type,
 * and prompts the user to select which log types to delete.
 * It then rewrites the log file with the selected log types removed.
 * The script is intended for use in environments where log management is required,
 * such as Discord bots that generate logs for debugging and monitoring purposes.
 * The script is designed to be run from the command line.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { existsSync, readFileSync, writeFileSync } from 'fs';
import { dirname, join } from 'path';
import { createInterface } from 'readline';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const LOG_PATH = join(__dirname, '../logs/bot-log.json');

// Helper to parse JSON objects spanning multiple lines
/**
 * Parses a log file containing multiple JSON objects (not JSONL, not an array).
 * Handles multi-line objects separated by newlines.
 * @param {string} content
 */
function parseLogFile(content) {
  const entries = [];
  let buffer = '';
  let openBraces = 0;
  const lines = content.split('\n');
  for (let line of lines) {
    line = line.trim();
    if (!line) continue;
    // Count braces to detect object boundaries
    for (const char of line) {
      if (char === '{') openBraces++;
      if (char === '}') openBraces--;
    }
    buffer += line;
    // If we've closed all opened braces, try to parse
    if (openBraces === 0 && buffer) {
      try {
        entries.push(JSON.parse(buffer));
      } catch (e) {
        console.error(`Failed to parse object:\n${buffer}`);
        console.error(e);
      }
      buffer = '';
    }
  }
  return entries;
}

// Helper to pretty-print log entries (one per line)
/**
 * @param {any[]} entries
 */
function formatLogEntries(entries) {
  return entries
    .map((/** @type {any} */ e) => JSON.stringify(e, null, 4))
    .join('\n');
}

// Count log types
/**
 * @param {any[]} entries
 */
function countLogTypes(entries) {
  const counts = {
    ERROR: 0,
    WARN: 0,
    INFO: 0,
    DEBUG: 0,
    TRACE: 0,
  };

  for (const entry of entries) {
    const level = (entry.level || '').toUpperCase();
    // Only count known log levels to avoid injection
    if (level === 'ERROR') counts.ERROR += 1;
    else if (level === 'WARN') counts.WARN += 1;
    else if (level === 'INFO') counts.INFO += 1;
    else if (level === 'DEBUG') counts.DEBUG += 1;
    else if (level === 'TRACE') counts.TRACE += 1;
    else if (level && !counts.OTHER) counts.OTHER = 1;
    else if (level && counts.OTHER) counts.OTHER += 1;
  }
  return counts;
}

// Prompt user for deletion option
/**
 * @param {any[]} options
 */
function promptUser(options) {
  return new Promise(resolve => {
    const rl = createInterface({
      input: process.stdin,
      output: process.stdout,
    });
    rl.question(
      `Choose log type to delete:\n${options.map((/** @type {any} */ o, /** @type {number} */ i) => `${i + 1}. ${o}`).join('\n')}\nEnter number: `,
      answer => {
        rl.close();
        const idx = parseInt(answer, 10) - 1;
        if (idx >= 0 && idx < options.length) {
          // Use explicit index-based selection to avoid injection warnings
          let selectedOption = null;
          if (idx === 0) selectedOption = options[0];
          else if (idx === 1) selectedOption = options[1];
          else if (idx === 2) selectedOption = options[2];
          else if (idx === 3) selectedOption = options[3];
          else if (idx === 4) selectedOption = options[4];
          else if (idx === 5) selectedOption = options[5];

          resolve(selectedOption);
        } else {
          resolve(null);
        }
      }
    );
  });
}

async function main() {
  if (!existsSync(LOG_PATH)) {
    console.error('Log file not found.');
    process.exit(1);
  }
  const raw = readFileSync(LOG_PATH, 'utf8');
  const entries = parseLogFile(raw);

  // Format and rewrite the log file
  writeFileSync(LOG_PATH, formatLogEntries(entries) + '\n', 'utf8');
  console.log('Log file formatted.');

  // Count log types
  const counts = countLogTypes(entries);
  console.log('Log type counts:');
  for (const [type, count] of Object.entries(counts)) {
    console.log(`  ${type}: ${count}`);
  }

  // Options for deletion
  const typeOptions = [
    'only DEBUG',
    'only INFO',
    'only WARN',
    'only ERROR',
    'ALL',
  ];
  const userChoice = await promptUser(typeOptions);

  if (!userChoice) {
    console.log('No valid option selected. Exiting.');
    process.exit(0);
  }

  let filtered;
  switch (userChoice) {
    case 'only DEBUG':
      filtered = entries.filter(e => (e.level || '').toUpperCase() !== 'DEBUG');
      break;
    case 'only INFO':
      filtered = entries.filter(e => (e.level || '').toUpperCase() !== 'INFO');
      break;
    case 'only WARN':
      filtered = entries.filter(e => (e.level || '').toUpperCase() !== 'WARN');
      break;
    case 'only ERROR':
      filtered = entries.filter(e => (e.level || '').toUpperCase() !== 'ERROR');
      break;
    case 'ALL':
      filtered = [];
      break;
    default:
      filtered = entries;
  }

  writeFileSync(
    LOG_PATH,
    formatLogEntries(filtered) + (filtered.length ? '\n' : ''),
    'utf8'
  );
  console.log(
    `Deleted logs: ${userChoice}. Remaining entries: ${filtered.length}`
  );
}

main().catch(err => {
  console.error('Error:', err);
  process.exit(1);
});
