/**
 * DE<PERSON>YP<PERSON>ON EMAIL MANAGEMENT SCRIPT
 *
 * @version 2.0.1
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * This script is designed to fetch a user's email address
 * from the database using their Discord ID. It uses the AES-256-GCM
 * encryption method for security. The script is intended for use in
 * environments where sensitive data handling is required, such as
 * Discord bots that manage user data.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import 'dotenv/config';
import Database from '../src/utils/Database.js';

// ------------ <PERSON>ET<PERSON> USER EMAIL
/**
 * Fetches and displays a user's email by their ID
 * @param {string} userId - The Discord user ID to lookup
 * @returns {Promise<string|null>} The decrypted email or null if not found
 */
async function fetchUserEmail(userId) {
  try {
    // Input validation
    if (!userId || typeof userId !== 'string') {
      throw new Error('Invalid user ID provided');
    }

    // Log the operation (for audit purposes)
    console.warn(`Attempting to fetch email for user: ${userId}`);

    // Fetch the user from the database
    const user = await Database.getUser(userId);

    // Check if user exists
    if (!user) {
      console.warn(`User not found: ${userId}`);
      return null;
    }

    // Check if user has an email
    if (!user.email) {
      console.info(`No email found for user: ${userId}`);
      return null;
    }

    return user.email;
  } catch (error) {
    console.error(`Error fetching user email: ${error.message}`, {
      userId,
      error,
    });

    // Re-throw for caller to handle
    throw error;
  }
}

// ------------ COMMAND LINE ARGUMENTS
/**
 * Parse command line arguments
 * @returns {object} Parsed arguments object
 */
function parseArguments() {
  // Check for command line arguments
  const args = process.argv.slice(2);

  // Display help if requested
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Usage: node d-e-m.js [options] <userId>

Options:
  --help, -h     Show this help message
  --id, -i       Specify user ID (alternative to positional argument)
  --silent, -s   Suppress all output except the email (for scripting)

Examples:
  node d-e-m.js 823961954603368478
  node d-e-m.js --id 823961954603368478
  node d-e-m.js -i 823961954603368478 --silent
    `);
    process.exit(0);
  }

  // Parse arguments
  /** @type {{ userId: string | null, silent: boolean }} */
  const options = {
    userId: null,
    silent: false,
  };

  // Use Array.from to avoid object injection warnings
  const argsArray = Array.from(args);
  for (let i = 0; i < argsArray.length; i++) {
    // Only allow string arguments, and prevent prototype pollution
    const arg =
      // eslint-disable-next-line security/detect-object-injection
      typeof argsArray[i] === 'string' ? argsArray[i] : String(argsArray[i]);
    const dangerousKeys = ['__proto__', 'constructor', 'prototype'];
    if (dangerousKeys.includes(arg)) {
      continue; // skip dangerous keys
    }

    if (arg === '--id' || arg === '-i') {
      options.userId = String(argsArray[++i]);
    } else if (arg === '--silent' || arg === '-s') {
      options.silent = true;
    } else if (!options.userId && !arg.startsWith('-')) {
      // Treat as positional argument if not starting with - and no userId set yet
      options.userId = arg;
    }
  }

  // Fallback to default if no ID provided
  if (!options.userId) {
    console.error(
      'Error: User ID is required. Use --help for usage information.'
    );
    process.exit(1);
  }

  return options;
}

// ------------ MAIN EXECUTION
(async () => {
  try {
    // Parse command line arguments
    const { userId, silent } = parseArguments();

    if (!silent) {
      console.log(`Fetching email for user ID: ${userId}`);
    }

    // Fetch and display the user email
    const email = await fetchUserEmail(userId);

    if (email) {
      if (silent) {
        // Output only the email in silent mode (useful for piping)
        console.log(email);
      } else {
        console.log(`User ${userId} email: ${email}`);
      }
    } else {
      if (!silent) {
        console.log(`No email found for user ${userId}`);
      }

      // Exit with code 2 for "not found"
      process.exit(2);
    }
  } catch (error) {
    console.error('Script execution failed:', error.message);

    // Exit with error code
    process.exit(1);
  }
})();
