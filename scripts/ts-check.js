/**
 * @file TS-CHECK.JS
 *
 * @version 1.0.0
 * <AUTHOR> (username)
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * This script is designed to add TypeScript checks to all JavaScript files in the project.
 * It ensures that all JavaScript files are checked for type errors, which is useful for
 * maintaining code quality and catching potential issues early.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check
/* eslint-disable security/detect-non-literal-fs-filename */

// ------------ IMPORTS
import { createReadStream, createWriteStream, promises as fs } from 'node:fs';
import { join, resolve } from 'node:path';
import { createInterface } from 'node:readline';
import { pipeline } from 'node:stream/promises';

// ------------ SECURITY VALIDATION
const ALLOWED_EXTENSIONS = new Set(['.js']);
const TS_CHECK_DIRECTIVE = '// @ts-check';

/**
 * Validates path security
 * @param {string} filePath - Path to validate
 * @param {string} basePath - Base directory
 * @returns {boolean} Is path safe
 */
function isPathSafe(filePath, basePath) {
  const resolvedPath = resolve(filePath);
  const resolvedBase = resolve(basePath);
  return resolvedPath.startsWith(resolvedBase);
}

/**
 * Adds TypeScript check directive using streams
 * @param {string} filePath - Target file path
 * @param {string} basePath - Base directory
 */
async function addTsCheckToFile(filePath, basePath) {
  if (!isPathSafe(filePath, basePath)) return;

  const readStream = createReadStream(filePath);
  const rl = createInterface({ input: readStream });

  let firstLine = '';
  for await (const line of rl) {
    firstLine = line;
    break;
  }
  rl.close();

  if (firstLine.includes(TS_CHECK_DIRECTIVE)) return;

  const originalContent = createReadStream(filePath);
  const writeStream = createWriteStream(`${filePath}.tmp`);

  writeStream.write(`${TS_CHECK_DIRECTIVE}\n`);

  await pipeline(originalContent, writeStream);
  await fs.rename(`${filePath}.tmp`, filePath);
}

/**
 * Recursively processes directory
 * @param {string} dir - Directory path
 * @param {string} basePath - Base directory
 */
async function walkDir(dir, basePath) {
  if (!isPathSafe(dir, basePath)) return;

  const entries = await fs.readdir(dir, { withFileTypes: true });

  for (const entry of entries) {
    // Only allow string file names
    if (typeof entry.name !== 'string') continue;
    const fullPath = join(dir, entry.name);

    if (!isPathSafe(fullPath, basePath)) continue;

    if (entry.isDirectory()) {
      await walkDir(fullPath, basePath);
      continue;
    }

    // Only allow .js files by strict extension check
    if (fullPath.endsWith('.js') && ALLOWED_EXTENSIONS.has('.js')) {
      await addTsCheckToFile(fullPath, basePath);
    }
  }
}

// ------------ START
const baseDirectory = process.cwd();
await walkDir(baseDirectory, baseDirectory);
