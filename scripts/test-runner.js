/**
 * @file TEST-RUNNER.JS
 *
 * @version 1.0.0
 * <AUTHOR> (username)
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * Advanced test runner script for DIS Discord Bot.
 * Provides test execution, coverage reporting, and CI/CD integration.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import { spawn } from 'node:child_process';
import { promises as fs } from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

// ------------ CONSTANTS
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.resolve(__dirname, '..');

const TEST_TYPES = {
  unit: 'tests/unit/**/*.test.js',
  integration: 'tests/integration/**/*.test.js',
  e2e: 'tests/e2e/**/*.test.js',
  all: 'tests/**/*.test.js',
};

const COVERAGE_THRESHOLDS = {
  statements: 75,
  branches: 60,
  functions: 70,
  lines: 75,
};

// ------------ HELPER FUNCTIONS

/**
 * Execute a command and return a promise
 * @param {string} command - Command to execute
 * @param {string[]} args - Command arguments
 * @param {{ verbose?: boolean; env?: NodeJS.ProcessEnv }} options - Spawn options
 * @returns {Promise<{code: number, stdout: string, stderr: string}>}
 */
function executeCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      cwd: PROJECT_ROOT,
      stdio: ['ignore', 'pipe', 'pipe'],
      env: options.env || process.env,
    });

    let stdout = '';
    let stderr = '';

    if (child.stdout) {
      child.stdout.on('data', data => {
        stdout += data.toString();
        if (options.verbose) {
          process.stdout.write(data);
        }
      });
    }
    if (child.stderr) {
      child.stderr.on('data', data => {
        stderr += data.toString();
        if (options.verbose) {
          process.stderr.write(data);
        }
      });
    }

    child.on('close', code => {
      resolve({ code: typeof code === 'number' ? code : 0, stdout, stderr });
    });

    child.on('error', error => {
      reject(error);
    });
  });
}

/**
 * Parse command line arguments
 * @param {string[]} args - Command line arguments
 * @returns {{ type: string; watch: boolean; coverage: boolean; verbose: boolean; updateSnapshots: boolean; bail: boolean; silent: boolean; pattern: string | null }} Parsed options
 */
/**
 * Parse command line arguments
 * @param {string[]} args - Command line arguments
 * @returns {{ type: keyof typeof TEST_TYPES; watch: boolean; coverage: boolean; verbose: boolean; updateSnapshots: boolean; bail: boolean; silent: boolean; pattern: string | null }} Parsed options
 */
function parseArguments(args) {
  /** @type {{ type: keyof typeof TEST_TYPES; watch: boolean; coverage: boolean; verbose: boolean; updateSnapshots: boolean; bail: boolean; silent: boolean; pattern: string | null }} */
  const options = {
    type: 'all',
    watch: false,
    coverage: true,
    verbose: false,
    updateSnapshots: false,
    bail: false,
    silent: false,
    pattern: null,
  };

  const argsArray = Array.isArray(args) ? args : Array.from(args);
  // Strict allowlist of valid flags
  const validFlags = new Set([
    '--unit',
    '--integration',
    '--e2e',
    '--watch',
    '-w',
    '--no-coverage',
    '--verbose',
    '-v',
    '--update-snapshots',
    '-u',
    '--bail',
    '--silent',
    '--pattern',
    '--help',
    '-h',
  ]);
  for (let i = 0; i < argsArray.length; i++) {
    // This is safe since we control the input, it had been validated above
    // eslint-disable-next-line security/detect-object-injection
    const rawArg = argsArray[i];
    if (
      typeof rawArg !== 'string' ||
      (!validFlags.has(rawArg) &&
        !(argsArray[i - 1] === '--pattern' && typeof rawArg === 'string'))
    ) {
      continue;
    }
    const arg = rawArg;
    switch (arg) {
      case '--unit':
        options.type = 'unit';
        break;
      case '--integration':
        options.type = 'integration';
        break;
      case '--e2e':
        options.type = 'e2e';
        break;
      case '--watch':
      case '-w':
        options.watch = true;
        break;
      case '--no-coverage':
        options.coverage = false;
        break;
      case '--verbose':
      case '-v':
        options.verbose = true;
        break;
      case '--update-snapshots':
      case '-u':
        options.updateSnapshots = true;
        break;
      case '--bail':
        options.bail = true;
        break;
      case '--silent':
        options.silent = true;
        break;
      case '--pattern': {
        // Only allow string pattern, never object
        const nextArg = argsArray[i + 1];
        options.pattern = typeof nextArg === 'string' ? nextArg : null;
        if (options.pattern !== null) i++;
        break;
      }
      case '--help':
      case '-h':
        printHelp();
        process.exit(0);
    }
  }
  return options;
}

/**
 * Print help information
 */
function printHelp() {
  console.log(`
DIS Discord Bot Test Runner

Usage: node scripts/test-runner.js [options]

Options:
  --unit              Run only unit tests
  --integration       Run only integration tests
  --e2e               Run only end-to-end tests
  --watch, -w         Run tests in watch mode
  --no-coverage       Skip coverage collection
  --verbose, -v       Verbose output
  --update-snapshots, -u  Update test snapshots
  --bail              Stop on first test failure
  --silent            Minimal output
  --pattern <pattern> Run tests matching pattern
  --help, -h          Show this help

Examples:
  node scripts/test-runner.js --unit --watch
  node scripts/test-runner.js --integration --no-coverage
  node scripts/test-runner.js --pattern "ping"
  `);
}

/**
 * Build Jest command arguments
 * @param {Object} options - Test options
 * @returns {string[]} Jest arguments
 */
function buildJestArgs(options) {
  const args = [];

  // Test pattern
  if (options.type !== 'all') {
    args.push(TEST_TYPES[options.type]);
  }

  // Coverage
  if (options.coverage) {
    args.push('--coverage');
  } else {
    args.push('--no-coverage');
  }

  // Watch mode
  if (options.watch) {
    args.push('--watch');
  }

  // Verbose output
  if (options.verbose) {
    args.push('--verbose');
  }

  // Update snapshots
  if (options.updateSnapshots) {
    args.push('--updateSnapshot');
  }

  // Bail on first failure
  if (options.bail) {
    args.push('--bail');
  }

  // Silent mode
  if (options.silent) {
    args.push('--silent');
  }

  // Pattern matching
  if (options.pattern) {
    args.push('--testNamePattern', options.pattern);
  }

  // Force exit (useful for CI)
  if (!options.watch) {
    args.push('--forceExit');
  }

  return args;
}

/**
 * Check if coverage meets thresholds
 * @returns {Promise<boolean>} Whether coverage meets thresholds
 */
async function checkCoverageThresholds() {
  try {
    // Use coverage-summary.json as primary source (Jest generates this)
    const coveragePath = path.join(
      PROJECT_ROOT,
      'coverage',
      'coverage-summary.json'
    );
    const coverageData = JSON.parse(await fs.readFile(coveragePath, 'utf8'));
    const total = coverageData.total;

    const results = {
      statements: total.statements.pct >= COVERAGE_THRESHOLDS.statements,
      branches: total.branches.pct >= COVERAGE_THRESHOLDS.branches,
      functions: total.functions.pct >= COVERAGE_THRESHOLDS.functions,
      lines: total.lines.pct >= COVERAGE_THRESHOLDS.lines,
    };

    console.log('\n📊 Coverage Report:');
    console.log(
      `  Statements: ${total.statements.pct}% (threshold: ${COVERAGE_THRESHOLDS.statements}%) ${results.statements ? '✅' : '❌'}`
    );
    console.log(
      `  Branches:   ${total.branches.pct}% (threshold: ${COVERAGE_THRESHOLDS.branches}%) ${results.branches ? '✅' : '❌'}`
    );
    console.log(
      `  Functions:  ${total.functions.pct}% (threshold: ${COVERAGE_THRESHOLDS.functions}%) ${results.functions ? '✅' : '❌'}`
    );
    console.log(
      `  Lines:      ${total.lines.pct}% (threshold: ${COVERAGE_THRESHOLDS.lines}%) ${results.lines ? '✅' : '❌'}`
    );

    return Object.values(results).every(Boolean);
  } catch (error) {
    console.warn('⚠️  Could not read coverage report:', error.message);
    return true; // Don't fail if coverage report is missing
  }
}

/**
 * Generate test report
 * @param {Object} result - Test execution result
 * @param {Object} options - Test options
 */
async function generateTestReport(result, options) {
  const timestamp = new Date().toISOString();
  const report = {
    timestamp,
    exitCode: result.code,
    testType: options.type,
    options,
    success: result.code === 0,
  };

  try {
    const reportsDir = path.join(PROJECT_ROOT, 'coverage', 'reports');
    await fs.mkdir(reportsDir, { recursive: true });

    // Use a fixed filename to avoid security issues with dynamic paths
    const reportPath = path.join(reportsDir, 'latest-test-report.json');
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

    console.log(`📄 Test report saved to: ${reportPath}`);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.warn('⚠️  Could not save test report:', errorMessage);
  }
}

// ------------ MAIN EXECUTION

/**
 * Main test runner function
 */
async function runTests() {
  const args = process.argv.slice(2);
  const options = parseArguments(args);

  console.log('🧪 DIS Discord Bot Test Runner');
  console.log(`📋 Running ${options.type} tests...`);

  if (options.coverage) {
    console.log('📊 Coverage collection enabled');
  }

  let exitCode = 0;
  try {
    // Build Jest command
    const jestArgs = buildJestArgs(options);

    console.log(`🚀 Executing: npx jest ${jestArgs.join(' ')}`);

    // Run tests with ES modules support
    const result = await executeCommand('npx', ['jest', ...jestArgs], {
      verbose: !options.silent,
      env: {
        ...process.env,
        NODE_OPTIONS: '--experimental-vm-modules',
      },
    });

    // Generate report
    await generateTestReport(result, options);

    // Check coverage if enabled
    if (options.coverage && !options.watch) {
      const coverageMet = await checkCoverageThresholds();

      // Track coverage trends
      try {
        const { addCoverageTrend } = await import(
          './coverage-trend-tracker.js'
        );
        await addCoverageTrend();
      } catch (error) {
        console.warn(
          '⚠️  Could not update coverage trends:',
          error instanceof Error ? error.message : String(error)
        );
      }

      if (!coverageMet) {
        console.log('\n❌ Coverage thresholds not met');
        exitCode = 1;
      } else {
        console.log('\n✅ All coverage thresholds met');
      }
    }

    // Exit with test result code
    if (result.code === 0 && exitCode === 0) {
      console.log('\n✅ All tests passed!');
    } else {
      console.log('\n❌ Some tests failed');
      if (result.code !== 0) exitCode = result.code;
    }
  } catch (error) {
    console.error(
      '💥 Test runner failed:',
      error instanceof Error ? error.message : String(error)
    );
    exitCode = 1;
  }
  process.exit(exitCode);
}

// Run if called directly
if (
  typeof process !== 'undefined' &&
  import.meta.url === `file://${process.argv[1]}`
) {
  // Run main function, ignore promise result intentionally
  void runTests();
}

export { buildJestArgs, parseArguments, runTests };
