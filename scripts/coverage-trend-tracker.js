/**
 * @file COVERAGE-TREND-TRACKER.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * Tracks coverage trends over time and provides historical analysis.
 * Stores coverage data with timestamps for trend analysis.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

// ------------ CONSTANTS
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.resolve(__dirname, '..');

const TREND_DATA_FILE = path.join(
  PROJECT_ROOT,
  'coverage',
  'coverage-trends.json'
);
const MAX_TREND_ENTRIES = 100; // Keep last 100 entries

// ------------ <PERSON>ELPER FUNCTIONS

/**
 * Read current coverage data
 * @returns {Promise<Object>} Coverage data
 */
async function readCurrentCoverage() {
  // Try coverage-summary.json first (more reliable)
  try {
    const summaryPath = path.join(
      PROJECT_ROOT,
      'coverage',
      'coverage-summary.json'
    );
    const data = await fs.readFile(summaryPath, 'utf8');
    const summaryData = JSON.parse(data);
    return summaryData.total;
  } catch {
    // Fallback to coverage-final.json
    const finalPath = path.join(
      PROJECT_ROOT,
      'coverage',
      'coverage-final.json'
    );
    const data = await fs.readFile(finalPath, 'utf8');
    const finalData = JSON.parse(data);

    // Calculate totals from detailed data
    let totalStatements = 0,
      coveredStatements = 0;
    let totalFunctions = 0,
      coveredFunctions = 0;
    let totalBranches = 0,
      coveredBranches = 0;
    let totalLines = 0,
      coveredLines = 0;

    Object.values(finalData).forEach(file => {
      totalStatements += file.s ? Object.keys(file.s).length : 0;
      coveredStatements += file.s
        ? Object.values(file.s).filter(v => v > 0).length
        : 0;
      totalFunctions += file.f ? Object.keys(file.f).length : 0;
      coveredFunctions += file.f
        ? Object.values(file.f).filter(v => v > 0).length
        : 0;
      totalBranches += file.b ? Object.keys(file.b).length : 0;
      coveredBranches += file.b
        ? Object.values(file.b)
            .flat()
            .filter(v => v > 0).length
        : 0;
      totalLines += file.l ? Object.keys(file.l).length : 0;
      coveredLines += file.l
        ? Object.values(file.l).filter(v => v > 0).length
        : 0;
    });

    return {
      statements: {
        total: totalStatements,
        covered: coveredStatements,
        pct:
          totalStatements > 0
            ? Math.round((coveredStatements / totalStatements) * 100)
            : 0,
      },
      branches: {
        total: totalBranches,
        covered: coveredBranches,
        pct:
          totalBranches > 0
            ? Math.round((coveredBranches / totalBranches) * 100)
            : 0,
      },
      functions: {
        total: totalFunctions,
        covered: coveredFunctions,
        pct:
          totalFunctions > 0
            ? Math.round((coveredFunctions / totalFunctions) * 100)
            : 0,
      },
      lines: {
        total: totalLines,
        covered: coveredLines,
        pct: totalLines > 0 ? Math.round((coveredLines / totalLines) * 100) : 0,
      },
    };
  }
}

/**
 * Read existing trend data
 * @returns {Promise<Array>} Trend data array
 */
async function readTrendData() {
  try {
    const data = await fs.readFile(TREND_DATA_FILE, 'utf8');
    return JSON.parse(data);
  } catch {
    // File doesn't exist yet, return empty array
    return [];
  }
}

/**
 * Save trend data
 * @param {Array} trendData - Array of trend entries
 */
async function saveTrendData(trendData) {
  // Ensure coverage directory exists
  const coverageDir = path.dirname(TREND_DATA_FILE);
  await fs.mkdir(coverageDir, { recursive: true });

  await fs.writeFile(TREND_DATA_FILE, JSON.stringify(trendData, null, 2));
}

/**
 * Add current coverage to trend data
 */
async function addCoverageTrend() {
  try {
    const currentCoverage = await readCurrentCoverage();
    const trendData = await readTrendData();

    const entry = {
      timestamp: new Date().toISOString(),
      date: new Date().toISOString().split('T')[0],
      coverage: {
        statements: currentCoverage.statements.pct,
        branches: currentCoverage.branches.pct,
        functions: currentCoverage.functions.pct,
        lines: currentCoverage.lines.pct,
      },
      totals: {
        statements: currentCoverage.statements.total,
        branches: currentCoverage.branches.total,
        functions: currentCoverage.functions.total,
        lines: currentCoverage.lines.total,
      },
    };

    // Add new entry
    trendData.push(entry);

    // Keep only the last MAX_TREND_ENTRIES
    if (trendData.length > MAX_TREND_ENTRIES) {
      trendData.splice(0, trendData.length - MAX_TREND_ENTRIES);
    }

    await saveTrendData(trendData);

    console.log('✅ Coverage trend data updated');
    console.log(
      `📊 Current coverage: ${entry.coverage.statements}% statements, ${entry.coverage.functions}% functions`
    );

    return entry;
  } catch (error) {
    console.error('❌ Failed to update coverage trend:', error.message);
    throw error;
  }
}

/**
 * Analyze coverage trends
 */
async function analyzeTrends() {
  try {
    const trendData = await readTrendData();

    if (trendData.length < 2) {
      console.log(
        '📈 Not enough data for trend analysis (need at least 2 entries)'
      );
      return;
    }

    const latest = trendData[trendData.length - 1];
    const previous = trendData[trendData.length - 2];

    console.log('\n📈 Coverage Trend Analysis:');
    console.log('='.repeat(50));

    // Use explicit property access to avoid security warnings
    const metricData = [
      {
        name: 'statements',
        current: latest.coverage.statements,
        prev: previous.coverage.statements,
      },
      {
        name: 'branches',
        current: latest.coverage.branches,
        prev: previous.coverage.branches,
      },
      {
        name: 'functions',
        current: latest.coverage.functions,
        prev: previous.coverage.functions,
      },
      {
        name: 'lines',
        current: latest.coverage.lines,
        prev: previous.coverage.lines,
      },
    ];

    metricData.forEach(({ name, current, prev }) => {
      const change = current - prev;
      const emoji = change > 0 ? '📈' : change < 0 ? '📉' : '➡️';
      const sign = change > 0 ? '+' : '';

      console.log(
        `  ${emoji} ${name.padEnd(12)}: ${current}% (${sign}${change}%)`
      );
    });

    // Show trend over last 10 entries
    if (trendData.length >= 10) {
      const last10 = trendData.slice(-10);
      const oldest = last10[0];
      const newest = last10[last10.length - 1];

      console.log('\n📊 10-Entry Trend:');
      // Use explicit property access to avoid security warnings
      const trendMetricData = [
        {
          name: 'statements',
          newest: newest.coverage.statements,
          oldest: oldest.coverage.statements,
        },
        {
          name: 'branches',
          newest: newest.coverage.branches,
          oldest: oldest.coverage.branches,
        },
        {
          name: 'functions',
          newest: newest.coverage.functions,
          oldest: oldest.coverage.functions,
        },
        {
          name: 'lines',
          newest: newest.coverage.lines,
          oldest: oldest.coverage.lines,
        },
      ];

      trendMetricData.forEach(
        ({ name, newest: newestValue, oldest: oldestValue }) => {
          const change = newestValue - oldestValue;
          const emoji = change > 0 ? '📈' : change < 0 ? '📉' : '➡️';
          const sign = change > 0 ? '+' : '';

          console.log(
            `  ${emoji} ${name.padEnd(12)}: ${sign}${change}% over 10 runs`
          );
        }
      );
    }
  } catch (error) {
    console.error('❌ Failed to analyze trends:', error.message);
  }
}

// ------------ MAIN EXECUTION

/**
 * Main function
 */
async function main() {
  const command = process.argv[2];

  switch (command) {
    case 'add':
      await addCoverageTrend();
      break;
    case 'analyze':
      await analyzeTrends();
      break;
    case 'both':
      await addCoverageTrend();
      await analyzeTrends();
      break;
    default:
      console.log('Usage: node coverage-trend-tracker.js [add|analyze|both]');
      console.log('  add     - Add current coverage to trend data');
      console.log('  analyze - Analyze coverage trends');
      console.log('  both    - Add current coverage and analyze trends');
      process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('💥 Coverage trend tracking failed:', error.message);
    process.exit(1);
  });
}

export { addCoverageTrend, analyzeTrends };
