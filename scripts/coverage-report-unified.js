/**
 * @file COVERAGE-REPORT-UNIFIED.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Unified coverage reporting script that handles both Jest summary and detailed coverage data.
 * Provides consistent coverage reporting across all scripts.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

// ------------ CONSTANTS
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.resolve(__dirname, '..');

// ------------ COVERAGE DATA READER

/**
 * Read coverage data with fallback support
 * @returns {Promise<Object>} Coverage data in unified format
 */
async function readCoverageData() {
  let coverageData = null;
  let dataSource = null;

  // Try coverage-summary.json first (Jest generates this reliably)
  try {
    const summaryPath = path.join(
      PROJECT_ROOT,
      'coverage',
      'coverage-summary.json'
    );
    const summaryData = JSON.parse(await fs.readFile(summaryPath, 'utf8'));

    coverageData = {
      total: summaryData.total,
      files: {},
    };

    // Convert summary format to unified format using Map to avoid injection
    const summaryEntries = Object.entries(summaryData).filter(
      ([key]) => key !== 'total'
    );
    const summaryMap = new Map(summaryEntries);

    for (const [filePath, fileData] of summaryMap) {
      const filesMap = new Map(Object.entries(coverageData.files));
      filesMap.set(filePath, fileData);
      coverageData.files = Object.fromEntries(filesMap);
    }

    dataSource = 'coverage-summary.json';
    console.log('📁 Reading coverage from:', summaryPath);
  } catch (summaryError) {
    // Fallback to coverage-final.json
    try {
      const finalPath = path.join(
        PROJECT_ROOT,
        'coverage',
        'coverage-final.json'
      );
      const finalData = JSON.parse(await fs.readFile(finalPath, 'utf8'));

      // Convert detailed format to unified format
      coverageData = {
        total: calculateTotalFromDetailed(finalData),
        files: {},
      };

      // Use Map to avoid object injection warnings
      const finalEntries = Object.entries(finalData);
      const finalMap = new Map(finalEntries);

      for (const [filePath, fileData] of finalMap) {
        const convertedData = convertDetailedToSummary(fileData);
        const filesMap = new Map(Object.entries(coverageData.files));
        filesMap.set(filePath, convertedData);
        coverageData.files = Object.fromEntries(filesMap);
      }

      dataSource = 'coverage-final.json';
      console.log('📁 Reading coverage from:', finalPath);
    } catch (finalError) {
      throw new Error(
        `Could not read coverage data from either source:\n` +
          `Summary error: ${summaryError.message}\n` +
          `Final error: ${finalError.message}`
      );
    }
  }

  return { data: coverageData, source: dataSource };
}

/**
 * Calculate total coverage from detailed coverage data
 * @param {Object} detailedData - Detailed coverage data
 * @returns {Object} Total coverage summary
 */
function calculateTotalFromDetailed(detailedData) {
  let totalStatements = 0,
    coveredStatements = 0;
  let totalBranches = 0,
    coveredBranches = 0;
  let totalFunctions = 0,
    coveredFunctions = 0;
  let totalLines = 0,
    coveredLines = 0;

  Object.values(detailedData).forEach(fileData => {
    // Count statements
    const statements = Object.values(fileData.s || {});
    totalStatements += statements.length;
    coveredStatements += statements.filter(count => count > 0).length;

    // Count branches
    const branches = Object.values(fileData.b || {}).flat();
    totalBranches += branches.length;
    coveredBranches += branches.filter(count => count > 0).length;

    // Count functions
    const functions = Object.values(fileData.f || {});
    totalFunctions += functions.length;
    coveredFunctions += functions.filter(count => count > 0).length;

    // Lines (use statements as proxy)
    totalLines += statements.length;
    coveredLines += statements.filter(count => count > 0).length;
  });

  return {
    statements: {
      total: totalStatements,
      covered: coveredStatements,
      pct:
        totalStatements > 0
          ? Math.round((coveredStatements / totalStatements) * 100)
          : 0,
    },
    branches: {
      total: totalBranches,
      covered: coveredBranches,
      pct:
        totalBranches > 0
          ? Math.round((coveredBranches / totalBranches) * 100)
          : 0,
    },
    functions: {
      total: totalFunctions,
      covered: coveredFunctions,
      pct:
        totalFunctions > 0
          ? Math.round((coveredFunctions / totalFunctions) * 100)
          : 0,
    },
    lines: {
      total: totalLines,
      covered: coveredLines,
      pct: totalLines > 0 ? Math.round((coveredLines / totalLines) * 100) : 0,
    },
  };
}

/**
 * Convert detailed file coverage to summary format
 * @param {Object} fileData - Detailed file coverage data
 * @returns {Object} Summary format coverage data
 */
function convertDetailedToSummary(fileData) {
  const statements = Object.values(fileData.s || {});
  const branches = Object.values(fileData.b || {}).flat();
  const functions = Object.values(fileData.f || {});

  return {
    statements: {
      total: statements.length,
      covered: statements.filter(count => count > 0).length,
      pct:
        statements.length > 0
          ? Math.round(
              (statements.filter(count => count > 0).length /
                statements.length) *
                100
            )
          : 0,
    },
    branches: {
      total: branches.length,
      covered: branches.filter(count => count > 0).length,
      pct:
        branches.length > 0
          ? Math.round(
              (branches.filter(count => count > 0).length / branches.length) *
                100
            )
          : 0,
    },
    functions: {
      total: functions.length,
      covered: functions.filter(count => count > 0).length,
      pct:
        functions.length > 0
          ? Math.round(
              (functions.filter(count => count > 0).length / functions.length) *
                100
            )
          : 0,
    },
    lines: {
      total: statements.length,
      covered: statements.filter(count => count > 0).length,
      pct:
        statements.length > 0
          ? Math.round(
              (statements.filter(count => count > 0).length /
                statements.length) *
                100
            )
          : 0,
    },
  };
}

// ------------ REPORT GENERATION

/**
 * Generate unified coverage report
 * @returns {Promise<void>}
 */
async function generateUnifiedReport() {
  try {
    console.log('🔍 Generating unified coverage report...');
    console.log('📁 Project root:', PROJECT_ROOT);

    const { data: coverageData, source } = await readCoverageData();

    console.log(`📊 Data source: ${source}`);
    console.log(
      `📁 Found coverage data for ${Object.keys(coverageData.files).length} files\n`
    );

    // Display overall coverage
    const total = coverageData.total;
    console.log('📈 Overall Coverage Summary:');
    console.log(
      `  Statements: ${total.statements.pct}% (${total.statements.covered}/${total.statements.total})`
    );
    console.log(
      `  Branches:   ${total.branches.pct}% (${total.branches.covered}/${total.branches.total})`
    );
    console.log(
      `  Functions:  ${total.functions.pct}% (${total.functions.covered}/${total.functions.total})`
    );
    console.log(
      `  Lines:      ${total.lines.pct}% (${total.lines.covered}/${total.lines.total})\n`
    );

    // Group files by directory using Map to avoid injection
    const filesByDirMap = new Map();
    const fileEntries = Object.entries(coverageData.files);

    for (const [filePath, fileData] of fileEntries) {
      const relativePath = filePath.includes('src/')
        ? filePath.substring(filePath.indexOf('src/'))
        : path.relative(PROJECT_ROOT, filePath);

      const dir = path.dirname(relativePath);

      if (!filesByDirMap.has(dir)) {
        filesByDirMap.set(dir, []);
      }

      const dirFiles = filesByDirMap.get(dir);
      dirFiles.push({
        path: relativePath,
        coverage: fileData,
      });
      filesByDirMap.set(dir, dirFiles);
    }

    const filesByDir = Object.fromEntries(filesByDirMap);

    // Display coverage by directory using Map entries to avoid injection
    console.log('📂 Coverage by Directory:');
    const dirEntries = Object.entries(filesByDir);
    const sortedDirs = dirEntries.sort(([a], [b]) => a.localeCompare(b));

    for (const [dir, files] of sortedDirs) {
      const avgStatements = Math.round(
        files.reduce(
          /** @param {number} sum @param {{ coverage: { statements: { pct: number } } }} file */
          (sum, file) => sum + file.coverage.statements.pct,
          0
        ) / files.length
      );

      console.log(`\n  ${dir}/ (${avgStatements}% avg statements)`);

      files
        .sort(
          /** @param {{ coverage: { statements: { pct: number } } }} a @param {{ coverage: { statements: { pct: number } } }} b */
          (a, b) => a.coverage.statements.pct - b.coverage.statements.pct
        )
        .forEach(
          /** @param {{ coverage: any, path: string }} file */
          file => {
            const coverage = file.coverage;
            const status =
              coverage.statements.pct >= 80
                ? '✅'
                : coverage.statements.pct >= 60
                  ? '⚠️'
                  : '❌';
            console.log(
              `    ${status} ${path.basename(file.path)}: ${coverage.statements.pct}%`
            );
          }
        );
    }

    // Save unified report
    const reportPath = path.join(
      PROJECT_ROOT,
      'coverage',
      'unified-report.json'
    );
    await fs.writeFile(
      reportPath,
      JSON.stringify(
        {
          timestamp: new Date().toISOString(),
          source,
          total: coverageData.total,
          files: coverageData.files,
          summary: {
            totalFiles: Object.keys(coverageData.files).length,
            excellentFiles: Object.values(coverageData.files).filter(
              f => f.statements.pct >= 90
            ).length,
            goodFiles: Object.values(coverageData.files).filter(
              f => f.statements.pct >= 80 && f.statements.pct < 90
            ).length,
            needsWork: Object.values(coverageData.files).filter(
              f => f.statements.pct < 80
            ).length,
          },
        },
        null,
        2
      )
    );

    console.log(`\n💾 Unified report saved to: ${reportPath}`);
    console.log('✅ Coverage report generation complete!');
  } catch (error) {
    console.error('❌ Error generating coverage report:', error.message);
    process.exit(1);
  }
}

// ------------ MAIN EXECUTION
if (import.meta.url === `file://${process.argv[1]}`) {
  generateUnifiedReport().catch(error => {
    console.error('Script failed:', error);
    process.exit(1);
  });
}

export { generateUnifiedReport, readCoverageData };
