/**
 * Simple Coverage Report Generator
 * Reads Jest coverage data and generates a readable report
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.resolve(__dirname, '..');

/**
 * Read coverage data from available sources
 * @returns {Promise<Record<string, any>>} Coverage data
 */
async function readCoverageData() {
  let coverageData;

  // Try coverage-summary.json first (Jest generates this reliably)
  try {
    const summaryPath = path.join(
      PROJECT_ROOT,
      'coverage',
      'coverage-summary.json'
    );
    const data = await fs.readFile(summaryPath, 'utf8');
    const summaryData = JSON.parse(data);
    console.log('📁 Reading summary coverage from:', summaryPath);

    // Convert summary format to detailed format for compatibility
    coverageData = {};

    // Process known file paths from summary data using Map to avoid injection
    const summaryEntries = Object.entries(summaryData).filter(
      ([key]) => key !== 'total'
    );
    const fileDataMap = new Map(summaryEntries);

    for (const [filePath, fileData] of fileDataMap) {
      if (fileData && typeof fileData === 'object') {
        const convertedData = {};

        // Use explicit property access to avoid security warnings
        if (fileData.statements && typeof fileData.statements === 'object') {
          convertedData.statements = fileData.statements;
        }
        if (fileData.branches && typeof fileData.branches === 'object') {
          convertedData.branches = fileData.branches;
        }
        if (fileData.functions && typeof fileData.functions === 'object') {
          convertedData.functions = fileData.functions;
        }
        if (fileData.lines && typeof fileData.lines === 'object') {
          convertedData.lines = fileData.lines;
        }

        // Use Map to avoid injection warnings
        if (Object.keys(convertedData).length > 0) {
          const coverageMap = new Map(Object.entries(coverageData));
          coverageMap.set(filePath, convertedData);
          coverageData = Object.fromEntries(coverageMap);
        }
      }
    }
  } catch {
    // Fallback to coverage-final.json (detailed data)
    try {
      const finalPath = path.join(
        PROJECT_ROOT,
        'coverage',
        'coverage-final.json'
      );
      const data = await fs.readFile(finalPath, 'utf8');
      coverageData = JSON.parse(data);
      console.log('📁 Reading detailed coverage from:', finalPath);
    } catch {
      throw new Error('Could not read coverage data from either source');
    }
  }

  console.log(
    '📊 Found coverage data for',
    Object.keys(coverageData).length,
    'files\n'
  );

  return coverageData;
}

/**
 * Analyze coverage data for all files
 * @param {Record<string, any>} coverageData - Coverage data
 * @returns {{ files: Array<any>; totals: { statements: number; coveredStatements: number; functions: number; coveredFunctions: number; branches: number; coveredBranches: number } }}
 */
function analyzeFiles(coverageData) {
  const files = [];
  let totalStatements = 0;
  let coveredStatements = 0;
  let totalFunctions = 0;
  let coveredFunctions = 0;
  let totalBranches = 0;
  let coveredBranches = 0;

  for (const [filePath, fileData] of Object.entries(coverageData)) {
    // Calculate coverage for this file
    const statements = fileData.s || {};
    const functions = fileData.f || {};
    const branches = fileData.b || {};

    const fileStatements = Object.keys(statements).length;
    const fileCoveredStatements = Object.values(statements).filter(
      v => v > 0
    ).length;
    const fileFunctions = Object.keys(functions).length;
    const fileCoveredFunctions = Object.values(functions).filter(
      v => v > 0
    ).length;
    const fileBranches = Object.values(branches).flat().length;
    const fileCoveredBranches = Object.values(branches)
      .flat()
      .filter(v => v > 0).length;

    const statementPercent =
      fileStatements > 0
        ? Math.round((fileCoveredStatements / fileStatements) * 100)
        : 100;
    const functionPercent =
      fileFunctions > 0
        ? Math.round((fileCoveredFunctions / fileFunctions) * 100)
        : 100;
    const branchPercent =
      fileBranches > 0
        ? Math.round((fileCoveredBranches / fileBranches) * 100)
        : 100;

    // Get clean file name
    const fileName = filePath.includes('src/')
      ? filePath.substring(filePath.indexOf('src/'))
      : path.basename(filePath);

    // Categorize file
    let category = 'other';
    if (fileName.includes('commands')) category = 'commands';
    else if (fileName.includes('utils')) category = 'utils';
    else if (fileName.includes('models')) category = 'models';
    else if (fileName.includes('events')) category = 'events';

    files.push({
      name: fileName,
      category,
      statements: statementPercent,
      functions: functionPercent,
      branches: branchPercent,
      totals: {
        statements: fileStatements,
        coveredStatements: fileCoveredStatements,
        functions: fileFunctions,
        coveredFunctions: fileCoveredFunctions,
        branches: fileBranches,
        coveredBranches: fileCoveredBranches,
      },
    });

    // Add to totals
    totalStatements += fileStatements;
    coveredStatements += fileCoveredStatements;
    totalFunctions += fileFunctions;
    coveredFunctions += fileCoveredFunctions;
    totalBranches += fileBranches;
    coveredBranches += fileCoveredBranches;
  }

  return {
    files,
    totals: {
      statements: totalStatements,
      coveredStatements,
      functions: totalFunctions,
      coveredFunctions,
      branches: totalBranches,
      coveredBranches,
    },
  };
}

/**
 * Print overall coverage summary
 * @param {{ statements: number; coveredStatements: number; functions: number; coveredFunctions: number; branches: number; coveredBranches: number }} totals
 */
function printOverallSummary(totals) {
  const {
    statements: totalStatements,
    coveredStatements,
    functions: totalFunctions,
    coveredFunctions,
    branches: totalBranches,
    coveredBranches,
  } = totals;

  // Calculate overall coverage
  const overallStatements =
    totalStatements > 0
      ? Math.round((coveredStatements / totalStatements) * 100)
      : 0;
  const overallFunctions =
    totalFunctions > 0
      ? Math.round((coveredFunctions / totalFunctions) * 100)
      : 0;
  const overallBranches =
    totalBranches > 0 ? Math.round((coveredBranches / totalBranches) * 100) : 0;

  // Print overall summary
  console.log('📈 Overall Coverage Summary:');
  console.log(
    `  📝 Statements: ${overallStatements}% (${coveredStatements}/${totalStatements})`
  );
  console.log(
    `  🔧 Functions:  ${overallFunctions}% (${coveredFunctions}/${totalFunctions})`
  );
  console.log(
    `  🌿 Branches:   ${overallBranches}% (${coveredBranches}/${totalBranches})`
  );

  // Categorize coverage level
  let overallLevel = 'Critical';
  if (overallStatements >= 90) overallLevel = 'Excellent';
  else if (overallStatements >= 80) overallLevel = 'Good';
  else if (overallStatements >= 70) overallLevel = 'Acceptable';
  else if (overallStatements >= 50) overallLevel = 'Poor';

  const levelEmoji =
    overallLevel === 'Excellent'
      ? '🟢'
      : overallLevel === 'Good'
        ? '🔵'
        : overallLevel === 'Acceptable'
          ? '🟡'
          : overallLevel === 'Poor'
            ? '🟠'
            : '🔴';

  console.log(`  ${levelEmoji} Overall Level: ${overallLevel}\n`);

  return { overallStatements, overallBranches };
}

/**
 * Print coverage by category
 * @param {Array<any>} files - File analysis results
 */
function printCoverageByCategory(files) {
  // Group by category
  const categories = {};
  files.forEach(file => {
    if (!categories[file.category]) {
      categories[file.category] = [];
    }
    categories[file.category].push(file);
  });

  console.log('📂 Coverage by Category:');
  for (const [category, categoryFiles] of Object.entries(categories)) {
    const avgCoverage = Math.round(
      categoryFiles.reduce(
        /** @param {number} sum @param {{ statements: number }} f */
        (sum, f) => sum + f.statements,
        0
      ) / categoryFiles.length
    );
    const emoji =
      category === 'commands'
        ? '⚡'
        : category === 'utils'
          ? '🔧'
          : category === 'models'
            ? '📊'
            : category === 'events'
              ? '🎯'
              : '📁';
    console.log(
      `  ${emoji} ${category}: ${avgCoverage}% (${categoryFiles.length} files)`
    );
  }
}

/**
 * Print files needing attention
 * @param {Array<any>} files - File analysis results
 */
function printFilesNeedingAttention(files) {
  console.log('\n🎯 Files Needing Attention (< 70% coverage):');
  const poorFiles = files
    .filter(f => f.statements < 70)
    .sort((a, b) => a.statements - b.statements);

  if (poorFiles.length === 0) {
    console.log('  🎉 All files have good coverage!');
  } else {
    poorFiles.slice(0, 10).forEach(file => {
      const emoji =
        file.statements === 0 ? '🔴' : file.statements < 30 ? '🟠' : '🟡';
      console.log(`  ${emoji} ${file.name}: ${file.statements}% statements`);
    });

    if (poorFiles.length > 10) {
      console.log(`  ... and ${poorFiles.length - 10} more files`);
    }
  }
}

/**
 * Print files with excellent coverage
 * @param {Array<any>} files - File analysis results
 */
function printExcellentFiles(files) {
  console.log('\n🌟 Files with Excellent Coverage (≥ 90%):');
  const excellentFiles = files.filter(f => f.statements >= 90);

  if (excellentFiles.length === 0) {
    console.log('  📈 No files with excellent coverage yet');
  } else {
    excellentFiles.slice(0, 10).forEach(file => {
      console.log(`  🟢 ${file.name}: ${file.statements}%`);
    });

    if (excellentFiles.length > 10) {
      console.log(`  ... and ${excellentFiles.length - 10} more files`);
    }
  }
}

/**
 * Print recommendations based on coverage data
 * @param {number} overallStatements - Overall statement coverage percentage
 * @param {number} overallBranches - Overall branch coverage percentage
 * @param {Array<any>} files - File analysis results
 */
function printRecommendations(overallStatements, overallBranches, files) {
  const poorFiles = files.filter(f => f.statements < 70);

  console.log('\n💡 Recommendations:');

  if (overallStatements < 70) {
    console.log('  🔴 HIGH PRIORITY: Overall coverage is below 70%');
    console.log('     - Focus on adding basic tests for core functionality');
    console.log(
      '     - Start with commands and utils (most critical components)'
    );
  }

  if (poorFiles.length > 0) {
    console.log(
      `  🟡 MEDIUM PRIORITY: ${poorFiles.length} files have poor coverage`
    );
    console.log('     - Add unit tests for these files');
    console.log('     - Focus on testing main functions and error paths');
  }

  if (overallBranches < 60) {
    console.log('  🟠 MEDIUM PRIORITY: Branch coverage is low');
    console.log('     - Add tests for error conditions and edge cases');
    console.log('     - Test both success and failure scenarios');
  }

  if (overallStatements >= 80) {
    console.log('  🟢 GOOD: Coverage is at a good level');
    console.log('     - Consider adding integration tests');
    console.log('     - Focus on edge cases and error handling');
  }

  console.log('\n' + '='.repeat(60));
  console.log('📄 Full HTML report: coverage/index.html');
  console.log('🌐 Open in browser for detailed line-by-line coverage');
  console.log('='.repeat(60));
}

async function generateReport() {
  try {
    console.log('🧪 DIS Discord Bot - Test Coverage Report');
    console.log('='.repeat(60));

    const coverageData = await readCoverageData();
    const { files, totals } = analyzeFiles(coverageData);

    const { overallStatements, overallBranches } = printOverallSummary(totals);

    printCoverageByCategory(files);
    printFilesNeedingAttention(files);
    printExcellentFiles(files);
    printRecommendations(overallStatements, overallBranches, files);
  } catch (error) {
    console.error('❌ Error generating coverage report:', error.message);
    console.error('Make sure you have run: npm run test:coverage');
  }
}

// Run the report
generateReport();
