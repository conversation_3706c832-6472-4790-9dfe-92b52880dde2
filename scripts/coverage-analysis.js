/**
 * @file COVERAGE-ANALYSIS.JS
 *
 * @version 1.0.1
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Advanced coverage analysis script for DIS Discord Bot.
 * Analyzes test coverage data and generates detailed reports with recommendations.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------ IMPORTS
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

// ------------ CONSTANTS
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const PROJECT_ROOT = path.resolve(__dirname, '..');

const COVERAGE_THRESHOLDS = {
  excellent: { statements: 90, branches: 85, functions: 90, lines: 90 },
  good: { statements: 80, branches: 70, functions: 80, lines: 80 },
  acceptable: { statements: 70, branches: 60, functions: 70, lines: 70 },
  poor: { statements: 50, branches: 40, functions: 50, lines: 50 },
};

const PRIORITY_AREAS = {
  critical: ['commands', 'utils', 'models'],
  important: ['middleware', 'services'],
  optional: ['events'],
};

// ------------ HELPER FUNCTIONS

/**
 * Read coverage data from JSON file
 * @returns {Promise<Record<string, any>>} Coverage data
 */
async function readCoverageData() {
  // Try coverage-final.json first (detailed data), then fallback to coverage-summary.json
  try {
    const coveragePath = path.join(
      PROJECT_ROOT,
      'coverage',
      'coverage-final.json'
    );
    const data = await fs.readFile(coveragePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    // Fallback to coverage-summary.json and convert format
    try {
      const summaryPath = path.join(
        PROJECT_ROOT,
        'coverage',
        'coverage-summary.json'
      );
      const summaryData = JSON.parse(await fs.readFile(summaryPath, 'utf8'));

      // Convert summary format to detailed format for compatibility
      const detailedData = {};
      Object.keys(summaryData).forEach(key => {
        if (key !== 'total') {
          // Create mock detailed structure from summary data
          // eslint-disable-next-line security/detect-object-injection
          const summary = summaryData[key];
          // eslint-disable-next-line security/detect-object-injection
          detailedData[key] = {
            s: createMockStatements(summary.statements),
            b: createMockBranches(summary.branches),
            f: createMockFunctions(summary.functions),
            statementMap: {},
            branchMap: {},
            fnMap: {},
          };
        }
      });

      return detailedData;
    } catch {
      throw new Error(
        `Failed to read coverage data from either source: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }
}

/**
 * Create mock statements object from summary data
 * @param {{ total: number; covered: number }} statements - Summary statements data
 * @returns {Record<string, number>} Mock statements object
 */
function createMockStatements(statements) {
  /** @type {Record<string, number>} */
  const result = {};
  for (let i = 0; i < statements.total; i++) {
    // eslint-disable-next-line security/detect-object-injection
    result[i] = i < statements.covered ? 1 : 0;
  }
  return result;
}

/**
 * Create mock branches object from summary data
 * @param {{ total: number; covered: number }} branches - Summary branches data
 * @returns {Record<string, number[]>} Mock branches object
 */
function createMockBranches(branches) {
  /** @type {Record<string, number[]>} */
  const result = {};
  for (let i = 0; i < branches.total; i++) {
    // eslint-disable-next-line security/detect-object-injection
    result[i] = [i < branches.covered ? 1 : 0, 0];
  }
  return result;
}

/**
 * Create mock functions object from summary data
 * @param {{ total: number; covered: number }} functions - Summary functions data
 * @returns {Record<string, number>} Mock functions object
 */
function createMockFunctions(functions) {
  /** @type {Record<string, number>} */
  const result = {};
  for (let i = 0; i < functions.total; i++) {
    // eslint-disable-next-line security/detect-object-injection
    result[i] = i < functions.covered ? 1 : 0;
  }
  return result;
}

/**
 * Calculate coverage percentage for a file
 * @param {{ s: Record<string, number>; b: Record<string, number[]>; f: Record<string, number> }} fileData - Coverage data for a file
 * @returns {{ statements: number; branches: number; functions: number; lines: number; totals: { statements: { covered: number; total: number }; branches: { covered: number; total: number }; functions: { covered: number; total: number }; lines: { covered: number; total: number } } }} Coverage percentages
 */
function calculateCoverage(fileData) {
  const statements = fileData.s;
  const branches = fileData.b;
  const functions = fileData.f;

  /** @param {number} covered @param {number} total @returns {number} */
  const calculatePercentage = (covered, total) => {
    if (total === 0) return 100;
    return Math.round((covered / total) * 100);
  };

  const statementsCovered = Object.values(statements).filter(v => v > 0).length;
  const statementsTotal = Object.keys(statements).length;

  const branchesCovered = Object.values(branches)
    .flat()
    .filter(v => v > 0).length;
  const branchesTotal = Object.values(branches).flat().length;

  const functionsCovered = Object.values(functions).filter(v => v > 0).length;
  const functionsTotal = Object.keys(functions).length;

  return {
    statements: calculatePercentage(statementsCovered, statementsTotal),
    branches: calculatePercentage(branchesCovered, branchesTotal),
    functions: calculatePercentage(functionsCovered, functionsTotal),
    lines: calculatePercentage(statementsCovered, statementsTotal),
    totals: {
      statements: { covered: statementsCovered, total: statementsTotal },
      branches: { covered: branchesCovered, total: branchesTotal },
      functions: { covered: functionsCovered, total: functionsTotal },
      lines: { covered: statementsCovered, total: statementsTotal },
    },
  };
}

/**
 * Categorize coverage level
 * @param {number} percentage - Coverage percentage
 * @returns {string} Coverage level
 */
function categorizeCoverage(percentage) {
  if (percentage >= 90) return 'excellent';
  if (percentage >= 80) return 'good';
  if (percentage >= 70) return 'acceptable';
  if (percentage >= 50) return 'poor';
  return 'critical';
}

/**
 * Get file category based on path
 * @param {string} filePath - File path
 * @returns {string} File category
 */
function getFileCategory(filePath) {
  // Handle both absolute and relative paths
  const normalizedPath = filePath.includes('src/')
    ? filePath.substring(filePath.indexOf('src/'))
    : path.relative(PROJECT_ROOT, filePath);

  if (normalizedPath.includes('commands')) return 'commands';
  if (normalizedPath.includes('utils')) return 'utils';
  if (normalizedPath.includes('models')) return 'models';
  if (normalizedPath.includes('middleware')) return 'middleware';
  if (normalizedPath.includes('services')) return 'services';
  if (normalizedPath.includes('events')) return 'events';

  return 'other';
}

/**
 * @typedef {Object} FileAnalysis
 * @property {string} path - File path
 * @property {string} name - File name
 * @property {string} category - File category
 * @property {{ statements: number; branches: number; functions: number; lines: number }} coverage - Coverage data
 * @property {string} level - Coverage level
 */

/**
 * @typedef {Object} Recommendation
 * @property {string} priority - Priority level
 * @property {string} type - Recommendation type
 * @property {string} description - Description
 * @property {string[]} files - Affected files
 * @property {string} action - Recommended action
 */

/**
 * Generate coverage recommendations
 * @param {{ files: FileAnalysis[] }} analysis - Coverage analysis data
 * @returns {Recommendation[]} Recommendations
 */
function generateRecommendations(analysis) {
  /** @type {Recommendation[]} */
  const recommendations = [];

  // Critical files with poor coverage
  const criticalFiles = analysis.files.filter(
    file =>
      PRIORITY_AREAS.critical.includes(file.category) &&
      file.coverage.statements < COVERAGE_THRESHOLDS.acceptable.statements
  );

  if (criticalFiles.length > 0) {
    recommendations.push({
      priority: 'HIGH',
      type: 'Critical Coverage',
      description: `${criticalFiles.length} critical files have poor test coverage`,
      files: criticalFiles.map(f => f.name),
      action: 'Add comprehensive unit tests for these core components',
    });
  }

  // Untested functions
  const untestedFunctions = analysis.files.filter(
    file => file.coverage.functions === 0
  );

  if (untestedFunctions.length > 0) {
    recommendations.push({
      priority: 'HIGH',
      type: 'Untested Functions',
      description: `${untestedFunctions.length} files have no function coverage`,
      files: untestedFunctions.map(f => f.name),
      action:
        'Create basic function tests to ensure code executes without errors',
    });
  }

  // Branch coverage gaps
  const poorBranchCoverage = analysis.files.filter(
    file => file.coverage.branches < 60 && file.coverage.statements > 50
  );

  if (poorBranchCoverage.length > 0) {
    recommendations.push({
      priority: 'MEDIUM',
      type: 'Branch Coverage',
      description: `${poorBranchCoverage.length} files have poor branch coverage`,
      files: poorBranchCoverage.map(f => f.name),
      action:
        'Add tests for error conditions, edge cases, and alternative code paths',
    });
  }

  return recommendations;
}

/**
 * Generate detailed coverage report
 * @param {Record<string, any>} coverageData - Raw coverage data
 * @returns {{ timestamp: string; overall: { statements: number; branches: number; functions: number; lines: number }; overallLevel: string; files: FileAnalysis[]; byCategory: Record<string, { files: number; avgCoverage: number; level: string }>; summary: { totalFiles: number; excellentFiles: number; goodFiles: number; acceptableFiles: number; poorFiles: number; criticalFiles: number }; recommendations: Recommendation[] }} Analysis report
 */
function analyzeCoverage(coverageData) {
  /** @type {FileAnalysis[]} */
  const files = [];
  let totalStatements = 0;
  let totalBranches = 0;
  let totalFunctions = 0;
  let totalLines = 0;
  let coveredStatements = 0;
  let coveredBranches = 0;
  let coveredFunctions = 0;
  let coveredLines = 0;

  // Analyze each file
  for (const entry of Object.entries(coverageData)) {
    // Validate entry is [string, object]
    if (
      !Array.isArray(entry) ||
      typeof entry[0] !== 'string' ||
      typeof entry[1] !== 'object' ||
      entry[1] === null
    ) {
      continue;
    }
    const filePath = entry[0];
    const fileData = entry[1];
    const coverage = calculateCoverage(fileData);
    const category = getFileCategory(filePath);
    // Create a clean relative path for display
    const name = filePath.includes('src/')
      ? filePath.substring(filePath.indexOf('src/'))
      : path.relative(PROJECT_ROOT, filePath);

    files.push({
      path: filePath,
      name,
      category,
      coverage,
      level: categorizeCoverage(coverage.statements),
    });

    // Accumulate totals
    totalStatements += coverage.totals.statements.total;
    totalBranches += coverage.totals.branches.total;
    totalFunctions += coverage.totals.functions.total;
    totalLines += coverage.totals.lines.total;

    coveredStatements += coverage.totals.statements.covered;
    coveredBranches += coverage.totals.branches.covered;
    coveredFunctions += coverage.totals.functions.covered;
    coveredLines += coverage.totals.lines.covered;
  }

  // Calculate overall coverage
  const overall = {
    statements: Math.round((coveredStatements / totalStatements) * 100),
    branches: Math.round((coveredBranches / totalBranches) * 100),
    functions: Math.round((coveredFunctions / totalFunctions) * 100),
    lines: Math.round((coveredLines / totalLines) * 100),
  };

  // Group by category
  /** @type {Record<string, { files: number; avgCoverage: number; level: string }>} */
  const byCategory = {};
  for (const category of Object.keys(PRIORITY_AREAS).concat(['other'])) {
    const categoryFiles = files.filter(f => f.category === category);
    if (categoryFiles.length > 0) {
      const avgStatements = Math.round(
        categoryFiles.reduce((sum, f) => sum + f.coverage.statements, 0) /
          categoryFiles.length
      );
      // Only allow assignment for known categories
      const allowedCategories = Object.keys(PRIORITY_AREAS).concat(['other']);
      if (
        typeof category === 'string' &&
        allowedCategories.includes(category)
      ) {
        Object.defineProperty(byCategory, category, {
          value: {
            files: categoryFiles.length,
            avgCoverage: avgStatements,
            level: categorizeCoverage(avgStatements),
          },
          enumerable: true,
          configurable: true,
          writable: true,
        });
      }
    }
  }

  const analysis = {
    timestamp: new Date().toISOString(),
    overall,
    overallLevel: categorizeCoverage(overall.statements),
    files: files.sort((a, b) => a.coverage.statements - b.coverage.statements),
    byCategory,
    summary: {
      totalFiles: files.length,
      excellentFiles: files.filter(f => f.level === 'excellent').length,
      goodFiles: files.filter(f => f.level === 'good').length,
      acceptableFiles: files.filter(f => f.level === 'acceptable').length,
      poorFiles: files.filter(f => f.level === 'poor').length,
      criticalFiles: files.filter(f => f.level === 'critical').length,
    },
    recommendations: generateRecommendations({ files }),
  };

  return analysis;
}

/**
 * Print coverage report to console
 * @param {{ overall: { statements: number; branches: number; functions: number; lines: number }; overallLevel: string; summary: { totalFiles: number; excellentFiles: number; goodFiles: number; acceptableFiles: number; poorFiles: number; criticalFiles: number }; byCategory: Record<string, { files: number; avgCoverage: number; level: string }>; recommendations: Recommendation[]; files: FileAnalysis[] }} analysis - Coverage analysis
 */
function printReport(analysis) {
  console.log('\n🧪 DIS Discord Bot - Test Coverage Analysis');
  console.log('='.repeat(60));

  // Overall coverage
  console.log('\n📊 Overall Coverage:');
  console.log(
    `  Statements: ${analysis.overall.statements}% (${analysis.overallLevel})`
  );
  console.log(`  Branches:   ${analysis.overall.branches}%`);
  console.log(`  Functions:  ${analysis.overall.functions}%`);
  console.log(`  Lines:      ${analysis.overall.lines}%`);

  // Summary
  console.log('\n📈 Coverage Summary:');
  console.log(`  Total Files: ${analysis.summary.totalFiles}`);
  console.log(`  🟢 Excellent (90%+): ${analysis.summary.excellentFiles}`);
  console.log(`  🔵 Good (80-89%):    ${analysis.summary.goodFiles}`);
  console.log(`  🟡 Acceptable (70-79%): ${analysis.summary.acceptableFiles}`);
  console.log(`  🟠 Poor (50-69%):    ${analysis.summary.poorFiles}`);
  console.log(`  🔴 Critical (<50%):  ${analysis.summary.criticalFiles}`);

  // By category
  console.log('\n📂 Coverage by Category:');
  for (const [category, data] of Object.entries(analysis.byCategory)) {
    const emoji = PRIORITY_AREAS.critical?.includes(category)
      ? '🔥'
      : PRIORITY_AREAS.important?.includes(category)
        ? '⚡'
        : '📁';
    console.log(
      `  ${emoji} ${category}: ${data.avgCoverage}% (${data.files} files)`
    );
  }

  // Recommendations
  if (analysis.recommendations.length > 0) {
    console.log('\n💡 Recommendations:');
    analysis.recommendations.forEach(rec => {
      const priorityEmoji =
        rec.priority === 'HIGH'
          ? '🔴'
          : rec.priority === 'MEDIUM'
            ? '🟡'
            : '🟢';
      console.log(
        `\n  ${priorityEmoji} ${rec.type} (${rec.priority} Priority)`
      );
      console.log(`     ${rec.description}`);
      console.log(`     Action: ${rec.action}`);
      if (rec.files.length <= 5) {
        console.log(`     Files: ${rec.files.join(', ')}`);
      } else {
        console.log(
          `     Files: ${rec.files.slice(0, 3).join(', ')} and ${rec.files.length - 3} more`
        );
      }
    });
  }

  // Worst performing files
  console.log('\n🎯 Files Needing Attention (Lowest Coverage):');
  const worstFiles = analysis.files.slice(0, 10);
  worstFiles.forEach(file => {
    const emoji =
      file.level === 'critical' ? '🔴' : file.level === 'poor' ? '🟠' : '🟡';
    console.log(`  ${emoji} ${file.name}: ${file.coverage.statements}%`);
  });

  console.log('\n' + '='.repeat(60));
  console.log(`📄 Full report saved to: coverage/analysis-report.json`);
  console.log(`🌐 Open coverage/index.html in browser for detailed view`);
}

// ------------ MAIN EXECUTION

/**
 * Main analysis function
 */
async function runAnalysis() {
  try {
    console.log('🔍 Analyzing test coverage...');
    console.log('📁 Project root:', PROJECT_ROOT);

    const coverageData = await readCoverageData();
    console.log(
      '📊 Found coverage data for',
      Object.keys(coverageData).length,
      'files'
    );

    const analysis = analyzeCoverage(coverageData);
    console.log('✅ Analysis complete');

    // Save detailed report
    const reportPath = path.join(
      PROJECT_ROOT,
      'coverage',
      'analysis-report.json'
    );
    await fs.writeFile(reportPath, JSON.stringify(analysis, null, 2));
    console.log('💾 Report saved to:', reportPath);

    // Print summary
    printReport(analysis);

    return analysis;
  } catch (error) {
    console.error('💥 Coverage analysis failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAnalysis();
}

export { analyzeCoverage, runAnalysis };
