/**
 * @file INDEX.JS
 *
 * @version 3.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Entrypoint for the Discord bot. Handles initialization, environment validation,
 * event registration, and lifecycle management. Uses utility/model folders for logic.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('discord.js').Client} DiscordClient
 * @typedef {import('discord.js').Interaction} Interaction
 * @typedef {import('discord.js').Collection<string, object>} CommandCollection
 * @typedef {DiscordClient & { commands: CommandCollection }} Client
 */

// Class to extend Discord.js Client with custom properties
class ExtendedDiscordClient extends DiscordJsClient {
  /**
   * @type {CommandCollection}
   */
  commands;
}

// ------------ IMPORTS
import 'dotenv/config';
import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath, pathToFileURL } from 'node:url';

import { Client as DiscordJsClient } from 'discord.js';
import ipc from 'node-ipc';

import {
  botConfig,
  intents,
  partials,
  validateEnvironment,
} from './src/config/config.js';
import { loadCommands } from './src/events/interactionCreate.js';
import { handleError } from './src/utils/errorHandler.js';
import logger, { markShuttingDown } from './src/utils/logger.js';

// ------------ CIRRUS ANALYTICS BASE
/**
 * @param {string} _event
 * @param {{ status: string; uptime: number; }} _data
 */
function sendCirrusAnalyticsEvent(_event, _data) {
  // Example: sendCirrusAnalyticsEvent('bot_health', { status: 'ok' });
}

// ------------ DISCORD CLIENT SETUP
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const { TOKEN, APPLICATION_ID, CLIENT_ID } = botConfig.env;

const client = new ExtendedDiscordClient({
  intents,
  partials,
  sweepers: {
    messages: {
      interval: 3600,
      lifetime: 7200,
    },
  },
});

// ------------ IPC CLIENT (node-ipc)

const IPC_ID = process.env.DIS_BOT_IPC_ID;
function sendIpcEvent(event, data) {
  if (IPC_ID && ipc.of[IPC_ID] && typeof ipc.of[IPC_ID].emit === 'function') {
    ipc.of[IPC_ID].emit(event, data);
  }
}
global.sendIpcEvent = sendIpcEvent;

if (IPC_ID) {
  ipc.config.silent = true;
  ipc.connectTo(IPC_ID, () => {
    ipc.of[IPC_ID].on('connect', () => {
      logger.info('Connected to launcher IPC');
    });
    ipc.of[IPC_ID].on('disconnect', () => {
      logger.warn('Disconnected from launcher IPC');
    });
  });
}

// ------------ EVENT REGISTRATION
/**
 * Recursively find all .js/.ts files in a directory and its subdirectories.
 * @param {string} dir
 * @returns {string[]}
 */
function getEventFilesRecursive(dir) {
  const files = [];
  for (const entry of fs.readdirSync(dir, { withFileTypes: true })) {
    const fullPath = path.join(dir, entry.name);
    if (entry.isDirectory()) {
      files.push(...getEventFilesRecursive(fullPath));
    } else if (
      entry.isFile() &&
      (entry.name.endsWith('.js') || entry.name.endsWith('.ts'))
    ) {
      files.push(fullPath);
    }
  }
  return files;
}

/**
 * Registers all event handlers from the events directory (recursively).
 * Loads event modules from the utils/models folders as needed.
 * @returns {Promise<void>}
 */
async function registerEvents() {
  const eventsPath = path.resolve(__dirname, 'src', 'events');
  if (!fs.existsSync(eventsPath)) {
    logger.error(`Events directory not found at: ${eventsPath}`);
    return;
  }
  const eventFiles = getEventFilesRecursive(eventsPath);
  logger.info(`Found ${eventFiles.length} event files to load`);
  for (const filePath of eventFiles) {
    const file = path.basename(filePath);
    try {
      const { default: event } = await import(pathToFileURL(filePath).href);
      if (!event || !event.name || typeof event.execute !== 'function') {
        logger.warn(`Event file ${file} missing required properties`);
        continue;
      }
      if (event.once) {
        client.once(event.name, (...args) => {
          try {
            event.execute(client, ...args);
          } catch (error) {
            logger.error(error, `❌ Error in ${event.name} event`);
          }
        });
      } else {
        client.on(event.name, (...args) => {
          try {
            event.execute(client, ...args);
          } catch (error) {
            logger.error(error, `❌ Error in ${event.name} event`);
          }
        });
      }
      logger.info(`Loaded event: ${event.name}`);
    } catch (err) {
      logger.error(`Failed to load event ${file}:`, err);
    }
  }
}

// ------------ ERROR HANDLING (delegated to utils/errorHandler.js)
process.on('unhandledRejection', err => {
  const error = err instanceof Error ? err : new Error(String(err));
  logger.error('UnhandledRejection:', {
    error: error.message,
    stack: error.stack,
  });
});
process.on('uncaughtException', err => {
  const error = err instanceof Error ? err : new Error(String(err));
  logger.error('UncaughtException:', {
    error: error.message,
    stack: error.stack,
  });
  process.exit(1);
});

// ------------ SHUTDOWN HANDLER
/**
 * Graceful shutdown handler.
 * Cleans up all services, cache, and connections.
 * @param {string} signal
 * @returns {Promise<void>}
 */
async function gracefulShutdown(signal) {
  logger.info(`💤 Shutting down (${signal})...`);
  markShuttingDown();

  try {
    client.destroy();
    if (typeof logger.flush === 'function') {
      logger.flush();
    }
    try {
      const onboardingService = (
        await import('./src/services/onboardingService.js')
      ).default;
      if (typeof onboardingService.shutdown === 'function') {
        onboardingService.shutdown();
      }
    } catch (e) {
      logger.warn('OnboardingService graceful shutdown failed:', e);
    }
    try {
      const { defaultCache } = await import('./src/utils/cacheManagement.js');
      if (defaultCache && typeof defaultCache.destroy === 'function') {
        defaultCache.destroy();
      }
    } catch (e) {
      logger.warn('CacheManager cleanup failed:', e);
    }
    // Cooldown cleanup: clear the cooldowns map to free memory
    try {
      const cooldownModule = await import('./src/utils/cooldown.js');
      if (
        'clearAll' in cooldownModule &&
        typeof cooldownModule.clearAll === 'function'
      ) {
        cooldownModule.clearAll();
      } else if (
        'cooldowns' in cooldownModule &&
        cooldownModule.cooldowns instanceof Map
      ) {
        cooldownModule.cooldowns.clear();
      }
    } catch (e) {
      logger.warn('CooldownManager cleanup failed:', e);
    }
    // Rate limit cleanup: clear the rateLimit.requests map to free memory
    try {
      const rateLimitModule = await import('./src/utils/rateLimit.js');
      if (
        'rateLimit' in rateLimitModule &&
        rateLimitModule.rateLimit &&
        typeof rateLimitModule.rateLimit.requests === 'object' &&
        typeof rateLimitModule.rateLimit.requests.clear === 'function'
      ) {
        rateLimitModule.rateLimit.requests.clear();
      }
    } catch (e) {
      logger.warn('RateLimitManager cleanup failed:', e);
    }
    await new Promise(res => setTimeout(res, 500));
    logger.info('Shutdown complete. Exiting.');
    process.exit(0);
  } catch (err) {
    logger.error('Error during graceful shutdown:', err);
    process.exit(1);
  }
}

process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

// ------------ HEALTH CHECK ENDPOINT (delegated to utils or left inline)
if (botConfig.healthCheck.ENABLE_HEALTH_ENDPOINT === 'true') {
  import('http').then(({ createServer }) => {
    const port = botConfig.healthCheck.HEALTH_PORT;
    const server = createServer((req, res) => {
      if (req.url === '/health' && req.method === 'GET') {
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ status: 'ok', uptime: process.uptime() }));
      } else {
        res.writeHead(404);
        res.end();
      }
    });
    server.on('error', err => {
      const error = err instanceof Error ? err : new Error(String(err));
      logger.warn(`Health endpoint failed to start: ${error.message}`);
    });
    server.listen(port, () =>
      logger.info(`Health check endpoint running on :${port}/health`)
    );
  });
}

// ------------ IPC MESSAGE HANDLING FOR LAUNCHER
if (process.send) {
  process.on('message', async message => {
    if (!message || typeof message !== 'object' || !('action' in message))
      return;

    /**
     * @type {{ action?: string, signal?: string }}
     */

    const msg = /** @type {{ action?: string, signal?: string }} */ (message);
    const typedMessage = {
      action: typeof msg.action === 'string' ? msg.action : undefined,
      signal: typeof msg.signal === 'string' ? msg.signal : undefined,
    };
    const action = typedMessage.action;
    switch (action) {
      case 'prepare-restart':
        logger.info('Received prepare-restart signal from launcher');
        try {
          client.rest.setToken('');
          logger.info('Stopped accepting new Discord API requests');
          try {
            const onboardingService = (
              await import('./src/services/onboardingService.js')
            ).default;
            if (typeof onboardingService.shutdown === 'function') {
              onboardingService.shutdown();
              logger.info('Onboarding service cleaned up');
            }
          } catch (e) {
            logger.warn('OnboardingService cleanup failed:', e);
          }
          try {
            const { defaultCache } = await import(
              './src/utils/cacheManagement.js'
            );
            if (defaultCache && typeof defaultCache.destroy === 'function') {
              defaultCache.destroy();
              logger.info('Cache manager cleaned up');
            }
          } catch (e) {
            logger.warn('CacheManager cleanup failed:', e);
          }
          // Cooldown cleanup: clear the cooldowns map to free memory
          try {
            const cooldownModule = await import('./src/utils/cooldown.js');
            if (
              'clearAll' in cooldownModule &&
              typeof cooldownModule.clearAll === 'function'
            ) {
              cooldownModule.clearAll();
              logger.info('Cooldown manager cleaned up');
            } else if (
              'cooldowns' in cooldownModule &&
              cooldownModule.cooldowns instanceof Map
            ) {
              cooldownModule.cooldowns.clear();
              logger.info('Cooldown manager cleaned up');
            }
          } catch (e) {
            logger.warn('CooldownManager cleanup failed:', e);
          }
          // Rate limit cleanup: clear the rateLimit.requests map to free memory
          try {
            const rateLimitModule = await import('./src/utils/rateLimit.js');
            if (
              'rateLimit' in rateLimitModule &&
              rateLimitModule.rateLimit &&
              typeof rateLimitModule.rateLimit.requests === 'object' &&
              typeof rateLimitModule.rateLimit.requests.clear === 'function'
            ) {
              rateLimitModule.rateLimit.requests.clear();
              logger.info('Rate limit manager cleaned up');
            }
          } catch (e) {
            logger.warn('RateLimitManager cleanup failed:', e);
          }
          logger.info('Restart preparation complete');
          await client.destroy();
          process.exit(0);
        } catch (error) {
          logger.error('Error during restart preparation:', error);
          process.exit(1);
        }
      case 'shutdown': {
        const signal =
          typeof (typedMessage && typedMessage.signal) === 'string'
            ? typedMessage.signal
            : 'SIGTERM';
        logger.info(`Received shutdown signal from launcher: ${signal}`);
        await gracefulShutdown(signal ?? 'SIGTERM');
        break;
      }
      default:
        logger.debug(
          `Received unknown message from launcher: ${JSON.stringify(message)}`
        );
    }
  });
  logger.info('IPC message handling initialized');
}

// ------------ APPLICATION ENTRYPOINT
(async () => {
  try {
    if (!validateEnvironment()) {
      process.exit(1);
    }
    const commands = await loadCommands();
    client.commands = commands;
    await registerEvents();
    logger.info('✅ Events registered successfully');
    client.once('ready', () => {
      global.client = client;
      if (client.user) {
        logger.info(`🤖 Logged in as ${client.user.tag}`);
      } else {
        logger.warn('Client user is null after login');
      }
      logger.info(`📌 Application ID: ${APPLICATION_ID}`);
      logger.info(`🆔 Client ID: ${CLIENT_ID}`);
      if (client.user) {
        client.user.setPresence({
          activities: [{ name: 'Dynamic Innovative Studio', type: 0 }],
          status: 'online',
        });
      }
      // Always print stats table on startup
      const printStats = () => {
        if (!client.user) return;
        const stats = {
          User: client.user.tag,
          ApplicationID: APPLICATION_ID,
          ClientID: CLIENT_ID,
          Guilds: client.guilds.cache.size,
          NodeVersion: process.version,
          Platform: process.platform,
          MemoryUsage: `${Math.round(process.memoryUsage().rss / 1024 / 1024)}MB`,
        };
        console.clear();
        console.table(stats);
      };
      printStats();
      setInterval(printStats, 120000);
    });
    await client.login(TOKEN);
    logger.info('🔐 Successfully authenticated with Discord');
    const HEALTH_CHECK_INTERVAL = Number(
      botConfig.healthCheck.HEALTH_CHECK_INTERVAL
    );
    setInterval(() => {
      const memUsage = process.memoryUsage();
      logger.info(
        `✅ Bot is OK | Uptime: ${Math.floor(process.uptime())}s | Guilds: ${client.guilds.cache.size} | RSS: ${Math.round(memUsage.rss / 1024 / 1024)}MB`
      );
      // Sentry handled by logger.js
      sendCirrusAnalyticsEvent('bot_health', {
        status: 'ok',
        uptime: process.uptime(),
      });
    }, HEALTH_CHECK_INTERVAL);
  } catch (error) {
    await handleError(error, {}, client);
    process.exit(1);
  }
})();
