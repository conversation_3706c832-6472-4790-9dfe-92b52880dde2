// @ts-check
/**
 * @file LAUNCH.JS
 *
 * @version 3.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Bot launcher for Dynamic Innovative Studio Discord bot.
 * Handles process management, graceful restarts, and IPC with the bot process.
 * Ensures robust error handling, restart throttling, and clean shutdowns.
 */

// ------------ CODE QUALITY CONFIGURATIONS (for IDEs) - do NOT change

// @ts-check

// ------------------------ TYPE SAFETY ANNOTATIONS ------------------------ //
/**
 * @typedef {import('child_process').ChildProcess} ChildProcess
 * @typedef {Object} LauncherConfig
 * @property {number} maxRestartAttempts
 * @property {number} restartCooldown
 * @property {number} restartTimeout
 */

// ------------ IMPORTS

import { execa } from 'execa';
import ipc from 'node-ipc';

import logger from './src/utils/logger.js';

// ------------ PATHS & ENVIRONMENT

// ------------ LAUNCHER CONFIGURATION
/** @type {LauncherConfig} */
const launcherConfig = {
  maxRestartAttempts: 5,

  // 1 minute between restarts
  restartCooldown: 60000,

  // 10 seconds to wait for graceful shutdown
  restartTimeout: 10000,
};

// ------------ STATE
/** @type {number} */
let restartAttempts = 0;
/** @type {number} */
let lastRestartTime = 0;
/** @type {ReturnType<typeof execa>|null} */
let botProcess = null;
/** @type {boolean} */
let shuttingDown = false;

// ------------ IPC CONFIGURATION
const IPC_ID = 'dis-bot-ipc';
ipc.config.id = IPC_ID;
ipc.config.retry = 1500;
ipc.config.silent = true;

// ------------ BOT PROCESS MANAGEMENT

/**
 * Starts the bot process as a child process using execa and sets up IPC.
 * @returns {ReturnType<typeof execa>}
 */
function startBot() {
  logger.info('🚀 Starting bot process...');
  const proc = execa('node', ['./index.js'], {
    env: {
      ...process.env,
      DIS_BOT_IPC_ID: IPC_ID,
    },
    stdio: ['inherit', 'inherit', 'inherit'],
  });
  proc.on('exit', (code, signal) => {
    if (shuttingDown) return;
    logger.info(`⚠️ Bot process exited with code ${code} and signal ${signal}`);
    if (code !== 0) handleUnexpectedExit(code ?? 1);
  });
  proc.on('error', err => {
    logger.error('❌ Bot process error:', err);
    if (!shuttingDown) handleUnexpectedExit(1);
  });
  return proc;
}

/**
 * Handles an unexpected exit of the bot process.
 * Implements exponential backoff for rapid crash cycles.
 * @param {number} _code
 */
function handleUnexpectedExit(_code) {
  if (shuttingDown) return;
  const now = Date.now();
  if (now - lastRestartTime < launcherConfig.restartCooldown) {
    restartAttempts++;
    if (restartAttempts >= launcherConfig.maxRestartAttempts) {
      logger.error(
        `❌ Bot crashed ${restartAttempts} times in rapid succession. Stopping auto-restart.`
      );
      logger.error('Please fix the issue and restart manually.');
      process.exit(1);
    }
    const delay = Math.min(5000 * Math.pow(2, restartAttempts - 1), 60000);
    logger.info(`⏱️ Waiting ${delay}ms before attempting restart...`);
    setTimeout(() => restartBot(), delay);
  } else {
    restartAttempts = 0;
    restartBot();
  }
  lastRestartTime = now;
}

/**
 * Restarts the bot process gracefully.
 */
function restartBot() {
  if (shuttingDown) return;
  logger.info('🔁 Restarting bot...');
  if (botProcess) {
    logger.info('📨 Sending SIGTERM to bot process for graceful restart...');
    botProcess.kill('SIGTERM');
    const forceKillTimer = setTimeout(() => {
      logger.warn('⚠️ Bot process did not exit gracefully, force killing...');
      if (botProcess) botProcess.kill('SIGKILL');
    }, launcherConfig.restartTimeout);
    botProcess.once('exit', () => {
      clearTimeout(forceKillTimer);
      botProcess = startBot();
      setupIpcHandlers();
    });
  } else {
    botProcess = startBot();
    setupIpcHandlers();
  }
}

/**
 * Performs a graceful shutdown of the launcher and bot.
 * @param {string} signal
 */
function gracefulShutdown(signal) {
  if (shuttingDown) return;
  shuttingDown = true;
  logger.info(`💤 Launcher shutting down (${signal})...`);
  if (botProcess) {
    logger.info('📨 Sending SIGTERM to bot process for shutdown...');
    botProcess.kill('SIGTERM');
    const forceKillTimer = setTimeout(() => {
      logger.warn('⚠️ Bot process did not exit gracefully, force killing...');
      if (botProcess) botProcess.kill('SIGKILL');
      process.exit(0);
    }, launcherConfig.restartTimeout);
    botProcess.once('exit', () => {
      clearTimeout(forceKillTimer);
      logger.info('✅ Bot process exited gracefully.');
      process.exit(0);
    });
  } else {
    process.exit(0);
  }
}

// ------------ IPC HANDLING

/**
 * Sets up IPC handlers for messages from the bot process using node-ipc.
 */
function setupIpcHandlers() {
  ipc.serve(() => {
    ipc.server.on('restart', data => {
      logger.info('🔄 Received restart request from bot via IPC', data);
      restartBot();
    });
    ipc.server.on('shutdown', data => {
      logger.info('💤 Received shutdown request from bot via IPC', data);
      gracefulShutdown('IPC_SHUTDOWN');
    });
    ipc.server.on('log', data => {
      logger.info(`[BOT IPC LOG]: ${data}`);
    });
  });
  ipc.server.start();
}

// ------------ SIGNAL & ERROR HANDLING

process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('uncaughtException', err => {
  logger.error('❌ Launcher uncaught exception:', err);
  gracefulShutdown('CRASH');
});

// ------------ MAIN ENTRYPOINT

botProcess = startBot();
setupIpcHandlers();

logger.info('✅ Bot launcher initialized');
