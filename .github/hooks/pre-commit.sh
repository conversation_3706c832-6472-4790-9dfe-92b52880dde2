#!/bin/bash
branch_name=$(git rev-parse --abbrev-ref HEAD)
if [[ ! "$branch_name" =~ ^(feature|bugfix|hotfix)/ ]]; then
    echo "Error: Branch name '$branch_name' must start with feature/, bugfix/, or hotfix/."
    exit 1
fi

# Add commit message validation
commit_msg=$(cat $1)
if [[ ! "$commit_msg" =~ ^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: ]]; then
    echo "Error: Commit message must follow conventional commits format."
    echo "Example: feat(login): add password validation"
    exit 1
fi

exit 0
