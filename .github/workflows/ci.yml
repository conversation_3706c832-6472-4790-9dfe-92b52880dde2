name: CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  lint-build-test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [18, 20, 22]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Run Format Check
        run: yarn format --check

      - name: Lint Code
        run: yarn lint

      - name: Type Check
        run: yarn tsc --noEmit

      - name: Build Project
        run: yarn build

      - name: Run Tests with Coverage
        run: yarn test:coverage

      - name: Upload Coverage Artifact
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: coverage-${{ matrix.node-version }}
          path: coverage/
          if-no-files-found: ignore

      - name: Commitlint (PR Only)
        if: github.event_name == 'pull_request'
        run: |
          git fetch origin main --depth=1
          git log origin/main..HEAD --pretty=format:%s | npx commitlint

      - name: Summarize Test Coverage
        if: always()
        run: |
          if [ -f coverage/lcov.info ]; then
            echo "### Test Coverage" >> $GITHUB_STEP_SUMMARY
            npx coverage-badger -i coverage/lcov.info -o badge.svg
          fi
