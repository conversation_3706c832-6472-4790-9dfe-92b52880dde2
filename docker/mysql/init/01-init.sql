-- ========================================================================== --
-- MYSQL INITIALIZATION SCRIPT FOR DIS DISCORD BOT                           --
-- ========================================================================== --

-- Create the main database if it doesn't exist
CREATE DATABASE IF NOT EXISTS `dis_bot` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create development database if it doesn't exist
CREATE DATABASE IF NOT EXISTS `dis_bot_dev` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create test database if it doesn't exist
CREATE DATABASE IF NOT EXISTS `dis_bot_test` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the main database
USE `dis_bot`;

-- Create the internal workers table
CREATE TABLE IF NOT EXISTS `dis_internal_workers` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `discord_id` VARCHAR(20) NOT NULL UNIQUE,
    `email` TEXT NOT NULL,
    `roblox_username` VARCHAR(100) DEFAULT NULL,
    `nickname` VARCHAR(100) DEFAULT NULL,
    `timezone` VARCHAR(50) DEFAULT NULL,
    `onboarding_completed` BOOLEAN DEFAULT FALSE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_discord_id` (`discord_id`),
    INDEX `idx_onboarding_completed` (`onboarding_completed`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create messages table for message tracking
CREATE TABLE IF NOT EXISTS `messages` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `discord_message_id` VARCHAR(20) NOT NULL UNIQUE,
    `discord_user_id` VARCHAR(20) NOT NULL,
    `channel_id` VARCHAR(20) NOT NULL,
    `content` TEXT,
    `message_type` ENUM('dm', 'channel', 'system') DEFAULT 'channel',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX `idx_discord_message_id` (`discord_message_id`),
    INDEX `idx_discord_user_id` (`discord_user_id`),
    INDEX `idx_channel_id` (`channel_id`),
    INDEX `idx_message_type` (`message_type`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create audit log table for tracking changes
CREATE TABLE IF NOT EXISTS `audit_logs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `action` VARCHAR(100) NOT NULL,
    `actor_discord_id` VARCHAR(20) NOT NULL,
    `target_discord_id` VARCHAR(20) DEFAULT NULL,
    `details` JSON DEFAULT NULL,
    `ip_address` VARCHAR(45) DEFAULT NULL,
    `user_agent` TEXT DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX `idx_action` (`action`),
    INDEX `idx_actor_discord_id` (`actor_discord_id`),
    INDEX `idx_target_discord_id` (`target_discord_id`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create sessions table for tracking user sessions
CREATE TABLE IF NOT EXISTS `sessions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `session_id` VARCHAR(128) NOT NULL UNIQUE,
    `discord_user_id` VARCHAR(20) NOT NULL,
    `data` JSON DEFAULT NULL,
    `expires_at` TIMESTAMP NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_discord_user_id` (`discord_user_id`),
    INDEX `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert initial data if needed
INSERT IGNORE INTO `dis_internal_workers` (`discord_id`, `email`, `onboarding_completed`) 
VALUES ('000000000000000000', '<EMAIL>', TRUE);

-- Create the same tables for development database
USE `dis_bot_dev`;

CREATE TABLE IF NOT EXISTS `dis_internal_workers` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `discord_id` VARCHAR(20) NOT NULL UNIQUE,
    `email` TEXT NOT NULL,
    `roblox_username` VARCHAR(100) DEFAULT NULL,
    `nickname` VARCHAR(100) DEFAULT NULL,
    `timezone` VARCHAR(50) DEFAULT NULL,
    `onboarding_completed` BOOLEAN DEFAULT FALSE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_discord_id` (`discord_id`),
    INDEX `idx_onboarding_completed` (`onboarding_completed`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `messages` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `discord_message_id` VARCHAR(20) NOT NULL UNIQUE,
    `discord_user_id` VARCHAR(20) NOT NULL,
    `channel_id` VARCHAR(20) NOT NULL,
    `content` TEXT,
    `message_type` ENUM('dm', 'channel', 'system') DEFAULT 'channel',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX `idx_discord_message_id` (`discord_message_id`),
    INDEX `idx_discord_user_id` (`discord_user_id`),
    INDEX `idx_channel_id` (`channel_id`),
    INDEX `idx_message_type` (`message_type`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `audit_logs` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `action` VARCHAR(100) NOT NULL,
    `actor_discord_id` VARCHAR(20) NOT NULL,
    `target_discord_id` VARCHAR(20) DEFAULT NULL,
    `details` JSON DEFAULT NULL,
    `ip_address` VARCHAR(45) DEFAULT NULL,
    `user_agent` TEXT DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX `idx_action` (`action`),
    INDEX `idx_actor_discord_id` (`actor_discord_id`),
    INDEX `idx_target_discord_id` (`target_discord_id`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE IF NOT EXISTS `sessions` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `session_id` VARCHAR(128) NOT NULL UNIQUE,
    `discord_user_id` VARCHAR(20) NOT NULL,
    `data` JSON DEFAULT NULL,
    `expires_at` TIMESTAMP NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_discord_user_id` (`discord_user_id`),
    INDEX `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
