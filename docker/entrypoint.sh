#!/bin/sh

# ========================================================================== #
# DOCKER ENTRYPOINT SCRIPT FOR DIS DISCORD BOT                             #
# ========================================================================== #

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" >&2
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

success() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1"
}

# Function to check if required environment variables are set
check_env_vars() {
    log "Checking required environment variables..."
    
    local required_vars=(
        "TOKEN"
        "APPLICATION_ID"
        "CLIENT_ID"
        "CLIENT_SECRET"
        "PUBLIC_KEY"
        "DB_USERNAME"
        "DB_DATABASE"
        "DB_PASSWORD"
        "DB_HOST"
        "DB_PORT"
        "USER_DATA_ENCRYPTION_KEY"
        "ADMIN_ROLE_ID"
        "HR_ROLE_ID"
        "VERIFIED_ROLE_ID"
        "WELCOME_CHANNEL_ID"
        "ADMIN_LOG_CHANNEL_ID"
        "ERROR_LOG_CHANNEL_ID"
    )
    
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "$(eval echo \$$var)" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        error "Missing required environment variables:"
        for var in "${missing_vars[@]}"; do
            error "  - $var"
        done
        error "Please set all required environment variables before starting the bot."
        exit 1
    fi
    
    success "All required environment variables are set"
}

# Function to wait for database connection
wait_for_db() {
    log "Waiting for database connection..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z "$DB_HOST" "$DB_PORT" 2>/dev/null; then
            success "Database is available"
            return 0
        fi
        
        warn "Database not available yet (attempt $attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    error "Database is not available after $max_attempts attempts"
    exit 1
}

# Function to create necessary directories
create_directories() {
    log "Creating necessary directories..."
    
    mkdir -p logs
    mkdir -p public/icons
    
    success "Directories created"
}

# Function to deploy commands if needed
deploy_commands() {
    if [ "$DEPLOY_COMMANDS" = "true" ]; then
        log "Deploying Discord slash commands..."
        node src/deploy-commands.js
        success "Commands deployed"
    else
        log "Skipping command deployment (DEPLOY_COMMANDS not set to true)"
    fi
}

# Function to start the bot
start_bot() {
    log "Starting DIS Discord Bot..."
    
    # Use bundled version if available, otherwise use source
    if [ -f "dist/index.js" ] && [ "$USE_BUNDLE" = "true" ]; then
        log "Starting from bundled version..."
        exec node dist/index.js
    else
        log "Starting from source..."
        exec node launch.js
    fi
}

# Main execution
main() {
    log "🚀 DIS Discord Bot - Docker Container Starting"
    log "Environment: ${APP_ENV:-production}"
    log "Node.js Version: $(node --version)"
    
    # Run all checks and setup
    check_env_vars
    wait_for_db
    create_directories
    deploy_commands
    
    # Start the bot
    start_bot
}

# Handle signals for graceful shutdown
trap 'log "Received shutdown signal, stopping bot..."; exit 0' TERM INT

# Run main function
main "$@"
