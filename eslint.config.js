// @ts-check
/**
 * @file ESLINT.CONFIG.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 * @contributors
 * @license MIT
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * Modern ESLint configuration for JavaScript Discord bot project.
 * Includes rules for code quality, security, and formatting.
 */

// ------------ IMPORTS
import js from '@eslint/js';
import prettier from 'eslint-config-prettier';
import importPlugin from 'eslint-plugin-import';
import prettierPlugin from 'eslint-plugin-prettier';
import security from 'eslint-plugin-security';
import sonarjs from 'eslint-plugin-sonarjs';
import unicorn from 'eslint-plugin-unicorn';
import globals from 'globals';

/**
 * Helper function to define ESLint configuration
 * @param {Array} configs - Array of ESLint configurations
 * @returns {Array} ESLint configuration array
 */
const defineConfig = configs => configs;

/**
 * ESLint configuration for the project.
 * Extends recommended rules and plugins for JavaScript, Prettier, security, and SonarJS.
 * Enforces consistent code style, formatting (indentation, spacing, semicolons), and complexity limits.
 * Includes rules for maintainability, security best practices, and import path resolution via SonarJS.
 * Targets JavaScript files using ES module syntax, with the latest ECMAScript version and Node.js globals.
 * Compatible with ESLint v8.0.0+ and defined using the ESLint config API.
 * Exported as an array for potential future multi-config support.
 * Supports JavaScript files with .js, .mjs, and .cjs extensions.
 * Supports prettier integration for consistent code formatting.
 */
export default defineConfig([
  js.configs.recommended,
  {
    files: ['**/*.{js,mjs,cjs}'],
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      globals: globals.node,
    },
    plugins: {
      prettier: prettierPlugin,
      unicorn,
      security,
      sonarjs,
      import: importPlugin,
    },
    rules: {
      // Prettier integration
      'prettier/prettier': 'error',

      // Code style
      indent: ['error', 'tab'],
      'linebreak-style': ['error', 'unix'],
      quotes: ['error', 'single'],
      semi: ['error', 'always'],

      // Best practices
      'no-console': 'off',
      'no-unused-vars': 'warn',
      'no-var': 'error',
      'prefer-const': 'error',
      'no-undef': 'error',
      'no-duplicate-imports': 'error',
      'no-unreachable': 'error',
      'no-unused-expressions': 'error',

      // Complexity and maintainability
      complexity: ['warn', 32],
      'max-lines-per-function': ['warn', 250],
      'max-params': ['warn', 5],
      'max-depth': ['warn', 4],
      'max-nested-callbacks': ['warn', 4],
      'max-statements': ['warn', 50],
      'max-lines': ['warn', 1000],

      // Maintainability
      'sonarjs/cognitive-complexity': ['error', 64],

      // Security
      'security/detect-object-injection': 'error',
      'security/detect-non-literal-fs-filename': 'error',
      'security/detect-child-process': 'warn',

      'unicorn/prefer-ternary': 'error',
      'unicorn/require-number-to-fixed-digits-argument': 'error',

      // Import sorting/validation
      'import/order': ['warn', { 'newlines-between': 'always' }],
      'import/no-unresolved': 'error',
    },
    settings: {
      // SonarJS
      'import/resolver': {
        node: { extensions: ['.js', '.mjs', '.cjs'] },
      },
    },
  },

  // Test files configuration
  {
    files: ['**/*.{test,spec}.{js,mjs,cjs}', '**/tests/**/*.{js,mjs,cjs}'],
    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.jest,
      },
    },
    rules: {
      // Relax rules for test files
      'max-lines-per-function': 'off',
      'max-lines': 'off',
      'no-console': 'off',
      'sonarjs/cognitive-complexity': 'off',
    },
  },

  // Prettier integration (must be last)
  prettier,
]);
