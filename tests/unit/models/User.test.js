// @ts-check
/**
 * @file USER.CONSOLIDATED.TEST.JS
 *
 * @version 2.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * Comprehensive unit tests for User model.
 * Consolidates all User model tests including edge cases and key validation.
 * Tests user data validation, encryption, formatting, and error handling.
 */

// ------------ IMPORTS
import { afterEach, beforeEach, describe, expect, jest, test } from '@jest/globals';

import { User } from '../../../src/models/User.js';
import { userFixtures } from '../../helpers/fixtures.js';

// Mock Sentry logger
jest.mock('@sentry/node', () => ({
  logger: {
    error: jest.fn(),
  },
}));

// ------------ TEST SETUP
describe('User Model - Comprehensive Tests', () => {
  let validUserData;
  let originalEnv;
  let originalConsoleError;

  beforeEach(() => {
    // Store original environment and console
    originalEnv = process.env.USER_DATA_ENCRYPTION_KEY;
    originalConsoleError = console.error;

    // Set up test environment
    process.env.USER_DATA_ENCRYPTION_KEY = 'test-encryption-key-32-chars-lo!';
    validUserData = { ...userFixtures.validUser };

    // Mock console.error to capture calls
    console.error = jest.fn();
  });

  afterEach(() => {
    // Restore original environment
    if (originalEnv !== undefined) {
      process.env.USER_DATA_ENCRYPTION_KEY = originalEnv;
    } else {
      delete process.env.USER_DATA_ENCRYPTION_KEY;
    }

    // Restore console.error
    console.error = originalConsoleError;

    // Clear module cache
    jest.resetModules();
  });

  // ------------ CONSTRUCTOR TESTS
  describe('constructor', () => {
    test('should create user with valid data', () => {
      const user = new User(validUserData);

      expect(user.id).toBe(validUserData.id);
      expect(user.tag).toBe(validUserData.tag);
      expect(user.email).toBe(validUserData.email);
      expect(user.nickname).toBe(validUserData.nickname);
      expect(user.robloxUsername).toBe(validUserData.robloxUsername);
      expect(user.verified).toBe(validUserData.verified);
      expect(user.timeZone).toBe(validUserData.timeZone);
    });

    test('should throw error for missing user ID', () => {
      const invalidData = { ...validUserData };
      delete invalidData.id;

      expect(() => new User(invalidData)).toThrow('User id is required for User model');
    });

    test('should set default values for optional fields', () => {
      const minimalData = {
        id: '123456789012345678',
        tag: 'TestUser#1234',
      };

      const user = new User(minimalData);

      expect(user.email).toBeNull();
      expect(user.nickname).toBeNull();
      expect(user.robloxUsername).toBeNull();
      expect(user.verified).toBe(false);
      expect(user.verifiedAt).toBeNull();
      expect(user.timeZone).toBeNull();
    });

    test('should handle boolean conversion for verified field', () => {
      const testCases = [
        { verified: true, expected: true },
        { verified: false, expected: false },
        { verified: 1, expected: true },
        { verified: 0, expected: false },
        { verified: 'true', expected: true },
        { verified: '', expected: false },
        { verified: null, expected: false },
        { verified: undefined, expected: false },
      ];

      testCases.forEach(({ verified, expected }) => {
        const userData = { ...validUserData, verified };
        const user = new User(userData);
        expect(user.verified).toBe(expected);
      });
    });
  });

  // ------------ TO OBJECT TESTS
  describe('toObject', () => {
    test('should convert user to plain object for storage', () => {
      const user = new User(validUserData);
      const obj = user.toObject();

      expect(obj.id_dis_internal_workers).toBe(validUserData.id);
      expect(obj.tag).toBe(validUserData.tag);
      expect(obj.nickname).toBe(validUserData.nickname);
      expect(obj.robloxUsername).toBe(validUserData.robloxUsername);
      expect(obj.timeZone).toBe(validUserData.timeZone);
      expect(obj.verified).toBe(1); // Should be converted to 1 for MySQL
      expect(obj.onboardingStatus).toBe(validUserData.onboardingStatus);
    });

    test('should encrypt email field', () => {
      const user = new User(validUserData);
      const obj = user.toObject();

      // Email should be encrypted (not plain text)
      expect(obj.email).not.toBe(validUserData.email);
      expect(obj.email).toBeTruthy();
      expect(typeof obj.email).toBe('string');
    });

    test('should handle null email', () => {
      const userData = { ...validUserData, email: null };
      const user = new User(userData);
      const obj = user.toObject();

      expect(obj.email).toBeNull();
    });

    test('should set default dates', () => {
      const userData = {
        id: '123456789012345678',
        tag: 'TestUser#1234',
      };
      const user = new User(userData);
      const obj = user.toObject();

      expect(obj.joinedAt).toBeInstanceOf(Date);
      expect(obj.lastInteraction).toBeInstanceOf(Date);
    });
  });

  // ------------ UPDATE METHOD TESTS
  describe('update method', () => {
    test('should update user properties', () => {
      const user = new User(validUserData);
      const updates = {
        nickname: 'Updated Nickname',
        verified: true,
        onboardingStatus: 'completed',
      };

      user.update(updates);

      expect(user.nickname).toBe('Updated Nickname');
      expect(user.verified).toBe(true);
      expect(user.onboardingStatus).toBe('completed');
      expect(user.lastInteraction).toBeInstanceOf(Date);
    });

    test('should ignore unknown fields', () => {
      const user = new User(validUserData);
      const originalEmail = user.email;

      user.update({ unknownField: 'value' });

      expect(user.email).toBe(originalEmail);
      expect(user.lastInteraction).toBeInstanceOf(Date);
    });

    test('should update lastInteraction on every update', () => {
      const user = new User(validUserData);
      const originalTime = user.lastInteraction;

      // Wait a bit to ensure time difference
      setTimeout(() => {
        user.update({ nickname: 'New Nickname' });
        expect(user.lastInteraction.getTime()).toBeGreaterThan(originalTime.getTime());
      }, 1);
    });

    test('should handle multiple field updates in single call', () => {
      const user = new User(validUserData);
      const updates = {
        tag: 'NewUser#9999',
        email: '<EMAIL>',
        nickname: 'NewNick',
        verified: true,
        timeZone: 'America/New_York',
      };

      user.update(updates);

      expect(user.tag).toBe(updates.tag);
      expect(user.email).toBe(updates.email);
      expect(user.nickname).toBe(updates.nickname);
      expect(user.verified).toBe(updates.verified);
      expect(user.timeZone).toBe(updates.timeZone);
    });

    test('should handle null and undefined values in updates', () => {
      const user = new User(validUserData);

      user.update({
        email: null,
        nickname: undefined,
        robloxUsername: null,
        timeZone: undefined,
      });

      expect(user.email).toBeNull();
      expect(user.nickname).toBeUndefined();
      expect(user.robloxUsername).toBeNull();
      expect(user.timeZone).toBeUndefined();
    });
  });

  // ------------ VERIFY METHOD TESTS
  describe('verify method', () => {
    test('should mark user as verified', () => {
      const user = new User({ ...validUserData, verified: false });

      user.verify();

      expect(user.verified).toBe(true);
      expect(user.verifiedAt).toBeInstanceOf(Date);
      expect(user.onboardingStatus).toBe('completed');
    });

    test('should set verification timestamp', () => {
      const user = new User(validUserData);
      const beforeVerify = Date.now();

      user.verify();

      expect(user.verifiedAt.getTime()).toBeGreaterThanOrEqual(beforeVerify);
    });
  });

  // ------------ STATIC METHODS TESTS
  describe('static methods', () => {
    describe('fromMySQL', () => {
      test('should create user from MySQL data', () => {
        const mysqlData = {
          id_dis_internal_workers: '123456789012345678',
          tag: 'TestUser#1234',
          email: 'encrypted_email_data',
          nickname: 'Tester',
          robloxUsername: 'TestRobloxUser',
          timeZone: 'UTC',
          verified: 1,
          joinedAt: new Date('2024-01-01T00:00:00Z'),
          verifiedAt: new Date('2024-01-01T01:00:00Z'),
          onboardingStartTime: new Date('2024-01-01T00:30:00Z'),
          metadata: '{"source":"test"}',
          onboardingStatus: 'completed',
          lastInteraction: new Date('2024-01-01T02:00:00Z'),
        };

        const user = User.fromMySQL(mysqlData);

        expect(user.id).toBe(mysqlData.id_dis_internal_workers);
        expect(user.tag).toBe(mysqlData.tag);
        expect(user.verified).toBe(true); // Converted from 1
        expect(user.nickname).toBe(mysqlData.nickname);
        expect(user.robloxUsername).toBe(mysqlData.robloxUsername);
        expect(user.timeZone).toBe(mysqlData.timeZone);
      });

      test('should handle null values from MySQL', () => {
        const mysqlData = {
          id_dis_internal_workers: '123456789012345678',
          tag: 'TestUser#1234',
          email: null,
          nickname: null,
          robloxUsername: null,
          timeZone: null,
          verified: 0,
          joinedAt: new Date(),
          verifiedAt: null,
          onboardingStartTime: null,
          metadata: '{}',
          onboardingStatus: 'pending',
          lastInteraction: new Date(),
        };

        const user = User.fromMySQL(mysqlData);

        expect(user.email).toBeNull();
        expect(user.nickname).toBeNull();
        expect(user.robloxUsername).toBeNull();
        expect(user.timeZone).toBeNull();
        expect(user.verified).toBe(false);
        expect(user.verifiedAt).toBeNull();
      });
    });
  });

  // ------------ ENCRYPTION KEY VALIDATION TESTS (Isolated)
  describe('encryption key validation (isolated tests)', () => {
    test('should throw error for invalid key length', async () => {
      // Set an invalid key length (not 32 bytes)
      process.env.USER_DATA_ENCRYPTION_KEY = 'short-key-invalid-length';

      // Clear module cache to force re-import
      jest.resetModules();

      // Import should throw an error due to invalid key length
      await expect(async () => {
        await import('../../../src/models/User.js');
      }).rejects.toThrow('Invalid encryption key length; must be 32 bytes for AES-256-GCM');
    });

    test('should throw error for hex key with wrong length', async () => {
      // Set a hex key that's not 32 bytes (this is 16 bytes = 32 hex chars)
      process.env.USER_DATA_ENCRYPTION_KEY = '12345678901234567890123456789012';

      // Clear module cache to force re-import
      jest.resetModules();

      // Import should throw an error due to invalid key length
      await expect(async () => {
        await import('../../../src/models/User.js');
      }).rejects.toThrow('Invalid encryption key length; must be 32 bytes for AES-256-GCM');
    });

    test('should throw error for base64 key with wrong length', async () => {
      // Set a base64 key that's not 32 bytes
      process.env.USER_DATA_ENCRYPTION_KEY = Buffer.from('short').toString('base64');

      // Clear module cache to force re-import
      jest.resetModules();

      // Import should throw an error due to invalid key length
      await expect(async () => {
        await import('../../../src/models/User.js');
      }).rejects.toThrow('Invalid encryption key length; must be 32 bytes for AES-256-GCM');
    });

    test('should work with valid 32-byte hex key', async () => {
      // Set a valid 32-byte hex key (64 hex characters)
      process.env.USER_DATA_ENCRYPTION_KEY = '1234567890123456789012345678901234567890123456789012345678901234';

      // Clear module cache to force re-import
      jest.resetModules();

      // Import should succeed
      const { User } = await import('../../../src/models/User.js');

      // Test that the User class works
      const user = new User({
        id: '123456789012345678',
        tag: 'TestUser#1234',
      });

      expect(user.id).toBe('123456789012345678');
    });

    test('should work with valid 32-byte base64 key', async () => {
      // Set a valid 32-byte base64 key
      process.env.USER_DATA_ENCRYPTION_KEY = Buffer.from('12345678901234567890123456789012').toString('base64');

      // Clear module cache to force re-import
      jest.resetModules();

      // Import should succeed
      const { User } = await import('../../../src/models/User.js');

      // Test that the User class works
      const user = new User({
        id: '123456789012345678',
        tag: 'TestUser#1234',
      });

      expect(user.id).toBe('123456789012345678');
    });
  });

  // ------------ ENCRYPTION EDGE CASES AND ERROR HANDLING
  describe('encryption edge cases and error handling', () => {
    test('should handle encryption errors by testing edge cases', async () => {
      // Set a valid key first
      process.env.USER_DATA_ENCRYPTION_KEY = '1234567890123456789012345678901234567890123456789012345678901234';

      // Clear module cache
      jest.resetModules();

      // Import the User module
      const { User, encrypt } = await import('../../../src/models/User.js');

      // Test 1: Try with extremely large input that might cause memory issues
      const user1 = new User({
        id: '123456789012345678',
        tag: 'TestUser#1234',
        email: 'a'.repeat(1000000), // 1MB string
      });

      const obj1 = user1.toObject();
      // This should either work or fail gracefully
      expect(typeof obj1.email === 'string' || obj1.email === null).toBe(true);

      // Test 2: Try with special characters that might cause encoding issues
      const specialChars = '\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0A\x0B\x0C\x0D\x0E\x0F';
      const user3 = new User({
        id: '123456789012345678',
        tag: 'TestUser#1234',
        email: `test${specialChars}@example.com`,
      });

      const obj3 = user3.toObject();
      expect(typeof obj3.email === 'string' || obj3.email === null).toBe(true);

      // Test 3: Try to call encrypt function directly with problematic input
      try {
        const result = encrypt('\x00\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0A\x0B\x0C\x0D\x0E\x0F');
        expect(typeof result === 'string' || result === null).toBe(true);
      } catch (error) {
        // If encrypt throws, that's also valid behavior
        expect(error).toBeInstanceOf(Error);
      }

      // Test 4: Test with binary data
      const binaryData = Buffer.from([0, 1, 2, 3, 4, 5, 6, 7, 8, 9]).toString('binary');
      const user5 = new User({
        id: '123456789012345678',
        tag: 'TestUser#1234',
        email: binaryData,
      });

      const obj5 = user5.toObject();
      expect(typeof obj5.email === 'string' || obj5.email === null).toBe(true);
    });

    test('should use hex key decoding path', async () => {
      // Set a 64-character hex key to trigger the hex decoding path
      process.env.USER_DATA_ENCRYPTION_KEY = '1234567890123456789012345678901234567890123456789012345678901234';

      // Clear module cache and import fresh
      jest.resetModules();

      // Import the User module which will trigger the hex key decoding
      const { User } = await import('../../../src/models/User.js');

      // Test that the User class works (indicating successful hex key decoding)
      const user = new User({
        id: '123456789012345678',
        tag: 'TestUser#1234',
        email: '<EMAIL>',
      });

      expect(user.id).toBe('123456789012345678');
      expect(user.email).toBe('<EMAIL>');
    });

    test('should handle base64 key decoding', async () => {
      // Set a valid 32-byte base64 key
      process.env.USER_DATA_ENCRYPTION_KEY = Buffer.from('12345678901234567890123456789012').toString('base64');

      // Clear module cache to force re-import
      jest.resetModules();

      // Import should succeed
      const { User } = await import('../../../src/models/User.js');

      // Test that the User class works
      const user = new User({
        id: '123456789012345678',
        tag: 'TestUser#1234',
      });

      expect(user.id).toBe('123456789012345678');
    });

    test('should handle various encryption edge cases', async () => {
      // Set a valid key
      process.env.USER_DATA_ENCRYPTION_KEY = '1234567890123456789012345678901234567890123456789012345678901234';

      // Clear module cache
      jest.resetModules();

      // Import User
      const { User } = await import('../../../src/models/User.js');

      // Test with various edge case emails
      const edgeCases = [
        '', // Empty string
        null, // Null
        undefined, // Undefined
        '   ', // Whitespace
        'a'.repeat(1000), // Very long string
        '🎉@example.com', // Unicode
      ];

      edgeCases.forEach((email, index) => {
        const user = new User({
          id: `12345678901234567${index}`,
          tag: `TestUser#123${index}`,
          email: email,
        });

        const obj = user.toObject();

        if (!email) {
          expect(obj.email).toBeNull();
        } else {
          expect(typeof obj.email === 'string' || obj.email === null).toBe(true);
        }
      });
    });
  });
});
