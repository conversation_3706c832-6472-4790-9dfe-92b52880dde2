// @ts-check
/**
 * @file MESSAGE.TEST.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * Unit tests for Message model.
 * Tests message creation, validation, and formatting.
 */

// ------------ IMPORTS
import { beforeEach, describe, expect, test } from '@jest/globals';

import { Message } from '../../../src/models/Message.js';
import { messageFixtures } from '../../helpers/fixtures.js';

// ------------ TEST SETUP
describe('Message Model', () => {
  let validMessageData;

  beforeEach(() => {
    validMessageData = { ...messageFixtures.incomingMessage };
  });

  // ------------ CONSTRUCTOR TESTS
  describe('constructor', () => {
    test('should create message with valid data', () => {
      const message = new Message(validMessageData);

      expect(message.id).toBe(validMessageData.id);
      expect(message.userId).toBe(validMessageData.userId);
      expect(message.content).toBe(validMessageData.content);
      expect(message.type).toBe(validMessageData.type);
      expect(message.authorId).toBe(validMessageData.authorId);
      expect(message.timestamp).toBeInstanceOf(Date);
      expect(message.metadata).toEqual(validMessageData.metadata);
    });

    test('should set default timestamp if not provided', () => {
      const messageData = { ...validMessageData };
      delete messageData.timestamp;

      const message = new Message(messageData);

      expect(message.timestamp).toBeInstanceOf(Date);
      expect(message.timestamp.getTime()).toBeCloseTo(Date.now(), -2); // Within 100ms
    });

    test('should set default metadata if not provided', () => {
      const messageData = { ...validMessageData };
      delete messageData.metadata;

      const message = new Message(messageData);

      expect(message.metadata).toEqual({});
    });

    test('should handle undefined authorId', () => {
      const messageData = { ...validMessageData, authorId: undefined };

      const message = new Message(messageData);

      expect(message.authorId).toBeUndefined();
    });
  });

  // ------------ TO OBJECT TESTS
  describe('toObject', () => {
    test('should convert message to plain object', () => {
      const message = new Message(validMessageData);
      const obj = message.toObject();

      expect(obj.id).toBe(validMessageData.id);
      expect(obj.userId).toBe(validMessageData.userId);
      expect(obj.content).toBe(validMessageData.content);
      expect(obj.type).toBe(validMessageData.type);
      expect(obj.authorId).toBe(validMessageData.authorId);
      expect(obj.timestamp).toBeInstanceOf(Date);
      expect(obj.metadata).toEqual(validMessageData.metadata);
    });

    test('should preserve all properties in object conversion', () => {
      const message = new Message(validMessageData);
      const obj = message.toObject();

      // Check that all properties are preserved
      expect(Object.keys(obj)).toEqual([
        'id',
        'userId',
        'content',
        'type',
        'authorId',
        'timestamp',
        'metadata',
      ]);
    });
  });

  // ------------ STATIC METHODS TESTS
  describe('static methods', () => {
    describe('createIncoming', () => {
      test('should create incoming message', () => {
        const userId = '123456789012345678';
        const content = 'Hello from user';
        const metadata = { source: 'dm' };

        const message = Message.createIncoming(userId, content, metadata);

        expect(message.userId).toBe(userId);
        expect(message.content).toBe(content);
        expect(message.type).toBe('incoming');
        expect(message.authorId).toBeUndefined();
        expect(message.timestamp).toBeInstanceOf(Date);
        expect(message.metadata).toEqual(metadata);
        expect(message.id).toMatch(new RegExp(`^${userId}-\\d+$`));
      });

      test('should create incoming message with default metadata', () => {
        const userId = '123456789012345678';
        const content = 'Hello from user';

        const message = Message.createIncoming(userId, content);

        expect(message.metadata).toEqual({});
      });

      test('should generate unique IDs for concurrent messages', async () => {
        const userId = '123456789012345678';
        const content = 'Test message';

        const message1 = Message.createIncoming(userId, content);
        // Wait 1ms to ensure different timestamp
        await new Promise(resolve => setTimeout(resolve, 1));
        const message2 = Message.createIncoming(userId, content);

        expect(message1.id).not.toBe(message2.id);
        expect(message1.id).toMatch(new RegExp(`^${userId}-\\d+$`));
        expect(message2.id).toMatch(new RegExp(`^${userId}-\\d+$`));
      });
    });

    describe('createOutgoing', () => {
      test('should create outgoing message with author', () => {
        const userId = '123456789012345678';
        const content = 'Hello from admin';
        const authorId = '987654321098765432';
        const metadata = { command: 'message' };

        const message = Message.createOutgoing(userId, content, authorId, metadata);

        expect(message.userId).toBe(userId);
        expect(message.content).toBe(content);
        expect(message.type).toBe('outgoing');
        expect(message.authorId).toBe(authorId);
        expect(message.timestamp).toBeInstanceOf(Date);
        expect(message.metadata).toEqual(metadata);
        expect(message.id).toMatch(new RegExp(`^${userId}-\\d+$`));
      });

      test('should create outgoing message without author (bot message)', () => {
        const userId = '123456789012345678';
        const content = 'Hello from bot';

        const message = Message.createOutgoing(userId, content);

        expect(message.userId).toBe(userId);
        expect(message.content).toBe(content);
        expect(message.type).toBe('outgoing');
        expect(message.authorId).toBeUndefined();
        expect(message.metadata).toEqual({});
      });

      test('should handle null authorId', () => {
        const userId = '123456789012345678';
        const content = 'Hello from bot';

        const message = Message.createOutgoing(userId, content, null);

        expect(message.authorId).toBeUndefined();
      });

      test('should create outgoing message with default metadata', () => {
        const userId = '123456789012345678';
        const content = 'Hello from admin';
        const authorId = '987654321098765432';

        const message = Message.createOutgoing(userId, content, authorId);

        expect(message.metadata).toEqual({});
      });
    });

    describe('fromMySQL', () => {
      test('should create message from MySQL data', () => {
        const mysqlData = {
          id: 'msg-123456789012345678',
          userId: '123456789012345678',
          content: 'Test message from MySQL',
          type: 'incoming',
          authorId: undefined,
          timestamp: '2024-01-01T12:00:00Z',
          metadata: { source: 'test' },
        };

        const message = Message.fromMySQL(mysqlData);

        expect(message.id).toBe(mysqlData.id);
        expect(message.userId).toBe(mysqlData.userId);
        expect(message.content).toBe(mysqlData.content);
        expect(message.type).toBe(mysqlData.type);
        expect(message.authorId).toBe(mysqlData.authorId);
        expect(message.timestamp).toBeInstanceOf(Date);
        expect(message.timestamp.toISOString()).toBe('2024-01-01T12:00:00.000Z');
        expect(message.metadata).toEqual(mysqlData.metadata);
      });

      test('should handle Date object timestamp', () => {
        const timestamp = new Date('2024-01-01T12:00:00Z');
        const mysqlData = {
          id: 'msg-123456789012345678',
          userId: '123456789012345678',
          content: 'Test message',
          type: 'outgoing',
          timestamp,
          metadata: {},
        };

        const message = Message.fromMySQL(mysqlData);

        expect(message.timestamp).toBe(timestamp);
      });

      test('should handle string timestamp', () => {
        const timestampString = '2024-01-01T12:00:00Z';
        const mysqlData = {
          id: 'msg-123456789012345678',
          userId: '123456789012345678',
          content: 'Test message',
          type: 'outgoing',
          timestamp: timestampString,
          metadata: {},
        };

        const message = Message.fromMySQL(mysqlData);

        expect(message.timestamp).toBeInstanceOf(Date);
        expect(message.timestamp.toISOString()).toBe('2024-01-01T12:00:00.000Z');
      });
    });
  });

  // ------------ MESSAGE TYPE TESTS
  describe('message types', () => {
    test('should handle incoming message type', () => {
      const messageData = { ...validMessageData, type: 'incoming' };
      const message = new Message(messageData);

      expect(message.type).toBe('incoming');
    });

    test('should handle outgoing message type', () => {
      const messageData = { ...validMessageData, type: 'outgoing' };
      const message = new Message(messageData);

      expect(message.type).toBe('outgoing');
    });
  });

  // ------------ METADATA TESTS
  describe('metadata handling', () => {
    test('should store complex metadata', () => {
      const complexMetadata = {
        command: 'broadcast',
        role: 'verified',
        title: 'Important Announcement',
        nested: {
          data: true,
          count: 42,
        },
      };

      const messageData = { ...validMessageData, metadata: complexMetadata };
      const message = new Message(messageData);

      expect(message.metadata).toEqual(complexMetadata);
    });

    test('should handle empty metadata', () => {
      const messageData = { ...validMessageData, metadata: {} };
      const message = new Message(messageData);

      expect(message.metadata).toEqual({});
    });
  });

  // ------------ EDGE CASES TESTS
  describe('edge cases', () => {
    test('should handle very long content', () => {
      const longContent = 'a'.repeat(2000);
      const messageData = { ...validMessageData, content: longContent };
      const message = new Message(messageData);

      expect(message.content).toBe(longContent);
      expect(message.content.length).toBe(2000);
    });

    test('should handle special characters in content', () => {
      const specialContent = '🎉 Hello! @everyone #general <@123> <#456> <:emoji:789>';
      const messageData = { ...validMessageData, content: specialContent };
      const message = new Message(messageData);

      expect(message.content).toBe(specialContent);
    });

    test('should handle empty content', () => {
      const messageData = { ...validMessageData, content: '' };
      const message = new Message(messageData);

      expect(message.content).toBe('');
    });
  });
});
