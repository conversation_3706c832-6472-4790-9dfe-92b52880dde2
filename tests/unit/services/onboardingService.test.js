// @ts-check
/**
 * @file ONBOARDING-SERVICE.TEST.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * Unit tests for onboarding service error messages.
 * Tests error message content, format, and validation.
 */

// ------------ IMPORTS
import { describe, expect, test } from '@jest/globals';

import { messages } from '../../../src/config/messages.js';


// Mock the getValidationErrorMessage function since it's not exported
function getValidationErrorMessage(validation) {
  switch (validation) {
    case 'email':
      return messages.errors.invalidEmail;
    case 'nickname':
      return messages.errors.invalidNickname;
    case 'robloxUsername':
      return messages.errors.invalidRobloxUsername;
    case 'timezone':
      return messages.errors.invalidTimezone;
    case 'yesno':
      return messages.errors.invalidYesNo;
    default:
      return messages.errors.internalError;
  }
}

describe('Onboarding Service Error Messages', () => {
  describe('Error Message Validation', () => {
    test('should have all required error messages defined', () => {
      expect(messages.errors.invalidEmail).toBeDefined();
      expect(messages.errors.invalidRobloxUsername).toBeDefined();
      expect(messages.errors.invalidNickname).toBeDefined();
      expect(messages.errors.invalidTimezone).toBeDefined();
      expect(messages.errors.invalidYesNo).toBeDefined();
    });

    test('should have proper email error message', () => {
      expect(messages.errors.invalidEmail).toContain('Invalid Email Format');
      expect(messages.errors.invalidEmail).toContain('<EMAIL>');
    });

    test('should have proper Roblox username error message', () => {
      expect(messages.errors.invalidRobloxUsername).toContain('Invalid Roblox Username');
      expect(messages.errors.invalidRobloxUsername).toContain('3-20 characters');
      expect(messages.errors.invalidRobloxUsername).toContain('skip');
    });

    test('should have proper nickname error message', () => {
      expect(messages.errors.invalidNickname).toContain('Invalid Nickname');
      expect(messages.errors.invalidNickname).toContain('valid name');
    });

    test('should have proper timezone error message', () => {
      expect(messages.errors.invalidTimezone).toContain('Invalid Timezone');
      expect(messages.errors.invalidTimezone).toContain('UTC+01:00');
      expect(messages.errors.invalidTimezone).toContain('GMT-5');
    });

    test('should have proper yes/no error message', () => {
      expect(messages.errors.invalidYesNo).toContain('Invalid Response');
      expect(messages.errors.invalidYesNo).toContain('yes');
      expect(messages.errors.invalidYesNo).toContain('no');
    });
  });

  describe('Error Message Format', () => {
    test('all error messages should start with error emoji', () => {
      expect(messages.errors.invalidEmail).toMatch(/^\s*❌/);
      expect(messages.errors.invalidRobloxUsername).toMatch(/^\s*❌/);
      expect(messages.errors.invalidNickname).toMatch(/^\s*❌/);
      expect(messages.errors.invalidTimezone).toMatch(/^\s*❌/);
      expect(messages.errors.invalidYesNo).toMatch(/^\s*❌/);
    });

    test('all error messages should be strings', () => {
      expect(typeof messages.errors.invalidEmail).toBe('string');
      expect(typeof messages.errors.invalidRobloxUsername).toBe('string');
      expect(typeof messages.errors.invalidNickname).toBe('string');
      expect(typeof messages.errors.invalidTimezone).toBe('string');
      expect(typeof messages.errors.invalidYesNo).toBe('string');
    });

    test('all error messages should be non-empty', () => {
      expect(messages.errors.invalidEmail.trim()).not.toBe('');
      expect(messages.errors.invalidRobloxUsername.trim()).not.toBe('');
      expect(messages.errors.invalidNickname.trim()).not.toBe('');
      expect(messages.errors.invalidTimezone.trim()).not.toBe('');
      expect(messages.errors.invalidYesNo.trim()).not.toBe('');
    });
  });

  describe('Validation Error Message Function', () => {
    test('should return correct error message for email validation', () => {
      expect(getValidationErrorMessage('email')).toBe(messages.errors.invalidEmail);
    });

    test('should return correct error message for roblox username validation', () => {
      expect(getValidationErrorMessage('robloxUsername')).toBe(messages.errors.invalidRobloxUsername);
    });

    test('should return correct error message for nickname validation', () => {
      expect(getValidationErrorMessage('nickname')).toBe(messages.errors.invalidNickname);
    });

    test('should return correct error message for timezone validation', () => {
      expect(getValidationErrorMessage('timezone')).toBe(messages.errors.invalidTimezone);
    });

    test('should return correct error message for yes/no validation', () => {
      expect(getValidationErrorMessage('yesno')).toBe(messages.errors.invalidYesNo);
    });

    test('should return internal error for unknown validation type', () => {
      expect(getValidationErrorMessage('unknown')).toBe(messages.errors.internalError);
      expect(getValidationErrorMessage(null)).toBe(messages.errors.internalError);
      expect(getValidationErrorMessage(undefined)).toBe(messages.errors.internalError);
    });
  });
});
