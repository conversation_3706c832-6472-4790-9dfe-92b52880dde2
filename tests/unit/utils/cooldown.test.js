// @ts-check
/**
 * @file COOLDOWN.TEST.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * Unit tests for cooldown utility.
 * Tests cooldown management, expiration checking, and configuration.
 */

// ------------ IMPORTS
import { beforeEach, describe, expect, jest, test } from '@jest/globals';

import { check, getCooldownMs, getRemaining, isExpired, set } from '../../../src/utils/cooldown.js';

// ------------ TEST SETUP
describe('Cooldown Utility', () => {
  let testCounter = 0;

  beforeEach(() => {
    // Clear any existing cooldowns before each test
    jest.clearAllMocks();
    testCounter++;
  });

  // Helper function to generate unique keys for each test
  const getUniqueKey = (base) => `${base}-test-${testCounter}-${Date.now()}`;

  // ------------ CHECK FUNCTION TESTS
  describe('check function', () => {
    test('should return true for first use (no cooldown)', () => {
      const key = getUniqueKey('test-user-command');
      const cooldownMs = 5000;

      const result = check(key, cooldownMs);

      expect(result).toBe(true);
    });

    test('should return false when cooldown is active', () => {
      const key = getUniqueKey('test-user-command');
      const cooldownMs = 5000;

      // First call should succeed and set cooldown
      const firstResult = check(key, cooldownMs);
      expect(firstResult).toBe(true);

      // Second call should fail due to cooldown
      const secondResult = check(key, cooldownMs);
      expect(secondResult).toBe(false);
    });

    test('should return true when cooldown has expired', async () => {
      const key = getUniqueKey('test-user-command');
      const cooldownMs = 10; // Very short cooldown for testing

      // First call sets cooldown
      const firstResult = check(key, cooldownMs);
      expect(firstResult).toBe(true);

      // Wait for cooldown to expire
      await new Promise(resolve => setTimeout(resolve, 15));

      // Should be able to use again
      const secondResult = check(key, cooldownMs);
      expect(secondResult).toBe(true);
    });

    test('should handle different keys independently', () => {
      const key1 = getUniqueKey('user1-command');
      const key2 = getUniqueKey('user2-command');
      const cooldownMs = 5000;

      const result1 = check(key1, cooldownMs);
      const result2 = check(key2, cooldownMs);

      expect(result1).toBe(true);
      expect(result2).toBe(true);
    });

    test('should update timestamp on successful check', () => {
      const key = getUniqueKey('test-user-command');
      const cooldownMs = 5000;

      const beforeTime = Date.now();
      check(key, cooldownMs);
      const afterTime = Date.now();

      // Check that remaining time is close to the cooldown duration
      const remaining = getRemaining(key, cooldownMs);
      expect(remaining).toBeGreaterThan(cooldownMs - (afterTime - beforeTime) - 10);
      expect(remaining).toBeLessThanOrEqual(cooldownMs);
    });
  });

  // ------------ IS EXPIRED FUNCTION TESTS
  describe('isExpired function', () => {
    test('should return true for non-existent key', () => {
      const key = getUniqueKey('non-existent-key');
      const cooldownMs = 5000;

      const result = isExpired(key, cooldownMs);

      expect(result).toBe(true);
    });

    test('should return false when cooldown is active', () => {
      const key = getUniqueKey('test-user-command');
      const cooldownMs = 5000;

      // Set cooldown using check
      check(key, cooldownMs);

      const result = isExpired(key, cooldownMs);
      expect(result).toBe(false);
    });

    test('should return true when cooldown has expired', async () => {
      const key = getUniqueKey('test-user-command');
      const cooldownMs = 10; // Very short cooldown

      // Set cooldown
      set(key);

      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 15));

      const result = isExpired(key, cooldownMs);
      expect(result).toBe(true);
    });

    test('should not modify cooldown state', () => {
      const key = getUniqueKey('test-user-command');
      const cooldownMs = 5000;

      // Set cooldown
      set(key);
      const remainingBefore = getRemaining(key, cooldownMs);

      // Check expiration (should not modify state)
      isExpired(key, cooldownMs);
      const remainingAfter = getRemaining(key, cooldownMs);

      // Remaining time should be approximately the same (allowing for small time differences)
      expect(Math.abs(remainingBefore - remainingAfter)).toBeLessThan(10);
    });
  });

  // ------------ SET FUNCTION TESTS
  describe('set function', () => {
    test('should set cooldown timestamp', () => {
      const key = getUniqueKey('test-user-command');
      const cooldownMs = 5000;

      const beforeTime = Date.now();
      set(key);
      const afterTime = Date.now();

      const remaining = getRemaining(key, cooldownMs);
      expect(remaining).toBeGreaterThan(cooldownMs - (afterTime - beforeTime) - 10);
      expect(remaining).toBeLessThanOrEqual(cooldownMs);
    });

    test('should update existing cooldown', async () => {
      const key = getUniqueKey('test-user-command');
      const cooldownMs = 5000;

      // Set initial cooldown
      set(key);
      const firstRemaining = getRemaining(key, cooldownMs);

      // Wait a bit and set again
      await new Promise(resolve => setTimeout(resolve, 10));
      set(key);
      const secondRemaining = getRemaining(key, cooldownMs);

      // Second remaining should be higher (closer to full cooldown)
      expect(secondRemaining).toBeGreaterThanOrEqual(firstRemaining);
    });
  });

  // ------------ GET REMAINING FUNCTION TESTS
  describe('getRemaining function', () => {
    test('should return 0 for non-existent key', () => {
      const key = getUniqueKey('non-existent-key');
      const cooldownMs = 5000;

      const remaining = getRemaining(key, cooldownMs);

      expect(remaining).toBe(0);
    });

    test('should return correct remaining time', () => {
      const key = getUniqueKey('test-user-command');
      const cooldownMs = 5000;

      set(key);
      const remaining = getRemaining(key, cooldownMs);

      expect(remaining).toBeGreaterThan(4900); // Should be close to 5000ms
      expect(remaining).toBeLessThanOrEqual(cooldownMs);
    });

    test('should return 0 when cooldown has expired', async () => {
      const key = getUniqueKey('test-user-command');
      const cooldownMs = 10; // Very short cooldown

      set(key);

      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 15));

      const remaining = getRemaining(key, cooldownMs);
      expect(remaining).toBe(0);
    });

    test('should decrease over time', async () => {
      const key = getUniqueKey('test-user-command');
      const cooldownMs = 1000; // 1 second

      set(key);
      const firstRemaining = getRemaining(key, cooldownMs);

      // Wait 100ms
      await new Promise(resolve => setTimeout(resolve, 100));

      const secondRemaining = getRemaining(key, cooldownMs);

      expect(secondRemaining).toBeLessThan(firstRemaining);
      expect(firstRemaining - secondRemaining).toBeGreaterThan(80); // Should be around 100ms difference
    });
  });

  // ------------ GET COOLDOWN MS FUNCTION TESTS
  describe('getCooldownMs function', () => {
    test('should return command-specific cooldown', () => {
      const pingCooldown = getCooldownMs('ping');
      const helpCooldown = getCooldownMs('help');
      const broadcastCooldown = getCooldownMs('broadcast');

      expect(pingCooldown).toBe(10000); // 10 seconds from actual config
      expect(helpCooldown).toBe(5000); // 5 seconds from actual config
      expect(broadcastCooldown).toBe(600000); // 10 minutes from actual config
    });

    test('should return default cooldown for unknown command', () => {
      const unknownCooldown = getCooldownMs('unknown-command');

      expect(unknownCooldown).toBe(3000); // Default from actual config
    });

    test('should handle empty command name', () => {
      const emptyCooldown = getCooldownMs('');

      expect(emptyCooldown).toBe(3000); // Default from actual config
    });

    test('should handle null/undefined command name', () => {
      const nullCooldown = getCooldownMs(null);
      const undefinedCooldown = getCooldownMs(undefined);

      expect(nullCooldown).toBe(3000);
      expect(undefinedCooldown).toBe(3000);
    });
  });

  // ------------ INTEGRATION TESTS
  describe('integration tests', () => {
    test('should work with realistic command usage pattern', async () => {
      const userId = '123456789012345678';
      const commandName = 'ping';
      const key = getUniqueKey(`${commandName}:${userId}`);
      const cooldownMs = getCooldownMs(commandName);

      // First use should succeed
      expect(check(key, cooldownMs)).toBe(true);
      expect(isExpired(key, cooldownMs)).toBe(false);

      // Immediate second use should fail
      expect(check(key, cooldownMs)).toBe(false);
      expect(isExpired(key, cooldownMs)).toBe(false);

      // Should have remaining time
      const remaining = getRemaining(key, cooldownMs);
      expect(remaining).toBeGreaterThan(0);
      expect(remaining).toBeLessThanOrEqual(cooldownMs);
    });

    test('should handle multiple users with same command', () => {
      const user1Key = getUniqueKey('ping:user1');
      const user2Key = getUniqueKey('ping:user2');
      const cooldownMs = getCooldownMs('ping');

      // Both users should be able to use command initially
      expect(check(user1Key, cooldownMs)).toBe(true);
      expect(check(user2Key, cooldownMs)).toBe(true);

      // Both should be on cooldown now
      expect(check(user1Key, cooldownMs)).toBe(false);
      expect(check(user2Key, cooldownMs)).toBe(false);

      // Both should have remaining time
      expect(getRemaining(user1Key, cooldownMs)).toBeGreaterThan(0);
      expect(getRemaining(user2Key, cooldownMs)).toBeGreaterThan(0);
    });

    test('should handle different commands for same user', () => {
      const userId = '123456789012345678';
      const pingKey = getUniqueKey(`ping:${userId}`);
      const helpKey = getUniqueKey(`help:${userId}`);

      // User should be able to use both commands
      expect(check(pingKey, getCooldownMs('ping'))).toBe(true);
      expect(check(helpKey, getCooldownMs('help'))).toBe(true);

      // Both commands should be on cooldown
      expect(check(pingKey, getCooldownMs('ping'))).toBe(false);
      expect(check(helpKey, getCooldownMs('help'))).toBe(false);
    });
  });
});
