# Integration Tests

This directory contains integration tests that test the interaction between multiple components of the DIS Discord Bot.

## Directory Structure

```
tests/integration/
├── README.md                 # This file
├── commands/                 # Command integration tests
│   ├── admin/               # Admin command integration tests
│   ├── member/              # Member command integration tests
│   └── workflows/           # Command workflow tests
├── database/                # Database integration tests
│   ├── models/              # Model integration tests
│   └── transactions/        # Transaction tests
├── services/                # Service integration tests
│   ├── onboarding/          # Onboarding service tests
│   └── messaging/           # Messaging service tests
└── workflows/               # End-to-end workflow tests
    ├── user-onboarding/     # User onboarding workflows
    └── admin-workflows/     # Admin workflow tests
```

## Test Categories

### 1. Command Integration Tests (`commands/`)
Tests that verify commands work correctly with their dependencies:
- Database interactions
- Service integrations
- Permission checks
- Error handling across components

### 2. Database Integration Tests (`database/`)
Tests that verify database operations work correctly:
- Model interactions with actual database
- Transaction handling
- Data consistency
- Migration testing

### 3. Service Integration Tests (`services/`)
Tests that verify services work correctly with their dependencies:
- Service-to-service communication
- External API integrations
- Data flow between services

### 4. Workflow Integration Tests (`workflows/`)
End-to-end tests that verify complete user workflows:
- User registration and onboarding
- Admin management workflows
- Error recovery scenarios

## Running Integration Tests

```bash
# Run all integration tests
npm run test:integration

# Run specific integration test category
npm run test:integration -- --testPathPattern=commands
npm run test:integration -- --testPathPattern=database
npm run test:integration -- --testPathPattern=services
npm run test:integration -- --testPathPattern=workflows

# Run integration tests with coverage
npm run test:integration -- --coverage
```

## Test Environment

Integration tests should:
- Use test database instances
- Mock external APIs when appropriate
- Clean up after themselves
- Be independent and idempotent
- Test realistic scenarios

## Writing Integration Tests

### Best Practices

1. **Test Real Interactions**: Test actual component interactions, not mocked ones
2. **Use Test Data**: Create realistic test data that represents actual usage
3. **Clean Setup/Teardown**: Ensure tests don't interfere with each other
4. **Test Error Scenarios**: Include tests for error conditions and edge cases
5. **Performance Considerations**: Be mindful of test execution time

### Example Integration Test Structure

```javascript
describe('User Onboarding Integration', () => {
  beforeEach(async () => {
    // Set up test database
    // Create test Discord guild/user
    // Initialize services
  });

  afterEach(async () => {
    // Clean up test data
    // Reset services
  });

  test('should complete full user onboarding workflow', async () => {
    // Test the complete flow from user join to verification
  });
});
```

## Test Data Management

- Use factories for creating test data
- Ensure test data is isolated between tests
- Clean up test data after each test
- Use realistic data that matches production scenarios

## Continuous Integration

Integration tests are run as part of the CI/CD pipeline to ensure:
- All components work together correctly
- Database migrations don't break existing functionality
- Service integrations remain functional
- Performance doesn't degrade significantly
