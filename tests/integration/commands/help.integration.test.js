// @ts-check
/**
 * @file HELP.INTEGRATION.TEST.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Integration tests for help command with actual Discord.js interactions.
 * Tests command execution with real dependencies.
 */

// ------------ IMPORTS
import { beforeEach, afterEach, describe, expect, test } from '@jest/globals';

import { 
  createMockInteraction, 
  createMockRole,
  setupTestEnvironment 
} from '../../helpers/integrationTestSetup.js';

// Set up test environment
setupTestEnvironment();

// Import command after environment setup
const { default: helpCommand } = await import('../../../src/commands/member/help.js');

describe('Help Command Integration Tests', () => {
  let mockInteraction;

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
  });

  // ------------ BASIC EXECUTION TESTS
  describe('command execution', () => {
    test('should execute help command for regular member', async () => {
      mockInteraction = createMockInteraction({
        commandName: 'help',
        userId: 'member-123',
        userRoles: [createMockRole({ name: 'Member' })],
        isAdmin: false
      });

      await expect(helpCommand.execute(mockInteraction)).resolves.not.toThrow();
      
      expect(mockInteraction.reply).toHaveBeenCalledTimes(1);
      
      const replyCall = mockInteraction.reply.mock.calls[0][0];
      expect(replyCall).toHaveProperty('embeds');
      expect(Array.isArray(replyCall.embeds)).toBe(true);
      expect(replyCall.embeds.length).toBeGreaterThan(0);
    });

    test('should execute help command for HR user', async () => {
      mockInteraction = createMockInteraction({
        commandName: 'help',
        userId: 'hr-123',
        userRoles: [
          createMockRole({ name: 'HR', id: 'hr-role-id' }),
          createMockRole({ name: 'Member' })
        ],
        isAdmin: false
      });

      await expect(helpCommand.execute(mockInteraction)).resolves.not.toThrow();
      
      expect(mockInteraction.reply).toHaveBeenCalledTimes(1);
      
      const replyCall = mockInteraction.reply.mock.calls[0][0];
      expect(replyCall.embeds[0].fields.length).toBeGreaterThan(2); // Should have more commands for HR
    });

    test('should execute help command for admin user', async () => {
      mockInteraction = createMockInteraction({
        commandName: 'help',
        userId: 'admin-123',
        userRoles: [
          createMockRole({ name: 'Administrator', id: 'admin-role-id' }),
          createMockRole({ name: 'Member' })
        ],
        isAdmin: true
      });

      await expect(helpCommand.execute(mockInteraction)).resolves.not.toThrow();
      
      expect(mockInteraction.reply).toHaveBeenCalledTimes(1);
      
      const replyCall = mockInteraction.reply.mock.calls[0][0];
      expect(replyCall.embeds[0].fields.length).toBeGreaterThan(4); // Should have most commands for admin
    });
  });

  // ------------ PERMISSION INTEGRATION TESTS
  describe('permission integration', () => {
    test('should show different commands based on user roles', async () => {
      // Test member permissions
      const memberInteraction = createMockInteraction({
        commandName: 'help',
        userId: 'member-123',
        userRoles: [createMockRole({ name: 'Member' })],
        isAdmin: false
      });

      await helpCommand.execute(memberInteraction);
      const memberReply = memberInteraction.reply.mock.calls[0][0];
      const memberCommands = memberReply.embeds[0].fields.map(field => field.name);

      // Test admin permissions
      const adminInteraction = createMockInteraction({
        commandName: 'help',
        userId: 'admin-123',
        userRoles: [
          createMockRole({ name: 'Administrator' }),
          createMockRole({ name: 'Member' })
        ],
        isAdmin: true
      });

      await helpCommand.execute(adminInteraction);
      const adminReply = adminInteraction.reply.mock.calls[0][0];
      const adminCommands = adminReply.embeds[0].fields.map(field => field.name);

      // Admin should have more commands than member
      expect(adminCommands.length).toBeGreaterThan(memberCommands.length);
      
      // Member commands should be subset of admin commands
      memberCommands.forEach(command => {
        expect(adminCommands).toContain(command);
      });
    });

    test('should handle users with no roles gracefully', async () => {
      mockInteraction = createMockInteraction({
        commandName: 'help',
        userId: 'norole-123',
        userRoles: [],
        isAdmin: false
      });

      await expect(helpCommand.execute(mockInteraction)).resolves.not.toThrow();
      
      expect(mockInteraction.reply).toHaveBeenCalledTimes(1);
      
      const replyCall = mockInteraction.reply.mock.calls[0][0];
      expect(replyCall.embeds[0].fields.length).toBeGreaterThan(0); // Should still show basic commands
    });
  });

  // ------------ EMBED STRUCTURE TESTS
  describe('embed structure validation', () => {
    test('should create properly formatted embed', async () => {
      mockInteraction = createMockInteraction({
        commandName: 'help',
        userId: 'test-123'
      });

      await helpCommand.execute(mockInteraction);
      
      const replyCall = mockInteraction.reply.mock.calls[0][0];
      const embed = replyCall.embeds[0];

      // Validate embed structure
      expect(embed).toHaveProperty('title');
      expect(embed).toHaveProperty('description');
      expect(embed).toHaveProperty('fields');
      expect(embed).toHaveProperty('color');
      expect(embed).toHaveProperty('footer');

      // Validate fields structure
      embed.fields.forEach(field => {
        expect(field).toHaveProperty('name');
        expect(field).toHaveProperty('value');
        expect(field).toHaveProperty('inline');
        expect(typeof field.name).toBe('string');
        expect(typeof field.value).toBe('string');
        expect(typeof field.inline).toBe('boolean');
      });
    });

    test('should include footer with proper information', async () => {
      mockInteraction = createMockInteraction({
        commandName: 'help',
        userId: 'test-123'
      });

      await helpCommand.execute(mockInteraction);
      
      const replyCall = mockInteraction.reply.mock.calls[0][0];
      const embed = replyCall.embeds[0];

      expect(embed.footer).toHaveProperty('text');
      expect(embed.footer.text).toContain('DIS Bot');
    });
  });

  // ------------ ERROR HANDLING TESTS
  describe('error handling', () => {
    test('should handle interaction reply errors gracefully', async () => {
      mockInteraction = createMockInteraction({
        commandName: 'help',
        userId: 'test-123'
      });

      // Mock reply to throw error
      mockInteraction.reply.mockRejectedValueOnce(new Error('Discord API Error'));

      // Should not throw, but handle error internally
      await expect(helpCommand.execute(mockInteraction)).resolves.not.toThrow();
    });

    test('should handle malformed interaction object', async () => {
      const malformedInteraction = {
        member: null,
        reply: jest.fn().mockResolvedValue({ id: 'reply-123' })
      };

      // Should handle gracefully without throwing
      await expect(helpCommand.execute(malformedInteraction)).resolves.not.toThrow();
    });
  });

  // ------------ PERFORMANCE TESTS
  describe('performance characteristics', () => {
    test('should execute within reasonable time', async () => {
      mockInteraction = createMockInteraction({
        commandName: 'help',
        userId: 'test-123'
      });

      const startTime = Date.now();
      await helpCommand.execute(mockInteraction);
      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(100); // Should complete within 100ms
    });

    test('should handle multiple concurrent executions', async () => {
      const interactions = Array.from({ length: 5 }, (_, i) => 
        createMockInteraction({
          commandName: 'help',
          userId: `test-${i}`
        })
      );

      const executions = interactions.map(interaction => 
        helpCommand.execute(interaction)
      );

      await expect(Promise.all(executions)).resolves.not.toThrow();
      
      // All interactions should have been replied to
      interactions.forEach(interaction => {
        expect(interaction.reply).toHaveBeenCalledTimes(1);
      });
    });
  });
});
