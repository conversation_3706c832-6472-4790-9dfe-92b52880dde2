// @ts-check
/**
 * @file PING.INTEGRATION.TEST.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * Integration tests for ping command.
 * Tests the complete flow from command execution to response.
 */

// ------------ IMPORTS
import { beforeEach, afterEach, describe, expect, test, jest } from '@jest/globals';

// Set test environment
process.env.NODE_ENV = 'test';
process.env.USER_DATA_ENCRYPTION_KEY = 'test-encryption-key-32-chars-lo!';

// ------------ TEST SETUP
describe('Ping Command Integration', () => {
  let mockInteraction;
  let pingCommand;

  beforeEach(async () => {
    // Clear module cache to ensure fresh imports
    jest.resetModules();
    
    // Import the ping command
    const { default: ping } = await import('../../../src/commands/member/ping.js');
    pingCommand = ping;

    // Create mock interaction
    mockInteraction = {
      user: {
        id: 'test-user-id',
        tag: 'TestUser#1234',
        username: 'TestUser',
        displayName: 'Test User',
      },
      member: {
        id: 'test-user-id',
        user: {
          id: 'test-user-id',
          tag: 'TestUser#1234',
          username: 'TestUser',
          displayName: 'Test User',
        },
        roles: {
          cache: new Map(),
        },
        permissions: {
          has: jest.fn(() => true),
        },
      },
      guild: {
        id: 'test-guild-id',
        name: 'Test Guild',
      },
      channel: {
        id: 'test-channel-id',
        name: 'test-channel',
        send: jest.fn(),
      },
      channelId: 'test-channel-id',
      guildId: 'test-guild-id',
      commandName: 'ping',
      options: {
        getString: jest.fn(),
        getUser: jest.fn(),
        getRole: jest.fn(),
        getInteger: jest.fn(),
        getBoolean: jest.fn(),
      },
      reply: jest.fn(),
      editReply: jest.fn(),
      followUp: jest.fn(),
      deferReply: jest.fn(),
      deferred: false,
      replied: false,
      createdTimestamp: Date.now(),
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  // ------------ INTEGRATION TESTS
  describe('command execution', () => {
    test('should execute ping command successfully', async () => {
      // Execute the command
      await pingCommand.execute(mockInteraction);

      // Verify the command replied
      expect(mockInteraction.reply).toHaveBeenCalled();
      
      // Get the reply content
      const replyCall = mockInteraction.reply.mock.calls[0][0];
      
      // Verify reply structure
      expect(replyCall).toHaveProperty('embeds');
      expect(Array.isArray(replyCall.embeds)).toBe(true);
      expect(replyCall.embeds.length).toBe(1);
      
      const embed = replyCall.embeds[0];
      expect(embed).toHaveProperty('title');
      expect(embed).toHaveProperty('description');
      expect(embed).toHaveProperty('color');
      expect(embed).toHaveProperty('timestamp');
      expect(embed).toHaveProperty('footer');
    });

    test('should include correct ping information', async () => {
      // Execute the command
      await pingCommand.execute(mockInteraction);

      // Get the reply content
      const replyCall = mockInteraction.reply.mock.calls[0][0];
      const embed = replyCall.embeds[0];

      // Verify ping information is included
      expect(embed.title).toContain('🏓');
      expect(embed.description).toContain('Pong!');
      expect(embed.description).toContain('ms');
      
      // Verify footer contains user information
      expect(embed.footer.text).toContain('TestUser#1234');
    });

    test('should handle command data correctly', () => {
      // Verify command data structure
      expect(pingCommand.data).toBeDefined();
      expect(pingCommand.data.name).toBe('ping');
      expect(pingCommand.data.description).toBeDefined();
      expect(typeof pingCommand.data.description).toBe('string');
    });

    test('should have correct command properties', () => {
      // Verify command properties
      expect(pingCommand).toHaveProperty('data');
      expect(pingCommand).toHaveProperty('execute');
      expect(typeof pingCommand.execute).toBe('function');
      
      // Verify command metadata
      expect(pingCommand.data.name).toBe('ping');
      expect(pingCommand.data.description.length).toBeGreaterThan(0);
    });
  });

  // ------------ ERROR HANDLING TESTS
  describe('error handling', () => {
    test('should handle reply errors gracefully', async () => {
      // Mock reply to throw an error
      mockInteraction.reply.mockRejectedValue(new Error('Discord API Error'));

      // Execute command and expect it to handle the error
      await expect(pingCommand.execute(mockInteraction)).rejects.toThrow();
    });

    test('should handle missing interaction properties', async () => {
      // Create interaction with missing properties
      const incompleteInteraction = {
        reply: jest.fn(),
        createdTimestamp: Date.now(),
      };

      // Execute command - should handle gracefully
      await pingCommand.execute(incompleteInteraction);
      
      // Should still attempt to reply
      expect(incompleteInteraction.reply).toHaveBeenCalled();
    });
  });

  // ------------ PERFORMANCE TESTS
  describe('performance', () => {
    test('should execute within reasonable time', async () => {
      const startTime = Date.now();
      
      await pingCommand.execute(mockInteraction);
      
      const executionTime = Date.now() - startTime;
      
      // Should execute within 100ms
      expect(executionTime).toBeLessThan(100);
    });

    test('should handle multiple concurrent executions', async () => {
      const promises = [];
      
      // Create multiple mock interactions
      for (let i = 0; i < 5; i++) {
        const interaction = {
          ...mockInteraction,
          user: { ...mockInteraction.user, id: `test-user-${i}` },
          reply: jest.fn(),
          createdTimestamp: Date.now(),
        };
        
        promises.push(pingCommand.execute(interaction));
      }
      
      // All should complete successfully
      await Promise.all(promises);
      
      // Verify all interactions were handled
      promises.forEach((_, index) => {
        // Each interaction should have been replied to
        expect(promises.length).toBe(5);
      });
    });
  });
});
