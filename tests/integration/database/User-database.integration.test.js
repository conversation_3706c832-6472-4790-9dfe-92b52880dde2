// @ts-check
/**
 * @file USER-DATABASE.INTEGRATION.TEST.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * Integration tests for User model with actual database operations.
 * Tests the complete flow from User model to database storage and retrieval.
 */

// ------------ IMPORTS
import { afterEach, beforeEach, describe, expect, test } from '@jest/globals';

import { User } from '../../../src/models/User.js';
import { Database } from '../../../src/utils/Database.js';

// Set test environment
process.env.NODE_ENV = 'test';
process.env.USER_DATA_ENCRYPTION_KEY = 'test-encryption-key-32-chars-lo!';

// ------------ TEST SETUP
describe('User Model Database Integration', () => {
  let database;
  let testUserId;

  beforeEach(async () => {
    // Initialize test database connection
    database = new Database();
    await database.connect();

    // Generate unique test user ID
    testUserId = `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  });

  afterEach(async () => {
    // Clean up test data
    if (testUserId) {
      try {
        await database.query(
          'DELETE FROM dis_internal_workers WHERE id_dis_internal_workers = ?',
          [testUserId]
        );
      } catch (error) {
        // Ignore cleanup errors
      }
    }

    // Close database connection
    if (database) {
      await database.disconnect();
    }
  });

  // ------------ DATABASE STORAGE TESTS
  describe('database storage', () => {
    test('should store user data in database correctly', async () => {
      const userData = {
        id: testUserId,
        tag: 'TestUser#1234',
        email: '<EMAIL>',
        nickname: 'Test Nickname',
        robloxUsername: 'TestRobloxUser',
        verified: true,
        timeZone: 'UTC',
        onboardingStatus: 'completed'
      };

      const user = new User(userData);
      const userObj = user.toObject();

      // Insert user into database
      const query = `
        INSERT INTO dis_internal_workers (
          id_dis_internal_workers, tag, email, nickname, robloxUsername,
          verified, timeZone, onboardingStatus, joinedAt, lastInteraction
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      await database.query(query, [
        userObj.id_dis_internal_workers,
        userObj.tag,
        userObj.email,
        userObj.nickname,
        userObj.robloxUsername,
        userObj.verified,
        userObj.timeZone,
        userObj.onboardingStatus,
        userObj.joinedAt,
        userObj.lastInteraction
      ]);

      // Verify data was stored correctly
      const [storedUser] = await database.query(
        'SELECT * FROM dis_internal_workers WHERE id_dis_internal_workers = ?',
        [testUserId]
      );

      expect(storedUser).toBeDefined();
      expect(storedUser.id_dis_internal_workers).toBe(testUserId);
      expect(storedUser.tag).toBe(userData.tag);
      expect(storedUser.nickname).toBe(userData.nickname);
      expect(storedUser.robloxUsername).toBe(userData.robloxUsername);
      expect(storedUser.verified).toBe(1); // MySQL boolean as integer
      expect(storedUser.timeZone).toBe(userData.timeZone);
      expect(storedUser.onboardingStatus).toBe(userData.onboardingStatus);
    });

    test('should handle encrypted email storage and retrieval', async () => {
      const userData = {
        id: testUserId,
        tag: 'TestUser#1234',
        email: '<EMAIL>'
      };

      const user = new User(userData);
      const userObj = user.toObject();

      // Email should be encrypted in storage object
      expect(userObj.email).not.toBe(userData.email);
      expect(userObj.email).toBeTruthy();

      // Store in database
      await database.query(
        'INSERT INTO dis_internal_workers (id_dis_internal_workers, tag, email, joinedAt, lastInteraction) VALUES (?, ?, ?, ?, ?)',
        [userObj.id_dis_internal_workers, userObj.tag, userObj.email, userObj.joinedAt, userObj.lastInteraction]
      );

      // Retrieve from database
      const [storedUser] = await database.query(
        'SELECT * FROM dis_internal_workers WHERE id_dis_internal_workers = ?',
        [testUserId]
      );

      // Create User from database data
      const retrievedUser = User.fromMySQL(storedUser);

      // Email should be decrypted correctly
      expect(retrievedUser.email).toBe(userData.email);
      expect(retrievedUser.id).toBe(userData.id);
      expect(retrievedUser.tag).toBe(userData.tag);
    });

    test('should handle null email values correctly', async () => {
      const userData = {
        id: testUserId,
        tag: 'TestUser#1234',
        email: null
      };

      const user = new User(userData);
      const userObj = user.toObject();

      // Store in database
      await database.query(
        'INSERT INTO dis_internal_workers (id_dis_internal_workers, tag, email, joinedAt, lastInteraction) VALUES (?, ?, ?, ?, ?)',
        [userObj.id_dis_internal_workers, userObj.tag, userObj.email, userObj.joinedAt, userObj.lastInteraction]
      );

      // Retrieve from database
      const [storedUser] = await database.query(
        'SELECT * FROM dis_internal_workers WHERE id_dis_internal_workers = ?',
        [testUserId]
      );

      expect(storedUser.email).toBeNull();

      // Create User from database data
      const retrievedUser = User.fromMySQL(storedUser);
      expect(retrievedUser.email).toBeNull();
    });
  });

  // ------------ DATABASE RETRIEVAL TESTS
  describe('database retrieval', () => {
    test('should retrieve and reconstruct user correctly', async () => {
      // Insert test data directly into database
      const testData = {
        id_dis_internal_workers: testUserId,
        tag: 'TestUser#1234',
        email: null,
        nickname: 'Test Nickname',
        robloxUsername: 'TestRobloxUser',
        verified: 1,
        timeZone: 'America/New_York',
        onboardingStatus: 'completed',
        joinedAt: new Date('2024-01-01T00:00:00Z'),
        verifiedAt: new Date('2024-01-01T01:00:00Z'),
        lastInteraction: new Date('2024-01-01T02:00:00Z'),
        metadata: '{"source":"test","version":1}'
      };

      await database.query(`
        INSERT INTO dis_internal_workers (
          id_dis_internal_workers, tag, email, nickname, robloxUsername,
          verified, timeZone, onboardingStatus, joinedAt, verifiedAt,
          lastInteraction, metadata
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, Object.values(testData));

      // Retrieve and reconstruct user
      const [dbUser] = await database.query(
        'SELECT * FROM dis_internal_workers WHERE id_dis_internal_workers = ?',
        [testUserId]
      );

      const user = User.fromMySQL(dbUser);

      expect(user.id).toBe(testData.id_dis_internal_workers);
      expect(user.tag).toBe(testData.tag);
      expect(user.email).toBeNull();
      expect(user.nickname).toBe(testData.nickname);
      expect(user.robloxUsername).toBe(testData.robloxUsername);
      expect(user.verified).toBe(true); // Converted from 1
      expect(user.timeZone).toBe(testData.timeZone);
      expect(user.onboardingStatus).toBe(testData.onboardingStatus);
      expect(user.metadata).toEqual({ source: 'test', version: 1 });
    });

    test('should handle malformed JSON metadata gracefully', async () => {
      // Insert test data with invalid JSON metadata
      await database.query(`
        INSERT INTO dis_internal_workers (
          id_dis_internal_workers, tag, joinedAt, lastInteraction, metadata
        ) VALUES (?, ?, ?, ?, ?)
      `, [testUserId, 'TestUser#1234', new Date(), new Date(), 'invalid-json{']);

      // Retrieve and reconstruct user
      const [dbUser] = await database.query(
        'SELECT * FROM dis_internal_workers WHERE id_dis_internal_workers = ?',
        [testUserId]
      );

      const user = User.fromMySQL(dbUser);

      // Should handle invalid JSON gracefully
      expect(user.metadata).toBe('invalid-json{');
    });
  });

  // ------------ UPDATE OPERATIONS TESTS
  describe('update operations', () => {
    test('should update user data in database correctly', async () => {
      // Create and store initial user
      const initialData = {
        id: testUserId,
        tag: 'TestUser#1234',
        email: '<EMAIL>',
        verified: false,
        onboardingStatus: 'pending'
      };

      const user = new User(initialData);
      const userObj = user.toObject();

      await database.query(`
        INSERT INTO dis_internal_workers (
          id_dis_internal_workers, tag, email, verified, onboardingStatus,
          joinedAt, lastInteraction
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        userObj.id_dis_internal_workers,
        userObj.tag,
        userObj.email,
        userObj.verified,
        userObj.onboardingStatus,
        userObj.joinedAt,
        userObj.lastInteraction
      ]);

      // Update user
      user.update({
        email: '<EMAIL>',
        verified: true,
        onboardingStatus: 'completed'
      });

      const updatedObj = user.toObject();

      // Update in database
      await database.query(`
        UPDATE dis_internal_workers
        SET email = ?, verified = ?, onboardingStatus = ?, lastInteraction = ?
        WHERE id_dis_internal_workers = ?
      `, [
        updatedObj.email,
        updatedObj.verified,
        updatedObj.onboardingStatus,
        updatedObj.lastInteraction,
        testUserId
      ]);

      // Verify update
      const [updatedUser] = await database.query(
        'SELECT * FROM dis_internal_workers WHERE id_dis_internal_workers = ?',
        [testUserId]
      );

      const reconstructedUser = User.fromMySQL(updatedUser);

      expect(reconstructedUser.email).toBe('<EMAIL>');
      expect(reconstructedUser.verified).toBe(true);
      expect(reconstructedUser.onboardingStatus).toBe('completed');
    });
  });
});
