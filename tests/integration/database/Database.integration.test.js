// @ts-check
/**
 * @file DATABASE.INTEGRATION.TEST.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Integration tests for Database utility class.
 * Tests actual database connections and operations.
 */

// ------------ IMPORTS
import { beforeEach, afterEach, describe, expect, test } from '@jest/globals';

import { Database } from '../../../src/utils/Database.js';
import { setupTestEnvironment } from '../../helpers/integrationTestSetup.js';

// ------------ TEST SETUP
setupTestEnvironment();

describe('Database Integration Tests', () => {
  let database;

  beforeEach(async () => {
    database = new Database();
  });

  afterEach(async () => {
    if (database) {
      await database.disconnect();
    }
  });

  // ------------ CONNECTION TESTS
  describe('connection management', () => {
    test('should establish database connection successfully', async () => {
      await expect(database.connect()).resolves.not.toThrow();
      expect(database.isConnected()).toBe(true);
    });

    test('should handle multiple connection attempts gracefully', async () => {
      await database.connect();
      expect(database.isConnected()).toBe(true);
      
      // Second connection attempt should not throw
      await expect(database.connect()).resolves.not.toThrow();
      expect(database.isConnected()).toBe(true);
    });

    test('should disconnect properly', async () => {
      await database.connect();
      expect(database.isConnected()).toBe(true);
      
      await database.disconnect();
      expect(database.isConnected()).toBe(false);
    });

    test('should handle disconnect when not connected', async () => {
      expect(database.isConnected()).toBe(false);
      await expect(database.disconnect()).resolves.not.toThrow();
    });
  });

  // ------------ QUERY TESTS
  describe('query operations', () => {
    beforeEach(async () => {
      await database.connect();
    });

    test('should execute simple SELECT query', async () => {
      const result = await database.query('SELECT 1 as test_value');
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
      expect(result[0].test_value).toBe(1);
    });

    test('should execute parameterized query', async () => {
      const testValue = 'test_string';
      const result = await database.query('SELECT ? as test_value', [testValue]);
      expect(result[0].test_value).toBe(testValue);
    });

    test('should handle empty result sets', async () => {
      const result = await database.query(
        'SELECT * FROM dis_internal_workers WHERE id_dis_internal_workers = ?',
        ['non_existent_id']
      );
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(0);
    });

    test('should throw error for invalid SQL', async () => {
      await expect(
        database.query('INVALID SQL STATEMENT')
      ).rejects.toThrow();
    });
  });

  // ------------ TRANSACTION TESTS
  describe('transaction operations', () => {
    beforeEach(async () => {
      await database.connect();
    });

    test('should execute transaction successfully', async () => {
      const testId = `test_transaction_${Date.now()}`;
      
      await expect(database.transaction(async (connection) => {
        await connection.query(
          'INSERT INTO dis_internal_workers (id_dis_internal_workers, tag, joinedAt, lastInteraction) VALUES (?, ?, ?, ?)',
          [testId, 'TestUser#1234', new Date(), new Date()]
        );
        
        const [result] = await connection.query(
          'SELECT * FROM dis_internal_workers WHERE id_dis_internal_workers = ?',
          [testId]
        );
        
        expect(result.id_dis_internal_workers).toBe(testId);
        
        // Clean up
        await connection.query(
          'DELETE FROM dis_internal_workers WHERE id_dis_internal_workers = ?',
          [testId]
        );
      })).resolves.not.toThrow();
    });

    test('should rollback transaction on error', async () => {
      const testId = `test_rollback_${Date.now()}`;
      
      await expect(database.transaction(async (connection) => {
        await connection.query(
          'INSERT INTO dis_internal_workers (id_dis_internal_workers, tag, joinedAt, lastInteraction) VALUES (?, ?, ?, ?)',
          [testId, 'TestUser#1234', new Date(), new Date()]
        );
        
        // Force an error to trigger rollback
        throw new Error('Test error for rollback');
      })).rejects.toThrow('Test error for rollback');
      
      // Verify data was rolled back
      const result = await database.query(
        'SELECT * FROM dis_internal_workers WHERE id_dis_internal_workers = ?',
        [testId]
      );
      expect(result.length).toBe(0);
    });
  });

  // ------------ ERROR HANDLING TESTS
  describe('error handling', () => {
    test('should handle connection errors gracefully', async () => {
      // Create database with invalid config
      const invalidDb = new Database();
      
      // Mock invalid connection config
      const originalConfig = process.env.DB_HOST;
      process.env.DB_HOST = 'invalid-host-that-does-not-exist';
      
      await expect(invalidDb.connect()).rejects.toThrow();
      
      // Restore original config
      process.env.DB_HOST = originalConfig;
    });

    test('should throw error when querying without connection', async () => {
      const disconnectedDb = new Database();
      
      await expect(
        disconnectedDb.query('SELECT 1')
      ).rejects.toThrow();
    });
  });

  // ------------ PERFORMANCE TESTS
  describe('performance characteristics', () => {
    beforeEach(async () => {
      await database.connect();
    });

    test('should handle multiple concurrent queries', async () => {
      const queries = Array.from({ length: 10 }, (_, i) => 
        database.query('SELECT ? as query_number', [i])
      );
      
      const results = await Promise.all(queries);
      
      expect(results.length).toBe(10);
      results.forEach((result, index) => {
        expect(result[0].query_number).toBe(index);
      });
    });

    test('should complete simple queries within reasonable time', async () => {
      const startTime = Date.now();
      
      await database.query('SELECT 1');
      
      const duration = Date.now() - startTime;
      expect(duration).toBeLessThan(1000); // Should complete within 1 second
    });
  });
});
