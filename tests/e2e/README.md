# End-to-End (E2E) Tests

This directory contains end-to-end tests that simulate real user interactions with the DIS Discord Bot.

## Directory Structure

```
tests/e2e/
├── README.md                 # This file
├── workflows/               # Complete workflow tests
│   ├── user-onboarding/     # User onboarding E2E tests
│   ├── admin-management/    # Admin workflow tests
│   └── member-interactions/ # Member interaction tests
├── commands/                # Command E2E tests
│   ├── admin/              # Admin command E2E tests
│   ├── member/             # Member command E2E tests
│   └── system/             # System command E2E tests
└── scenarios/              # Specific scenario tests
    ├── error-handling/     # Error scenario tests
    ├── edge-cases/         # Edge case scenario tests
    └── performance/        # Performance scenario tests
```

## Test Categories

### 1. Workflow Tests (`workflows/`)
Complete end-to-end user workflows:
- **User Onboarding**: From Discord join to full verification
- **Admin Management**: Admin user management workflows
- **Member Interactions**: Typical member interaction patterns

### 2. Command Tests (`commands/`)
End-to-end command execution tests:
- **Admin Commands**: Full admin command workflows
- **Member Commands**: Member command interactions
- **System Commands**: System-level command testing

### 3. Scenario Tests (`scenarios/`)
Specific scenario and edge case testing:
- **Error Handling**: How the bot handles various error conditions
- **Edge Cases**: Unusual but valid usage patterns
- **Performance**: Load and performance testing scenarios

## Running E2E Tests

```bash
# Run all E2E tests
npm run test:e2e

# Run specific E2E test category
npm run test:e2e -- --testPathPattern=workflows
npm run test:e2e -- --testPathPattern=commands
npm run test:e2e -- --testPathPattern=scenarios

# Run E2E tests with coverage
npm run test:e2e -- --coverage

# Run E2E tests in watch mode
npm run test:e2e -- --watch
```

## Test Environment Setup

E2E tests require:
- Test Discord bot instance
- Test Discord server/guild
- Test database
- Mock external services (when appropriate)
- Test user accounts

### Environment Variables

```bash
# Test Discord Bot Configuration
DISCORD_BOT_TOKEN_TEST=your_test_bot_token
DISCORD_GUILD_ID_TEST=your_test_guild_id
DISCORD_CHANNEL_ID_TEST=your_test_channel_id

# Test Database Configuration
DATABASE_URL_TEST=your_test_database_url
USER_DATA_ENCRYPTION_KEY_TEST=your_test_encryption_key

# Test User Configuration
TEST_USER_ID=test_user_discord_id
TEST_ADMIN_USER_ID=test_admin_discord_id
```

## Writing E2E Tests

### Best Practices

1. **Simulate Real Usage**: Tests should mimic actual user behavior
2. **Complete Workflows**: Test entire user journeys, not just individual steps
3. **Clean State**: Each test should start with a clean, known state
4. **Realistic Data**: Use realistic test data that matches production scenarios
5. **Error Scenarios**: Include tests for error conditions and recovery
6. **Performance Awareness**: Monitor test execution time and bot responsiveness

### Example E2E Test Structure

```javascript
describe('User Onboarding E2E', () => {
  let testGuild;
  let testUser;
  let botClient;

  beforeAll(async () => {
    // Set up test Discord environment
    // Initialize bot client
    // Create test guild and user
  });

  afterAll(async () => {
    // Clean up test environment
    // Remove test data
    // Disconnect bot client
  });

  beforeEach(async () => {
    // Reset to clean state for each test
  });

  test('should complete full user onboarding workflow', async () => {
    // 1. Simulate user joining Discord server
    // 2. Bot should send welcome message
    // 3. User responds to onboarding prompts
    // 4. Bot processes responses and updates user status
    // 5. User completes verification
    // 6. Bot grants appropriate roles and permissions
    // 7. Verify final state is correct
  });
});
```

## Test Data Management

### Test User Management
- Create dedicated test Discord accounts
- Use test-specific user IDs and guild IDs
- Ensure test users have appropriate permissions
- Clean up test user data after tests

### Test Guild Setup
- Use dedicated test Discord server
- Configure test channels and roles
- Set up bot permissions correctly
- Isolate test environment from production

### Database Management
- Use separate test database
- Reset database state between test runs
- Use database transactions for isolation
- Clean up test data automatically

## Mocking and External Services

### When to Mock
- External APIs that are unreliable or expensive
- Services that are not part of the core functionality being tested
- Third-party integrations that have rate limits

### When NOT to Mock
- Discord API interactions (use test server instead)
- Database operations (use test database)
- Core bot functionality
- Internal service communications

## Continuous Integration

E2E tests in CI/CD:
- Run on dedicated test infrastructure
- Use containerized test environments
- Parallel execution where possible
- Comprehensive reporting and artifacts
- Automatic cleanup of test resources

## Performance Considerations

- Monitor test execution time
- Use parallel execution where safe
- Optimize test setup and teardown
- Consider test data size and complexity
- Profile bot performance during tests

## Debugging E2E Tests

### Common Issues
- Discord API rate limits
- Test environment state pollution
- Timing issues with async operations
- Test data conflicts

### Debugging Tools
- Detailed logging during test execution
- Screenshot/recording capabilities for visual tests
- Network request monitoring
- Database query logging
- Bot event logging

## Test Reporting

E2E tests should provide:
- Detailed execution reports
- Performance metrics
- Error screenshots/logs
- Coverage information
- Trend analysis over time
