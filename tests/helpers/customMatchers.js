// @ts-check
/**
 * @file CUSTOM-MATCHERS.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * Custom Jest matchers for Discord bot testing.
 * Provides specialized assertions for Discord.js objects and bot functionality.
 */

// ------------ IMPORTS
import { expect } from '@jest/globals';

// ------------ DISCORD INTERACTION MATCHERS

/**
 * Check if interaction was replied to with specific content
 */
expect.extend({
  toHaveRepliedWith(received, expectedContent) {
    const pass = received.reply.mock.calls.some(call => {
      const callArg = call[0];
      if (typeof callArg === 'string') {
        return callArg.includes(expectedContent);
      }
      if (typeof callArg === 'object' && callArg.content) {
        return callArg.content.includes(expectedContent);
      }
      return false;
    });

    return {
      message: () =>
        pass
          ? `Expected interaction not to have replied with "${expectedContent}"`
          : `Expected interaction to have replied with "${expectedContent}"`,
      pass,
    };
  },
});

/**
 * Check if interaction was deferred
 */
expect.extend({
  toHaveBeenDeferred(received) {
    const pass = received.deferReply.mock.calls.length > 0;

    return {
      message: () =>
        pass
          ? 'Expected interaction not to have been deferred'
          : 'Expected interaction to have been deferred',
      pass,
    };
  },
});

/**
 * Check if interaction reply was ephemeral
 */
expect.extend({
  toHaveRepliedEphemeral(received) {
    const pass = received.reply.mock.calls.some(call => {
      const callArg = call[0];
      return (
        typeof callArg === 'object' &&
        callArg.flags &&
        callArg.flags.includes(64) // MessageFlags.Ephemeral
      );
    });

    return {
      message: () =>
        pass
          ? 'Expected interaction not to have replied with ephemeral message'
          : 'Expected interaction to have replied with ephemeral message',
      pass,
    };
  },
});

// ------------ DISCORD EMBED MATCHERS

/**
 * Check if interaction was replied to with an embed
 */
expect.extend({
  toHaveRepliedWithEmbed(received, expectedEmbedProperties = {}) {
    const pass = received.reply.mock.calls.some(call => {
      const callArg = call[0];
      if (typeof callArg === 'object' && callArg.embeds && callArg.embeds.length > 0) {
        const embed = callArg.embeds[0];

        // Check if all expected properties match
        return Object.keys(expectedEmbedProperties).every(key => {
          if (key === 'color' && typeof expectedEmbedProperties[key] === 'string') {
            // Convert hex color to number for comparison
            const expectedColor = parseInt(expectedEmbedProperties[key].replace('#', ''), 16);
            return embed.data.color === expectedColor;
          }
          return embed.data[key] === expectedEmbedProperties[key];
        });
      }
      return false;
    });

    return {
      message: () =>
        pass
          ? 'Expected interaction not to have replied with matching embed'
          : `Expected interaction to have replied with embed matching ${JSON.stringify(expectedEmbedProperties)}`,
      pass,
    };
  },
});

// ------------ DISCORD MESSAGE MATCHERS

/**
 * Check if a DM was sent to a user
 */
expect.extend({
  toHaveSentDM(received, expectedContent) {
    const pass = received.send.mock.calls.some(call => {
      const callArg = call[0];
      if (typeof callArg === 'string') {
        return callArg.includes(expectedContent);
      }
      if (typeof callArg === 'object' && callArg.content) {
        return callArg.content.includes(expectedContent);
      }
      if (typeof callArg === 'object' && callArg.embeds) {
        return callArg.embeds.some(embed =>
          embed.data.description && embed.data.description.includes(expectedContent)
        );
      }
      return false;
    });

    return {
      message: () =>
        pass
          ? `Expected not to have sent DM with "${expectedContent}"`
          : `Expected to have sent DM with "${expectedContent}"`,
      pass,
    };
  },
});

// ------------ DATABASE MATCHERS

/**
 * Check if user was saved to database
 */
expect.extend({
  toHaveSavedUser(received, expectedUserId) {
    const pass = received.saveUser.mock.calls.some(call => {
      const user = call[0];
      return user && user.id === expectedUserId;
    });

    return {
      message: () =>
        pass
          ? `Expected not to have saved user with ID "${expectedUserId}"`
          : `Expected to have saved user with ID "${expectedUserId}"`,
      pass,
    };
  },
});

/**
 * Check if user onboarding status was updated
 */
expect.extend({
  toHaveUpdatedOnboardingStatus(received, expectedUserId, expectedStatus) {
    const pass = received.updateUserOnboardingStatus.mock.calls.some(call => {
      const [userId, updates] = call;
      return userId === expectedUserId && updates.onboardingStatus === expectedStatus;
    });

    return {
      message: () =>
        pass
          ? `Expected not to have updated onboarding status for user "${expectedUserId}" to "${expectedStatus}"`
          : `Expected to have updated onboarding status for user "${expectedUserId}" to "${expectedStatus}"`,
      pass,
    };
  },
});

// ------------ COOLDOWN MATCHERS

/**
 * Check if cooldown was set for a command
 */
expect.extend({
  toHaveSetCooldown(received, expectedKey) {
    // This would need to be implemented based on your cooldown system
    // For now, this is a placeholder
    const pass = true; // Implement based on your cooldown tracking

    return {
      message: () =>
        pass
          ? `Expected not to have set cooldown for "${expectedKey}"`
          : `Expected to have set cooldown for "${expectedKey}"`,
      pass,
    };
  },
});

// ------------ RATE LIMIT MATCHERS

/**
 * Check if rate limit was checked
 */
expect.extend({
  toHaveCheckedRateLimit(received, expectedContext) {
    // This would need to be implemented based on your rate limiting system
    // For now, this is a placeholder
    const pass = true; // Implement based on your rate limit tracking

    return {
      message: () =>
        pass
          ? `Expected not to have checked rate limit for context "${JSON.stringify(expectedContext)}"`
          : `Expected to have checked rate limit for context "${JSON.stringify(expectedContext)}"`,
      pass,
    };
  },
});

// ------------ ERROR MATCHERS

/**
 * Check if error was handled properly
 */
expect.extend({
  toHaveHandledError(received, expectedErrorType) {
    // This would need to be implemented based on your error handling system
    // For now, this is a placeholder
    const pass = true; // Implement based on your error handling tracking

    return {
      message: () =>
        pass
          ? `Expected not to have handled error of type "${expectedErrorType}"`
          : `Expected to have handled error of type "${expectedErrorType}"`,
      pass,
    };
  },
});

// ------------ VALIDATION MATCHERS

/**
 * Check if input was validated
 */
expect.extend({
  toBeValidEmail(received) {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    const pass = emailRegex.test(received);

    return {
      message: () =>
        pass
          ? `Expected "${received}" not to be a valid email`
          : `Expected "${received}" to be a valid email`,
      pass,
    };
  },
});

/**
 * Check if Discord ID is valid format
 */
expect.extend({
  toBeValidDiscordId(received) {
    const discordIdRegex = /^\d{17,19}$/;
    const pass = discordIdRegex.test(received);

    return {
      message: () =>
        pass
          ? `Expected "${received}" not to be a valid Discord ID`
          : `Expected "${received}" to be a valid Discord ID`,
      pass,
    };
  },
});

// ------------ PERMISSION MATCHERS

/**
 * Check if user has required role
 */
expect.extend({
  toHaveRole(received, expectedRoleId) {
    const pass = received.roles && received.roles.cache && received.roles.cache.has(expectedRoleId);

    return {
      message: () =>
        pass
          ? `Expected user not to have role "${expectedRoleId}"`
          : `Expected user to have role "${expectedRoleId}"`,
      pass,
    };
  },
});

/**
 * Check if user has required permission
 */
expect.extend({
  toHavePermission(received, expectedPermission) {
    const pass = received.permissions && received.permissions.has(expectedPermission);

    return {
      message: () =>
        pass
          ? `Expected user not to have permission "${expectedPermission}"`
          : `Expected user to have permission "${expectedPermission}"`,
      pass,
    };
  },
});

// ------------ EXPORT MATCHERS
export const customMatchers = {
  toHaveRepliedWith: expect.toHaveRepliedWith,
  toHaveBeenDeferred: expect.toHaveBeenDeferred,
  toHaveRepliedEphemeral: expect.toHaveRepliedEphemeral,
  toHaveRepliedWithEmbed: expect.toHaveRepliedWithEmbed,
  toHaveSentDM: expect.toHaveSentDM,
  toHaveSavedUser: expect.toHaveSavedUser,
  toHaveUpdatedOnboardingStatus: expect.toHaveUpdatedOnboardingStatus,
  toHaveSetCooldown: expect.toHaveSetCooldown,
  toHaveCheckedRateLimit: expect.toHaveCheckedRateLimit,
  toHaveHandledError: expect.toHaveHandledError,
  toBeValidEmail: expect.toBeValidEmail,
  toBeValidDiscordId: expect.toBeValidDiscordId,
  toHaveRole: expect.toHaveRole,
  toHavePermission: expect.toHavePermission,
};
