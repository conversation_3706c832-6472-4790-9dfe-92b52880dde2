// @ts-check
/**
 * @file TEST-SETUP.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * Jest test setup file for DIS Work Discord Bot.
 * Configures global test environment, mocks, and utilities.
 */

// ------------ <PERSON>MPORTS
import { jest } from '@jest/globals';
import './customMatchers.js';

// ------------ ENVIRONMENT SETUP
// Mock environment variables for testing
process.env.APP_ENV = 'test';
process.env.TOKEN = 'test-discord-token';
process.env.CLIENT_ID = 'test-client-id';
process.env.CLIENT_SECRET = 'test-client-secret';
process.env.APPLICATION_ID = 'test-application-id';
process.env.PUBLIC_KEY = 'test-public-key';
process.env.ADMIN_ROLE_ID = '1234567890123456789';
process.env.HR_ROLE_ID = '1234567890123456790';
process.env.VERIFIED_ROLE_ID = '1234567890123456791';
process.env.WELCOME_CHANNEL_ID = '1234567890123456792';
process.env.ADMIN_LOG_CHANNEL_ID = '1234567890123456793';
process.env.ERROR_LOG_CHANNEL_ID = '1234567890123456794';
process.env.GENERAL_CHANNEL_ID = '1234567890123456795';
process.env.DB_HOST = 'localhost';
process.env.DB_USERNAME = 'test_user';
process.env.DB_PASSWORD = 'test_password';
process.env.DB_DATABASE = 'test_db';
process.env.DB_PORT = '3306';
process.env.USER_DATA_ENCRYPTION_KEY = 'MW9T3w8m1zPIDI3ZbH0kpOOcvkssg7WtWz+ZL46HgBc=';
process.env.ROBLOX_CLIENT_ID = 'test-roblox-client-id';
process.env.ROBLOX_CLIENT_SECRET = 'test-roblox-client-secret';
process.env.ROBLOX_ASSETS_API_KEY = 'test-roblox-assets-api-key';
process.env.CIRRUS_API_KEY = 'test-cirrus-api-key';
process.env.CIRRUS_LOG_API_KEY = 'test-cirrus-log-api-key';
process.env.SENTRY_DSN = 'https://<EMAIL>/test';
process.env.VERSION = '3.1.1';
process.env.HEALTH_CHECK_INTERVAL = '60000';
process.env.HEALTH_PORT = '3000';
process.env.ENABLE_HEALTH_ENDPOINT = 'false';

// ------------ CONSOLE MANAGEMENT
// Store original console for restoration
const originalConsole = { ...console };

// Override console for cleaner test output
global.console = {
  ...originalConsole,
  log: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
  trace: jest.fn(),
};

// Restore console for specific tests if needed
global.restoreConsole = () => {
  global.console = originalConsole;
};

// ------------ GLOBAL MOCKS
// Mock Discord.js client
global.mockDiscordClient = {
  user: {
    id: 'test-bot-id',
    tag: 'TestBot#1234',
    displayAvatarURL: jest.fn(() => 'https://example.com/avatar.png'),
    setPresence: jest.fn(),
  },
  guilds: {
    cache: new Map(),
    fetch: jest.fn(),
  },
  commands: new Map(),
  login: jest.fn(),
  destroy: jest.fn(),
  once: jest.fn(),
  on: jest.fn(),
  rest: {
    setToken: jest.fn(),
  },
};

// Mock database connection
global.mockDatabase = {
  query: jest.fn(),
  saveUser: jest.fn(),
  getUserById: jest.fn(),
  updateUserOnboardingStatus: jest.fn(),
  getAllOnboardingProgress: jest.fn(),
  close: jest.fn(),
};

// ------------ TEST UTILITIES
// Create mock interaction
global.createMockInteraction = (overrides = {}) => ({
  user: {
    id: 'test-user-id',
    tag: 'TestUser#1234',
    username: 'TestUser',
    displayName: 'Test User',
    ...overrides.user,
  },
  member: {
    id: 'test-user-id',
    user: {
      id: 'test-user-id',
      tag: 'TestUser#1234',
      username: 'TestUser',
      displayName: 'Test User',
    },
    roles: {
      cache: new Map(),
    },
    permissions: {
      has: jest.fn(() => true),
    },
    createDM: jest.fn(() => Promise.resolve(global.createMockDMChannel())),
    ...overrides.member,
  },
  guild: {
    id: 'test-guild-id',
    name: 'Test Guild',
    members: {
      cache: new Map(),
      fetch: jest.fn(),
    },
    ...overrides.guild,
  },
  channel: {
    id: 'test-channel-id',
    name: 'test-channel',
    send: jest.fn(),
    ...overrides.channel,
  },
  channelId: 'test-channel-id',
  guildId: 'test-guild-id',
  commandName: 'test-command',
  options: {
    getString: jest.fn(),
    getUser: jest.fn(),
    getRole: jest.fn(),
    getInteger: jest.fn(),
    getBoolean: jest.fn(),
    ...overrides.options,
  },
  reply: jest.fn(),
  editReply: jest.fn(),
  followUp: jest.fn(),
  deferReply: jest.fn(),
  deferred: false,
  replied: false,
  ...overrides,
});

// Create mock DM channel
global.createMockDMChannel = (overrides = {}) => ({
  id: 'test-dm-channel-id',
  type: 1, // DM channel type
  send: jest.fn(),
  ...overrides,
});

// Create mock message
global.createMockMessage = (overrides = {}) => ({
  id: 'test-message-id',
  content: 'Test message content',
  author: {
    id: 'test-user-id',
    tag: 'TestUser#1234',
    bot: false,
    send: jest.fn(),
    createDM: jest.fn(() => Promise.resolve(global.createMockDMChannel())),
    ...overrides.author,
  },
  channel: global.createMockDMChannel(),
  guild: null,
  createdTimestamp: Date.now(),
  ...overrides,
});

// ------------ CLEANUP
// Reset all mocks after each test
afterEach(() => {
  jest.clearAllMocks();
});
