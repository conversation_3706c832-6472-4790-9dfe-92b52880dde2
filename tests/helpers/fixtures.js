// @ts-check
/**
 * @file FIXTURES.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * Test fixtures and sample data for testing Discord bot functionality.
 * Provides consistent test data across different test suites.
 */

// ------------ USER FIXTURES

/**
 * Sample user data for testing
 */
export const userFixtures = {
  validUser: {
    id: '123456789012345678',
    tag: 'TestUser#1234',
    username: 'TestUser',
    displayName: 'Test User',
    email: '<EMAIL>',
    nickname: 'Tester',
    robloxUsername: 'TestRobloxUser',
    verified: true,
    joinedAt: new Date('2024-01-01T00:00:00Z'),
    verifiedAt: new Date('2024-01-01T01:00:00Z'),
    onboardingStartTime: new Date('2024-01-01T00:30:00Z'),
    metadata: { source: 'test' },
    onboardingStatus: 'completed',
    lastInteraction: new Date('2024-01-01T02:00:00Z'),
    timeZone: 'UTC',
  },

  unverifiedUser: {
    id: '123456789012345679',
    tag: 'UnverifiedUser#5678',
    username: 'UnverifiedUser',
    displayName: 'Unverified User',
    email: null,
    nickname: null,
    robloxUsername: null,
    verified: false,
    joinedAt: new Date('2024-01-02T00:00:00Z'),
    verifiedAt: null,
    onboardingStartTime: new Date('2024-01-02T00:30:00Z'),
    metadata: {},
    onboardingStatus: 'pending',
    lastInteraction: new Date('2024-01-02T00:30:00Z'),
    timeZone: null,
  },

  adminUser: {
    id: '123456789012345680',
    tag: 'AdminUser#9999',
    username: 'AdminUser',
    displayName: 'Admin User',
    email: '<EMAIL>',
    nickname: 'Admin',
    robloxUsername: 'AdminRobloxUser',
    verified: true,
    joinedAt: new Date('2023-12-01T00:00:00Z'),
    verifiedAt: new Date('2023-12-01T01:00:00Z'),
    onboardingStartTime: new Date('2023-12-01T00:30:00Z'),
    metadata: { role: 'admin' },
    onboardingStatus: 'completed',
    lastInteraction: new Date('2024-01-01T12:00:00Z'),
    timeZone: 'UTC',
  },

  hrUser: {
    id: '123456789012345681',
    tag: 'HRUser#8888',
    username: 'HRUser',
    displayName: 'HR User',
    email: '<EMAIL>',
    nickname: 'HR',
    robloxUsername: 'HRRobloxUser',
    verified: true,
    joinedAt: new Date('2023-12-15T00:00:00Z'),
    verifiedAt: new Date('2023-12-15T01:00:00Z'),
    onboardingStartTime: new Date('2023-12-15T00:30:00Z'),
    metadata: { role: 'hr' },
    onboardingStatus: 'completed',
    lastInteraction: new Date('2024-01-01T10:00:00Z'),
    timeZone: 'UTC',
  },
};

// ------------ MESSAGE FIXTURES

/**
 * Sample message data for testing
 */
export const messageFixtures = {
  incomingMessage: {
    id: 'msg-123456789012345678',
    userId: '123456789012345678',
    content: 'Hello, this is a test message from user',
    type: 'incoming',
    authorId: undefined,
    timestamp: new Date('2024-01-01T12:00:00Z'),
    metadata: { source: 'dm' },
  },

  outgoingMessage: {
    id: 'msg-123456789012345679',
    userId: '123456789012345678',
    content: 'Hello, this is a response from the bot',
    type: 'outgoing',
    authorId: 'bot-id',
    timestamp: new Date('2024-01-01T12:01:00Z'),
    metadata: { command: 'message' },
  },

  broadcastMessage: {
    id: 'msg-123456789012345680',
    userId: 'broadcast',
    content: 'This is a broadcast message to all users',
    type: 'outgoing',
    authorId: '123456789012345680',
    timestamp: new Date('2024-01-01T15:00:00Z'),
    metadata: {
      command: 'broadcast',
      role: 'verified',
      title: 'Important Announcement',
    },
  },
};

// ------------ ROLE FIXTURES

/**
 * Sample role data for testing
 */
export const roleFixtures = {
  adminRole: {
    id: '1234567890123456789',
    name: 'Admin',
    color: 0xff0000,
    hoist: true,
    position: 10,
    permissions: { has: () => true },
    mentionable: false,
    managed: false,
  },

  hrRole: {
    id: '1234567890123456790',
    name: 'HR',
    color: 0x00ff00,
    hoist: true,
    position: 9,
    permissions: { has: () => true },
    mentionable: true,
    managed: false,
  },

  verifiedRole: {
    id: '1234567890123456791',
    name: 'Verified',
    color: 0x0099ff,
    hoist: false,
    position: 5,
    permissions: { has: () => false },
    mentionable: true,
    managed: false,
  },

  memberRole: {
    id: '1234567890123456792',
    name: '@everyone',
    color: 0x000000,
    hoist: false,
    position: 0,
    permissions: { has: () => false },
    mentionable: false,
    managed: false,
  },
};

// ------------ CHANNEL FIXTURES

/**
 * Sample channel data for testing
 */
export const channelFixtures = {
  welcomeChannel: {
    id: '1234567890123456792',
    name: 'welcome',
    type: 0, // Text channel
    topic: 'Welcome new members!',
    nsfw: false,
    position: 1,
  },

  adminLogChannel: {
    id: '1234567890123456793',
    name: 'admin-log',
    type: 0, // Text channel
    topic: 'Admin action logs',
    nsfw: false,
    position: 2,
  },

  errorLogChannel: {
    id: '1234567890123456794',
    name: 'error-log',
    type: 0, // Text channel
    topic: 'Error logs and debugging',
    nsfw: false,
    position: 3,
  },

  generalChannel: {
    id: '1234567890123456795',
    name: 'general',
    type: 0, // Text channel
    topic: 'General discussion',
    nsfw: false,
    position: 0,
  },
};

// ------------ COMMAND FIXTURES

/**
 * Sample command interaction data for testing
 */
export const commandFixtures = {
  pingCommand: {
    commandName: 'ping',
    options: {},
    user: userFixtures.validUser,
  },

  helpCommand: {
    commandName: 'help',
    options: {},
    user: userFixtures.validUser,
  },

  registerCommand: {
    commandName: 'register',
    options: {},
    user: userFixtures.unverifiedUser,
  },

  broadcastCommand: {
    commandName: 'broadcast',
    options: {
      message: 'Test broadcast message',
      role: roleFixtures.verifiedRole,
      title: 'Test Announcement',
      author: 'Test Admin',
      footer: 'Test Footer',
      timestamp: true,
      color: '#ccf3ff',
    },
    user: userFixtures.adminUser,
  },

  messageCommand: {
    commandName: 'message',
    options: {
      user: userFixtures.validUser,
      message: 'Test direct message',
    },
    user: userFixtures.adminUser,
  },

  viewMessagesCommand: {
    commandName: 'viewmessages',
    options: {
      user: userFixtures.validUser,
      count: 10,
    },
    user: userFixtures.hrUser,
  },

  manageEmployeeCommand: {
    commandName: 'manageemployee',
    options: {
      user: userFixtures.validUser,
    },
    user: userFixtures.hrUser,
  },
};

// ------------ ONBOARDING FIXTURES

/**
 * Sample onboarding flow data for testing
 */
export const onboardingFixtures = {
  basicFlow: [
    {
      field: 'email',
      prompt: 'Please provide your email address:',
      validation: 'email',
      required: true,
      nextStep: 1,
    },
    {
      field: 'nickname',
      prompt: 'What would you like to be called?',
      validation: 'string',
      required: true,
      nextStep: 2,
    },
    {
      field: 'robloxUsername',
      prompt: 'What is your Roblox username? (Type "skip" to skip)',
      validation: 'string',
      required: false,
      skipStep: 3,
      nextStep: 3,
    },
  ],

  onboardingSession: {
    userId: '123456789012345678',
    stepIndex: 0,
    data: {},
    startTime: Date.now(),
    lastActivity: Date.now(),
    dmConfirmed: true,
    flow: null, // Will be set to basicFlow in tests
  },

  completedSession: {
    userId: '123456789012345678',
    stepIndex: 3,
    data: {
      email: '<EMAIL>',
      nickname: 'Tester',
      robloxUsername: 'TestRobloxUser',
    },
    startTime: Date.now() - 300000, // 5 minutes ago
    lastActivity: Date.now(),
    dmConfirmed: true,
    flow: null, // Will be set to basicFlow in tests
  },
};

// ------------ ERROR FIXTURES

/**
 * Sample error data for testing
 */
export const errorFixtures = {
  validationError: {
    name: 'ValidationError',
    message: 'Invalid input provided',
    field: 'email',
  },

  networkError: {
    name: 'NetworkError',
    message: 'Network connection failed',
    status: 500,
    response: { error: 'Internal Server Error' },
  },

  authenticationError: {
    name: 'AuthenticationError',
    message: 'Unauthorized access',
  },

  discordApiError: {
    name: 'DiscordAPIError',
    message: 'Cannot send messages to this user',
    code: 50007,
    status: 403,
  },
};

// ------------ DATABASE FIXTURES

/**
 * Sample database query results for testing
 */
export const databaseFixtures = {
  userQueryResult: [
    {
      id_dis_internal_workers: '123456789012345678',
      tag: 'TestUser#1234',
      email: 'encrypted_email_data',
      nickname: 'Tester',
      robloxUsername: 'TestRobloxUser',
      timeZone: 'UTC',
      verified: 1,
      joinedAt: new Date('2024-01-01T00:00:00Z'),
      verifiedAt: new Date('2024-01-01T01:00:00Z'),
      onboardingStartTime: new Date('2024-01-01T00:30:00Z'),
      metadata: '{"source":"test"}',
      onboardingStatus: 'completed',
      lastInteraction: new Date('2024-01-01T02:00:00Z'),
    },
  ],

  onboardingProgressResult: [
    {
      id_dis_internal_workers: '123456789012345678',
      tag: 'TestUser#1234',
      onboardingStatus: 'completed',
      verified: 1,
      onboardingStartTime: new Date('2024-01-01T00:30:00Z'),
      verifiedAt: new Date('2024-01-01T01:00:00Z'),
    },
    {
      id_dis_internal_workers: '123456789012345679',
      tag: 'UnverifiedUser#5678',
      onboardingStatus: 'pending',
      verified: 0,
      onboardingStartTime: new Date('2024-01-02T00:30:00Z'),
      verifiedAt: null,
    },
  ],
};
