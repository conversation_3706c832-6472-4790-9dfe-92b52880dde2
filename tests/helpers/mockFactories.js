// @ts-check
/**
 * @file MOCK-FACTORIES.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * Mock factories for Discord.js components and other external dependencies.
 * Provides reusable mock objects for testing Discord bot functionality.
 */

// ------------ IMPORTS
import { jest } from '@jest/globals';
import { Collection } from 'discord.js';

// ------------ DISCORD MOCK FACTORIES

/**
 * Create a mock Discord.js Client
 * @param {Object} overrides - Properties to override
 * @returns {Object} Mock Discord client
 */
export function createMockClient(overrides = {}) {
  return {
    user: {
      id: 'test-bot-id',
      tag: 'TestBot#1234',
      username: 'TestBot',
      displayAvatarURL: jest.fn(() => 'https://example.com/avatar.png'),
      setPresence: jest.fn(),
      ...overrides.user,
    },
    guilds: {
      cache: new Collection(),
      fetch: jest.fn(),
      ...overrides.guilds,
    },
    commands: new Collection(),
    login: jest.fn(() => Promise.resolve()),
    destroy: jest.fn(() => Promise.resolve()),
    once: jest.fn(),
    on: jest.fn(),
    rest: {
      setToken: jest.fn(),
      ...overrides.rest,
    },
    ws: {
      ping: 50, // Default ping value
      ...overrides.ws,
    },
    ...overrides,
  };
}

/**
 * Create a mock Discord.js Guild
 * @param {Object} overrides - Properties to override
 * @returns {Object} Mock Discord guild
 */
export function createMockGuild(overrides = {}) {
  return {
    id: 'test-guild-id',
    name: 'Test Guild',
    members: {
      cache: new Collection(),
      fetch: jest.fn(() => Promise.resolve()),
      ...overrides.members,
    },
    roles: {
      cache: new Collection(),
      fetch: jest.fn(() => Promise.resolve()),
      ...overrides.roles,
    },
    channels: {
      cache: new Collection(),
      fetch: jest.fn(() => Promise.resolve()),
      ...overrides.channels,
    },
    ...overrides,
  };
}

/**
 * Create a mock Discord.js GuildMember
 * @param {Object} overrides - Properties to override
 * @returns {Object} Mock Discord guild member
 */
export function createMockGuildMember(overrides = {}) {
  const userId = overrides.id || 'test-user-id';
  const userTag = overrides.tag || 'TestUser#1234';

  return {
    id: userId,
    user: {
      id: userId,
      tag: userTag,
      username: userTag.split('#')[0],
      discriminator: userTag.split('#')[1],
      bot: false,
      displayAvatarURL: jest.fn(() => 'https://example.com/user-avatar.png'),
      createDM: jest.fn(() => Promise.resolve(createMockDMChannel())),
      send: jest.fn(() => Promise.resolve(createMockMessage())),
      ...overrides.user,
    },
    displayName: overrides.displayName || userTag.split('#')[0],
    nickname: overrides.nickname || null,
    roles: {
      cache: new Collection(),
      add: jest.fn(() => Promise.resolve()),
      remove: jest.fn(() => Promise.resolve()),
      has: jest.fn(() => false),
      ...overrides.roles,
    },
    permissions: {
      has: jest.fn(() => true),
      ...overrides.permissions,
    },
    joinedAt: new Date(),
    createDM: jest.fn(() => Promise.resolve(createMockDMChannel())),
    send: jest.fn(() => Promise.resolve(createMockMessage())),
    ...overrides,
  };
}

/**
 * Create a mock Discord.js User
 * @param {Object} overrides - Properties to override
 * @returns {Object} Mock Discord user
 */
export function createMockUser(overrides = {}) {
  const userId = overrides.id || 'test-user-id';
  const userTag = overrides.tag || 'TestUser#1234';

  return {
    id: userId,
    tag: userTag,
    username: userTag.split('#')[0],
    discriminator: userTag.split('#')[1],
    bot: false,
    displayAvatarURL: jest.fn(() => 'https://example.com/user-avatar.png'),
    createDM: jest.fn(() => Promise.resolve(createMockDMChannel())),
    send: jest.fn(() => Promise.resolve(createMockMessage())),
    ...overrides,
  };
}

/**
 * Create a mock Discord.js Role
 * @param {Object} overrides - Properties to override
 * @returns {Object} Mock Discord role
 */
export function createMockRole(overrides = {}) {
  return {
    id: 'test-role-id',
    name: 'Test Role',
    color: 0x99aab5,
    hoist: false,
    position: 1,
    permissions: {
      has: jest.fn(() => true),
      ...overrides.permissions,
    },
    mentionable: true,
    managed: false,
    ...overrides,
  };
}

/**
 * Create a mock Discord.js Channel
 * @param {Object} overrides - Properties to override
 * @returns {Object} Mock Discord channel
 */
export function createMockChannel(overrides = {}) {
  return {
    id: 'test-channel-id',
    name: 'test-channel',
    type: 0, // Text channel
    send: jest.fn(() => Promise.resolve(createMockMessage())),
    ...overrides,
  };
}

/**
 * Create a mock Discord.js DM Channel
 * @param {Object} overrides - Properties to override
 * @returns {Object} Mock Discord DM channel
 */
export function createMockDMChannel(overrides = {}) {
  return {
    id: 'test-dm-channel-id',
    type: 1, // DM channel
    send: jest.fn(() => Promise.resolve(createMockMessage())),
    recipient: createMockUser(),
    ...overrides,
  };
}

/**
 * Create a mock Discord.js Message
 * @param {Object} overrides - Properties to override
 * @returns {Object} Mock Discord message
 */
export function createMockMessage(overrides = {}) {
  return {
    id: 'test-message-id',
    content: 'Test message content',
    author: createMockUser(overrides.author),
    channel: createMockChannel(overrides.channel),
    guild: null,
    createdTimestamp: Date.now(),
    edit: jest.fn(() => Promise.resolve()),
    delete: jest.fn(() => Promise.resolve()),
    reply: jest.fn(() => Promise.resolve(createMockMessage())),
    ...overrides,
  };
}

/**
 * Create a mock Discord.js CommandInteraction
 * @param {Object} overrides - Properties to override
 * @returns {Object} Mock Discord command interaction
 */
export function createMockCommandInteraction(overrides = {}) {
  return {
    id: 'test-interaction-id',
    type: 2, // Application command
    user: createMockUser(overrides.user),
    member: createMockGuildMember(overrides.member),
    guild: createMockGuild(overrides.guild),
    channel: createMockChannel(overrides.channel),
    channelId: 'test-channel-id',
    guildId: 'test-guild-id',
    commandName: 'test-command',
    options: {
      getString: jest.fn(),
      getUser: jest.fn(),
      getRole: jest.fn(),
      getInteger: jest.fn(),
      getBoolean: jest.fn(),
      getChannel: jest.fn(),
      getMentionable: jest.fn(),
      getNumber: jest.fn(),
      getAttachment: jest.fn(),
      getSubcommand: jest.fn(),
      getSubcommandGroup: jest.fn(),
      ...overrides.options,
    },
    reply: jest.fn(() => Promise.resolve()),
    editReply: jest.fn(() => Promise.resolve()),
    followUp: jest.fn(() => Promise.resolve()),
    deferReply: jest.fn(() => Promise.resolve()),
    deferred: false,
    replied: false,
    ephemeral: false,
    createdTimestamp: Date.now(),
    ...overrides,
  };
}

// ------------ DATABASE MOCK FACTORIES

/**
 * Create a mock database instance
 * @param {Object} overrides - Properties to override
 * @returns {Object} Mock database
 */
export function createMockDatabase(overrides = {}) {
  return {
    query: jest.fn(() => Promise.resolve([])),
    saveUser: jest.fn(() => Promise.resolve()),
    getUserById: jest.fn(() => Promise.resolve(null)),
    updateUserOnboardingStatus: jest.fn(() => Promise.resolve()),
    getAllOnboardingProgress: jest.fn(() => Promise.resolve([])),
    close: jest.fn(() => Promise.resolve()),
    initializePool: jest.fn(() => Promise.resolve()),
    ...overrides,
  };
}

// ------------ UTILITY MOCK FACTORIES

/**
 * Create a mock logger instance
 * @param {Object} overrides - Properties to override
 * @returns {Object} Mock logger
 */
export function createMockLogger(overrides = {}) {
  return {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    trace: jest.fn(),
    fatal: jest.fn(),
    child: jest.fn(() => createMockLogger()),
    flush: jest.fn(),
    ...overrides,
  };
}
