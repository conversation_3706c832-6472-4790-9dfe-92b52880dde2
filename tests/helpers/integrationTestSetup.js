// @ts-check
/**
 * @file INTEGRATION-TEST-SETUP.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Integration test setup helpers for DIS Discord Bot.
 * Provides database setup, Discord API mocking, and test data management.
 */

// ------------ IMPORTS
import { Database } from '../../src/utils/Database.js';

// ------------ TEST DATABASE SETUP

/**
 * Integration test database manager
 */
export class IntegrationTestDatabase {
  constructor() {
    this.database = null;
    this.testDataIds = new Set();
  }

  /**
   * Initialize test database connection
   * @returns {Promise<void>}
   */
  async setup() {
    // Set test environment variables
    process.env.NODE_ENV = 'test';
    
    // Initialize database connection
    this.database = new Database();
    await this.database.connect();
  }

  /**
   * Clean up test database and close connection
   * @returns {Promise<void>}
   */
  async teardown() {
    // Clean up all test data
    await this.cleanupTestData();
    
    // Close database connection
    if (this.database) {
      await this.database.disconnect();
      this.database = null;
    }
  }

  /**
   * Register test data ID for cleanup
   * @param {string} id - Test data ID
   */
  registerTestData(id) {
    this.testDataIds.add(id);
  }

  /**
   * Clean up all registered test data
   * @returns {Promise<void>}
   */
  async cleanupTestData() {
    if (!this.database || this.testDataIds.size === 0) {
      return;
    }

    try {
      // Clean up users
      for (const id of this.testDataIds) {
        await this.database.query(
          'DELETE FROM dis_internal_workers WHERE id_dis_internal_workers = ?',
          [id]
        );
      }
      
      // Clean up messages (if any test data exists)
      await this.database.query(
        'DELETE FROM dis_messages WHERE user_id IN (?)',
        [Array.from(this.testDataIds)]
      );
    } catch (error) {
      // Ignore cleanup errors in tests
      console.warn('Test cleanup warning:', error.message);
    }

    this.testDataIds.clear();
  }

  /**
   * Generate unique test ID
   * @param {string} prefix - ID prefix
   * @returns {string} Unique test ID
   */
  generateTestId(prefix = 'test') {
    const id = `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.registerTestData(id);
    return id;
  }

  /**
   * Get database instance
   * @returns {Database} Database instance
   */
  getDatabase() {
    return this.database;
  }
}

// ------------ DISCORD API MOCKING

/**
 * Create mock Discord interaction for testing
 * @param {Object} options - Mock options
 * @returns {Object} Mock interaction
 */
export function createMockInteraction(options = {}) {
  const {
    commandName = 'test',
    userId = 'test-user-123',
    guildId = 'test-guild-123',
    channelId = 'test-channel-123',
    userRoles = [],
    isAdmin = false,
    options: commandOptions = []
  } = options;

  const mockUser = {
    id: userId,
    username: 'TestUser',
    discriminator: '1234',
    tag: 'TestUser#1234',
    bot: false
  };

  const mockMember = {
    id: userId,
    user: mockUser,
    roles: {
      cache: new Map(userRoles.map(role => [role.id, role])),
      has: jest.fn((roleId) => userRoles.some(role => role.id === roleId))
    },
    permissions: {
      has: jest.fn(() => isAdmin)
    }
  };

  const mockGuild = {
    id: guildId,
    name: 'Test Guild',
    members: {
      cache: new Map([[userId, mockMember]])
    }
  };

  const mockChannel = {
    id: channelId,
    name: 'test-channel',
    type: 0, // TEXT_CHANNEL
    send: jest.fn().mockResolvedValue({ id: 'message-123' })
  };

  return {
    commandName,
    user: mockUser,
    member: mockMember,
    guild: mockGuild,
    channel: mockChannel,
    channelId,
    guildId,
    options: {
      get: jest.fn((name) => {
        const option = commandOptions.find(opt => opt.name === name);
        return option ? { value: option.value } : null;
      }),
      getString: jest.fn((name) => {
        const option = commandOptions.find(opt => opt.name === name);
        return option ? option.value : null;
      }),
      getUser: jest.fn((name) => {
        const option = commandOptions.find(opt => opt.name === name);
        return option ? option.value : null;
      }),
      getChannel: jest.fn((name) => {
        const option = commandOptions.find(opt => opt.name === name);
        return option ? option.value : null;
      })
    },
    reply: jest.fn().mockResolvedValue({ id: 'reply-123' }),
    editReply: jest.fn().mockResolvedValue({ id: 'reply-123' }),
    followUp: jest.fn().mockResolvedValue({ id: 'followup-123' }),
    deferReply: jest.fn().mockResolvedValue(),
    deleteReply: jest.fn().mockResolvedValue(),
    replied: false,
    deferred: false,
    ephemeral: false
  };
}

/**
 * Create mock Discord role
 * @param {Object} options - Role options
 * @returns {Object} Mock role
 */
export function createMockRole(options = {}) {
  const {
    id = 'role-123',
    name = 'Test Role',
    permissions = [],
    color = 0,
    position = 1
  } = options;

  return {
    id,
    name,
    permissions,
    color,
    position,
    mentionable: true,
    managed: false
  };
}

// ------------ TEST DATA FACTORIES

/**
 * Create test user data
 * @param {Object} overrides - Data overrides
 * @returns {Object} Test user data
 */
export function createTestUserData(overrides = {}) {
  return {
    id: `test_user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    tag: 'TestUser#1234',
    email: '<EMAIL>',
    nickname: 'Test Nickname',
    robloxUsername: 'TestRobloxUser',
    verified: false,
    timeZone: 'UTC',
    onboardingStatus: 'pending',
    ...overrides
  };
}

/**
 * Create test message data
 * @param {Object} overrides - Data overrides
 * @returns {Object} Test message data
 */
export function createTestMessageData(overrides = {}) {
  return {
    id: `test_message_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    user_id: 'test-user-123',
    channel_id: 'test-channel-123',
    guild_id: 'test-guild-123',
    content: 'Test message content',
    type: 'text',
    status: 'sent',
    ...overrides
  };
}

// ------------ ENVIRONMENT SETUP

/**
 * Setup test environment variables
 */
export function setupTestEnvironment() {
  // Set required environment variables for testing
  process.env.NODE_ENV = 'test';
  process.env.USER_DATA_ENCRYPTION_KEY = 'test-encryption-key-32-chars-lo!';
  process.env.DB_HOST = process.env.TEST_DB_HOST || 'localhost';
  process.env.DB_USER = process.env.TEST_DB_USER || 'test';
  process.env.DB_PASSWORD = process.env.TEST_DB_PASSWORD || 'test';
  process.env.DB_NAME = process.env.TEST_DB_NAME || 'dis_bot_test';
  
  // Disable logging in tests
  process.env.LOG_LEVEL = 'silent';
}

/**
 * Global integration test setup
 * @returns {Promise<IntegrationTestDatabase>} Test database instance
 */
export async function globalIntegrationSetup() {
  setupTestEnvironment();
  
  const testDb = new IntegrationTestDatabase();
  await testDb.setup();
  
  return testDb;
}

/**
 * Global integration test teardown
 * @param {IntegrationTestDatabase} testDb - Test database instance
 * @returns {Promise<void>}
 */
export async function globalIntegrationTeardown(testDb) {
  if (testDb) {
    await testDb.teardown();
  }
}
