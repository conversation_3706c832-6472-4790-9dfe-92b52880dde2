// @ts-check
/**
 * @file COMMAND MOCKS.JS
 *
 * @version 1.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 *
 * @description
 * Centralized mock setup for command tests.
 * Provides consistent mocking for all command dependencies.
 */

import { jest } from '@jest/globals';

// ------------ RATE LIMIT MOCKS
export const mockRateLimit = {
  checkRateLimit: jest.fn(() => ({ limited: false, resetTime: 0 })),
  getRateLimitStatus: jest.fn(() => ({ remaining: 10, count: 0 })),
};

// ------------ COOLDOWN MOCKS
export const mockCooldown = {
  check: jest.fn(() => true),
  set: jest.fn(),
  getCooldownMs: jest.fn(() => 5000),
  getRemaining: jest.fn(() => 0),
  isExpired: jest.fn(() => true),
};

// ------------ ERROR HANDLER MOCKS
export const mockErrorHandler = {
  handleError: jest.fn(),
};

// ------------ REPLY HELPERS MOCKS
export const mockReplyHelpers = {
  safeReply: jest.fn((interaction, options) => {
    if (interaction.reply) {
      return interaction.reply(options);
    }
    return Promise.resolve();
  }),
};

// ------------ COMMAND HANDLER MOCKS
export const mockCommandHandler = {
  CommandHandler: {
    standard: jest.fn((commandName, executeFn, options) => executeFn),
    admin: jest.fn((commandName, executeFn) => executeFn),
    hr: jest.fn((commandName, executeFn) => executeFn),
  },
};

// ------------ CONFIG MOCKS
export const mockConfig = {
  botConfig: {
    colors: {
      primary: '#ccf3ff',
      success: '#43B581',
      error: '#F04747',
      warning: '#FAA61A',
    },
    version: '3.1.1',
    roles: {
      admin: '1234567890123456789',
      hr: '1234567890123456790',
      verified: '1234567890123456791',
    },
    channels: {
      welcome: '1234567890123456792',
      adminLog: '1234567890123456793',
      errorLog: '1234567890123456794',
      general: '1234567890123456795',
    },
    cooldowns: {
      commands: 3000,
      commandCooldowns: {
        ping: 10000,
        help: 5000,
        broadcast: 600000,
      },
    },
  },
};

// ------------ HELP UTILITY MOCKS
export const mockHelpUtility = {
  getHelpEmbedForMember: jest.fn(() => ({
    setTitle: jest.fn().mockReturnThis(),
    setDescription: jest.fn().mockReturnThis(),
    addFields: jest.fn().mockReturnThis(),
    setColor: jest.fn().mockReturnThis(),
    setTimestamp: jest.fn().mockReturnThis(),
    setThumbnail: jest.fn().mockReturnThis(),
    data: {
      title: 'Available Commands',
      description: 'Here are the commands you can use:',
      fields: [
        { name: '/ping', value: 'Check bot latency', inline: true },
        { name: '/help', value: 'Show available commands', inline: true },
      ],
    },
  })),
};

// ------------ MESSAGES MOCKS
export const mockMessages = {
  messages: {
    member: {
      helpEmbed: {
        title: 'Available Commands',
        description: 'Here are the commands you can use:',
        color: '#ccf3ff',
        fields: [
          { name: '/ping', value: 'Check bot latency', inline: true },
          { name: '/help', value: 'Show available commands', inline: true },
        ],
      },
    },
    admin: {
      helpEmbed: {
        title: 'Admin Commands',
        description: 'Admin commands available:',
        color: '#ff0000',
        fields: [
          { name: '/broadcast', value: 'Send message to all users', inline: true },
        ],
      },
    },
    hr: {
      helpEmbed: {
        title: 'HR Commands',
        description: 'HR commands available:',
        color: '#00ff00',
        fields: [
          { name: '/manageemployee', value: 'Manage employee data', inline: true },
        ],
      },
    },
    errors: {
      internalError: '⚠️ **System Error!** Please contact Founder & CEO immediately.',
    },
  },
};

// ------------ VALIDATORS MOCKS
export const mockValidators = {
  hasAdminPermission: jest.fn(() => true),
  hasHumanResourcesPermission: jest.fn(() => true),
  isValidEmail: jest.fn(() => true),
  isValidDiscordId: jest.fn(() => true),
  isValidRobloxUsername: jest.fn(() => true),
  sanitizeInput: jest.fn((input) => input),
  sanitizeNickname: jest.fn((input) => input),
  sanitizeTimeZone: jest.fn((input) => input),
  parseYesNo: jest.fn(() => true),
};

// ------------ SETUP FUNCTION FOR COMMAND TESTS
export function setupCommandMocks() {
  // Clear all mocks before each test
  jest.clearAllMocks();

  // Reset mock implementations
  mockRateLimit.checkRateLimit.mockReturnValue({ limited: false, resetTime: 0 });
  mockRateLimit.getRateLimitStatus.mockReturnValue({ remaining: 10, count: 0 });

  mockCooldown.check.mockReturnValue(true);
  mockCooldown.isExpired.mockReturnValue(true);
  mockCooldown.getCooldownMs.mockReturnValue(5000);
  mockCooldown.getRemaining.mockReturnValue(0);

  mockReplyHelpers.safeReply.mockImplementation((interaction, options) => {
    if (interaction.reply) {
      return interaction.reply(options);
    }
    return Promise.resolve();
  });

  mockCommandHandler.CommandHandler.standard.mockImplementation((commandName, executeFn, options) => executeFn);
  mockCommandHandler.CommandHandler.admin.mockImplementation((commandName, executeFn) => executeFn);
  mockCommandHandler.CommandHandler.hr.mockImplementation((commandName, executeFn) => executeFn);

  mockHelpUtility.getHelpEmbedForMember.mockReturnValue({
    setTitle: jest.fn().mockReturnThis(),
    setDescription: jest.fn().mockReturnThis(),
    addFields: jest.fn().mockReturnThis(),
    setColor: jest.fn().mockReturnThis(),
    setTimestamp: jest.fn().mockReturnThis(),
    setThumbnail: jest.fn().mockReturnThis(),
    data: {
      title: 'Available Commands',
      description: 'Here are the commands you can use:',
      fields: [
        { name: '/ping', value: 'Check bot latency', inline: true },
        { name: '/help', value: 'Show available commands', inline: true },
      ],
    },
  });

  mockValidators.hasAdminPermission.mockReturnValue(true);
  mockValidators.hasHumanResourcesPermission.mockReturnValue(true);
  mockValidators.isValidEmail.mockReturnValue(true);
  mockValidators.isValidDiscordId.mockReturnValue(true);
  mockValidators.isValidRobloxUsername.mockReturnValue(true);
  mockValidators.sanitizeInput.mockImplementation((input) => input);
  mockValidators.sanitizeNickname.mockImplementation((input) => input);
  mockValidators.sanitizeTimeZone.mockImplementation((input) => input);
  mockValidators.parseYesNo.mockReturnValue(true);
}

// ------------ MOCK MODULE FACTORY
export function createMockModule(moduleName, mockObject) {
  return jest.doMock(moduleName, () => mockObject, { virtual: true });
}

// ------------ EXPORTS
export default {
  mockRateLimit,
  mockCooldown,
  mockErrorHandler,
  mockReplyHelpers,
  mockCommandHandler,
  mockConfig,
  mockHelpUtility,
  mockMessages,
  mockValidators,
  setupCommandMocks,
  createMockModule,
};
