// @ts-check
/**
 * @file JEST.CONFIG.JS
 *
 * @version 2.0.0
 * <AUTHOR>
 *
 * @copyright Dynamic Innovative Studio
 * @license Commercial
 *
 * @description
 * Jest configuration for DIS Discord Bot.
 * Configures ES modules support, coverage, and testing environment.
 */

// ------------ CONFIGURATION
export default {
  // Test environment
  testEnvironment: 'node',

  // ES Modules support
  preset: null,
  transform: {},
  transformIgnorePatterns: ['node_modules/(?!(.*\\.mjs$))'],

  // Module resolution - simplified for ES modules
  moduleNameMapper: {},

  // Module directories
  moduleDirectories: ['node_modules', 'src'],

  // Module file extensions
  moduleFileExtensions: ['js', 'json'],

  // Resolver configuration
  resolver: undefined,

  // Test file patterns
  testMatch: [
    '**/tests/**/*.test.js',
    '**/tests/**/*.spec.js',
    '**/__tests__/**/*.js',
  ],

  // Test file ignore patterns
  testPathIgnorePatterns: ['/node_modules/', '/dist/', '/coverage/', '/logs/'],

  // Coverage configuration
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/deploy-commands.js',
    '!src/config/*.js',
    '!src/**/*.config.js',
    '!src/**/*.test.js',
    '!src/**/*.spec.js',
  ],

  // Coverage output
  coverageDirectory: 'coverage',
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov',
    'clover',
    'json',
    'json-summary',
  ],

  // Coverage thresholds
  coverageThreshold: {
    global: {
      branches: 60,
      functions: 70,
      lines: 75,
      statements: 75,
    },
    './src/commands/': {
      branches: 70,
      functions: 80,
      lines: 85,
      statements: 85,
    },
    './src/models/': {
      branches: 80,
      functions: 90,
      lines: 90,
      statements: 90,
    },
    './src/utils/': {
      branches: 75,
      functions: 85,
      lines: 85,
      statements: 85,
    },
  },

  // Setup files
  setupFilesAfterEnv: ['<rootDir>/tests/helpers/testSetup.js'],

  // Test timeout
  testTimeout: 10000,

  // Verbose output
  verbose: true,

  // Clear mocks between tests
  clearMocks: true,

  // Restore mocks after each test
  restoreMocks: true,

  // Error handling
  errorOnDeprecated: true,

  // Watch mode configuration
  watchPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/coverage/',
    '/logs/',
    '/.git/',
  ],
};
